import { http } from './http';
import { Channels } from '@/common/constants';
import {
    CreateBankAccountData,
    BankAccount,
    PaginatedTransactions,
    ReconciliationResult,
    BankStatement,
    GetTransactionHistoryParams,
    GetBankStatementParams
} from '@/common/types';

export const createBankAccount = async (data: CreateBankAccountData) => {
    return await http.post(Channels.CREATE_BANK_ACCOUNT, { body: data });
};

export const deleteBankAccount = async (id: string) => {
    return await http.delete(Channels.DELETE_BANK_ACCOUNT, {
        params: { id }
    });
};

export const getAllBankAccounts = async () => {
    return await http.get(Channels.GET_ALL_BANK_ACCOUNTS);
};

export const getBankAccountsForSelect = async () => {
    return await http.get(Channels.GET_BANK_ACCOUNTS_FOR_SELECT);
};

export const getBankAccountById = async (id: string) => {
    return await http.get(Channels.GET_BANK_ACCOUNT_BY_ID, {
        params: { id }
    });
};

export const adjustBalance = async (
    id: string,
    adjustment: number,
    reason: string,
    userId: string
) => {
    return await http.post(Channels.ADJUST_BANK_ACCOUNT_BALANCE, {
        params: { id },
        body: { adjustment, reason, userId }
    });
};

export const deactivateAccount = async (id: string) => {
    return await http.post(Channels.DEACTIVATE_BANK_ACCOUNT, {
        params: { id }
    });
};

export const getTransactionHistory = async (
    id: string,
    params: GetTransactionHistoryParams = {}
) => {
    const { startDate, endDate, ...rest } = params;

    return await http.get(Channels.GET_BANK_ACCOUNT_TRANSACTIONS, {
        params: { id },
        query: {
            ...rest,
            startDate: startDate?.toISOString(),
            endDate: endDate?.toISOString()
        }
    });
};

export const generateBankStatement = async (
    id: string,
    params: GetBankStatementParams
) => {
    const { startDate, endDate, ...rest } = params;

    return await http.get(Channels.GENERATE_BANK_STATEMENT, {
        params: { id },
        query: {
            ...rest,
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString()
        }
    });
};

export const reconcileBalance = async (id: string) => {
    return await http.get(Channels.RECONCILE_BANK_ACCOUNT, {
        params: { id }
    });
};