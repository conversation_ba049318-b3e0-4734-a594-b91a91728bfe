import { Input, Select, Space } from 'antd'
import type { GetPropertiesParams } from '@/common/types'

interface PropertyFiltersProps {
  filters: Partial<GetPropertiesParams>
  onFiltersChange: (filters: Partial<GetPropertiesParams>) => void
}

export const PropertyFilters = ({ filters, onFiltersChange }: PropertyFiltersProps) => {
  const handleChange = (key: keyof GetPropertiesParams, value: any) => {
    onFiltersChange({ ...filters, [key]: value })
  }

  return (
    <Space className="mb-4 w-full" size="middle" wrap>
      <Input.Search
        placeholder="Search properties"
        allowClear
        className="max-w-xs"
        value={filters.search}
        onChange={(e) => handleChange('search', e.target.value)}
      />

      <Select
        placeholder="Property Type"
        allowClear
        className="min-w-[150px]"
        value={filters.type}
        onChange={(value) => handleChange('type', value)}
        options={[
          { value: 'SHOP', label: 'SHOP' },
          { value: 'BUILDING', label: 'BUILDING' },
          { value: 'LAND', label: 'LAND' },
          { value: 'YARD', label: 'YARD' },
          { value: 'OTHER', label: 'OTHER' }
        ]}
      />

      <Select
        placeholder="Availability"
        allowClear
        className="min-w-[150px]"
        value={filters.isAvailable}
        onChange={(value) => handleChange('isAvailable', value)}
        options={[
          { value: true, label: 'Available' },
          { value: false, label: 'Rented' }
        ]}
      />
    </Space>
  )
}
