export const formatCurrency = (amount: number, currency?: string) => {
    return new Intl.NumberFormat('en-PK', {
        style: 'currency',
        currency: currency || 'PKR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount)
}

export const formatDate = (date: string | Date) => {
    const d = new Date(date);
    return d.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
}; 