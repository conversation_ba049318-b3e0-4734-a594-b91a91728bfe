import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>, Tabs, Switch } from 'antd'
import { SwapOutlined } from '@ant-design/icons'
import { TransitionWrapper } from '@/renderer/components'
import {
  StructuredExchange,
  ImprovedStructuredExchange,
  ExchangeHistory,
  ExchangeStatistics,
  DailyExchangeSummary,
  MonthlyExchangeReport
} from './components'

const Exchange = () => {
  const [showStructuredExchange, setShowStructuredExchange] = useState(false)
  const [useImprovedUI, setUseImprovedUI] = useState(false)
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  return (
    <div className="flex h-full flex-col gap-4 p-6">
      <div className="relative flex-1">
        <TransitionWrapper isVisible={showStructuredExchange} direction="right">
          {useImprovedUI ? (
            <ImprovedStructuredExchange
              onClose={() => setShowStructuredExchange(false)}
              setRefreshTrigger={setRefreshTrigger}
            />
          ) : (
            <StructuredExchange
              onClose={() => setShowStructuredExchange(false)}
              setRefreshTrigger={setRefreshTrigger}
            />
          )}
        </TransitionWrapper>

        <TransitionWrapper isVisible={!showStructuredExchange} direction="left">
          <Card className="mb-4 shadow-sm">
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-semibold">Currency Exchange</h1>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span>Use Improved UI:</span>
                  <Switch checked={useImprovedUI} onChange={setUseImprovedUI} />
                </div>
                <Button
                  type="primary"
                  icon={<SwapOutlined />}
                  onClick={() => setShowStructuredExchange(true)}
                >
                  New Exchange
                </Button>
              </div>
            </div>
          </Card>

          <Card className="shadow-sm">
            <Tabs
              defaultActiveKey="history"
              items={[
                {
                  key: 'history',
                  label: 'Exchange History',
                  children: <ExchangeHistory refreshTrigger={refreshTrigger} />
                },
                {
                  key: 'statistics',
                  label: 'Statistics',
                  children: <ExchangeStatistics />
                },
                {
                  key: 'daily',
                  label: 'Daily Summary',
                  children: <DailyExchangeSummary />
                },
                {
                  key: 'monthly',
                  label: 'Monthly Report',
                  children: <MonthlyExchangeReport />
                }
              ]}
            />
          </Card>
        </TransitionWrapper>
      </div>
    </div>
  )
}

export default Exchange
