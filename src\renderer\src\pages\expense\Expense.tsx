import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Space } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import { ExpenseList } from './components'
import { ExpenseForm } from './components'
import { ExpenseFilters } from './components'
import type { CreateExpenseData, GetExpensesParams } from '@/common/types'
import { expenseApi } from '@/renderer/services'

const Expense = () => {
  const [isFormVisible, setIsFormVisible] = useState(false)
  const [loading, setLoading] = useState(false)
  const [filters, setFilters] = useState<GetExpensesParams>({
    page: 1,
    pageSize: 20,
    orderBy: 'desc'
  })

  const { message } = App.useApp()

  const handleCreateExpense = async (data: CreateExpenseData) => {
    setLoading(true)
    const response = await expenseApi.createExpense(data)
    console.log(response)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      setLoading(false)
      return
    }

    message.success('Expense created successfully')
    setIsFormVisible(false)
    // Refresh list
    setFilters((prev) => ({ ...prev }))
    setLoading(false)
  }

  return (
    <div className="flex h-full flex-col gap-4 p-6">
      <Card className="shadow-sm">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold">Expenses</h1>
          <Button type="primary" icon={<PlusOutlined />} onClick={() => setIsFormVisible(true)}>
            New Expense
          </Button>
        </div>
      </Card>

      <Card className="shadow-sm">
        <ExpenseFilters filters={filters} onFiltersChange={setFilters} />
      </Card>

      <Card className="flex-1 overflow-hidden shadow-sm">
        <ExpenseList filters={filters} onFiltersChange={setFilters} />
      </Card>

      <ExpenseForm
        open={isFormVisible}
        onCancel={() => setIsFormVisible(false)}
        onSubmit={handleCreateExpense}
        loading={loading}
      />
    </div>
  )
}

export default Expense
