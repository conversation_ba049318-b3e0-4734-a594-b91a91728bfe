import { UserRole } from '@prisma/client';

export interface LoginData {
    username: string;
    password: string;
}

export interface CreateUserData {
    username: string;
    password: string;
    name: string;
    role: UserRole;
}

export interface UpdatePasswordData {
    userId: string;
    currentPassword: string;
    newPassword: string;
}

export interface ResetPasswordData {
    userId: string;
    adminId: string;
    newPassword: string;
}

export interface GetUsersParams {
    page: number;
    limit: number;
    includeInactive?: boolean;
}

export interface GetUsersResponse {
    users: Array<{
        id: string;
        username: string;
        name: string;
        role: UserRole;
        isActive: boolean;
        createdAt: Date;
    }>;
    total: number;
    page: number;
    totalPages: number;
}