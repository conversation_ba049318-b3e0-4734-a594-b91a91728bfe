import { Car, CarPart, Electronic, ItemStatus, Scrap } from '@prisma/client';

export interface GetStockParams {
    page: number;
    limit: number;
    status?: ItemStatus | 'ALL';
    search?: string;
    containerId?: string;
    orderBy?: 'asc' | 'desc';
}

export interface GetStockResponse<T> {
    items: T[];
    total: number;
    page: number;
    totalPages: number;
}

// Type guards for different item types
export interface StockCarResponse {
    id: string;
    chassisNumber: string;
    modelNumber: string;
    name: string;
    color: string;
    status: ItemStatus;
    container: {
        containerNumber: string;
        openedAt: Date;
    };
    sale?: {
        id: string;
        date: Date;
        totalAmount: number;
        quantity: number;
    };
}

export interface StockPartResponse {
    id: string;
    name: string;
    quantity: number;
    initialQuantity: number;
    status: ItemStatus;
    container: {
        containerNumber: string;
        openedAt: Date;
    };
    sales: Array<{
        id: string;
        date: Date;
        totalAmount: number;
        quantity: number;
    }>;
}

export interface StockElectronicResponse {
    id: string;
    name: string;
    quantity: number;
    initialQuantity: number;
    status: ItemStatus;
    container: {
        containerNumber: string;
        openedAt: Date;
    };
    sales: Array<{
        id: string;
        date: Date;
        totalAmount: number;
        quantity: number;
    }>;
}

export interface StockScrapResponse {
    id: string;
    description: string;
    quantity: number;
    initialQuantity: number;
    status: ItemStatus;
    container: {
        containerNumber: string;
        openedAt: Date;
    };
    sales: Array<{
        id: string;
        date: Date;
        totalAmount: number;
        quantity: number;
    }>;
}

interface StockItem {
    container: {
        containerNumber: string;
        openedAt: Date;
    };
}

interface StockItemWithSale extends StockItem {
    sale?: {
        id: string;
        date: Date;
        totalAmount: number;
        quantity: number;
    };
}

interface StockItemWithSales extends StockItem {
    sales: Array<{
        id: string;
        date: Date;
        totalAmount: number;
        quantity: number;
    }>;
}

export interface CarWithRelations extends Car, StockItemWithSale { }
export interface CarPartWithRelations extends CarPart, StockItemWithSales { }
export interface ElectronicWithRelations extends Electronic, StockItemWithSales { }
export interface ScrapWithRelations extends Scrap, StockItemWithSales { }

export interface StockResponse<T> {
    items: T[];
    total: number;
    page: number;
    totalPages: number;
}

export interface StockSummaryResponse {
    cars: Array<{
        status: ItemStatus;
        _count: { _all: number };
    }>;
    carParts: Array<{
        status: ItemStatus;
        _count: { _all: number };
        _sum: {
            total: number;
            remaining: number;
            sold: number;
        };
    }>;
    electronics: Array<{
        status: ItemStatus;
        _count: { _all: number };
        _sum: {
            total: number;
            remaining: number;
            sold: number;
        };
    }>;
    scraps: Array<{
        status: ItemStatus;
        _count: { _all: number };
        _sum: {
            total: number;
            remaining: number;
            sold: number;
        };
    }>;
}