import { useState } from 'react'
import { Card, Layout, Tabs } from 'antd'
import { Staff<PERSON><PERSON>, AddStaff, ResetPassword } from './components'
import { TransitionWrapper } from '@/renderer/components'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'

const { Content } = Layout

const Staff = () => {
  const [activeTab, setActiveTab] = useState('list')
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const user = useSelector((state: IRootState) => state.user.data)

  console.log(user)

  const items = [
    {
      key: 'list',
      label: 'Staff List',
      children: <StaffList refreshTrigger={refreshTrigger} />
    }
  ]

  // Add reset password tab only for admin users
  if (user?.role === 'ADMIN') {
    items.push(
      {
        key: 'reset',
        label: 'Reset Password',
        children: (
          <ResetPassword
            onSuccess={() => {
              setActiveTab('list')
            }}
          />
        )
      },
      {
        key: 'add',
        label: 'Add Staff',
        children: (
          <AddStaff
            onSuccess={() => {
              setRefreshTrigger((prev) => prev + 1)
              setActiveTab('list')
            }}
          />
        )
      }
    )
  }

  return (
    <Layout className="h-full">
      <Card className="m-6">
        <Tabs activeKey={activeTab} items={items} onChange={setActiveTab} />
      </Card>
    </Layout>
  )
}

export default Staff
