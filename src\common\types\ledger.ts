import { TransactionType, TransactionLocation, TransactionCategory } from '@prisma/client'

// Common types
export interface LedgerEntry {
    id: string
    date: Date
    description: string | null
    amount: number
    type: TransactionType
    sourceType: TransactionLocation
    destinationType: TransactionLocation
    transactionType: TransactionCategory
    isDeleted: boolean
    currency: {
        id: string
        code: string
    }
}



// Request params types
export interface GetAccountLedgerParams {
    startDate?: Date
    endDate?: Date
    currencyId?: string
    page?: number
    limit?: number
}

export interface GetAccountStatementParams {
    startDate: Date
    endDate: Date
    currencyId: string
}

export interface ReconcileBalanceParams {
    location: TransactionLocation
    currencyId: string
    bankAccountId?: string
}

export interface GetDailyLedgerParams {
    date: Date
}

export interface GetTransactionsByCategoryParams {
    startDate: Date
    endDate: Date
    categories?: TransactionCategory[]
}

export interface GetTransactionsByLocationParams {
    startDate: Date
    endDate: Date
    locations?: TransactionLocation[]
}

export interface GetBalanceSheetParams {
    date?: Date
}

export interface GetProfitLossStatementParams {
    startDate: Date
    endDate: Date
}

export interface GetAuditTrailParams {
    startDate: Date
    endDate: Date
    userId?: string
    transactionId?: string
    page?: number
    limit?: number
}

export interface VerifyTransactionChainParams {
    transactionId: string
}

// Response types
export interface AccountLedgerResponse {
    entries: LedgerEntry[]
    pagination: {
        total: number
        page: number
        limit: number
    }
}

export interface AccountStatementLedgerResponse {
    openingBalance: number
    closingBalance: number
    transactions: (LedgerEntry & { runningBalance: number })[]
    period: {
        startDate: Date
        endDate: Date
    }
}

export interface ReconcileBalanceResponse {
    isReconciled: boolean
    currentBalance: number
    calculatedBalance: number
    difference: number
}

export interface DailyLedgerResponse {
    date: Date
    entries: LedgerEntry[]
    groupedEntries: Record<TransactionCategory, LedgerEntry[]>
    totals: Record<string, {
        credits: number
        debits: number
        net: number
    }>
}

export interface TransactionsByCategoryResponse {
    startDate: Date
    endDate: Date
    summary: Record<TransactionCategory, Record<string, {
        count: number
        total: number
        entries: LedgerEntry[]
    }>>
}

export interface TransactionsByLocationResponse {
    startDate: Date
    endDate: Date
    summary: Record<TransactionLocation, Record<string, {
        inflow: number
        outflow: number
        net: number
        entries: LedgerEntry[]
    }>>
}

export interface BalanceSheetResponse {
    date: Date
    assets: {
        cash: Record<string, {
            smallCounter: number
            cashVault: number
            bankAccounts: number
            total: number
        }>
        receivables: Record<string, number>
        inventory: {
            cars: number
            parts: number
            electronics: number
            scrap: number
            total: number
        }
    }
}

export interface ProfitLossStatementResponse {
    period: {
        startDate: Date
        endDate: Date
    }
    summary: Record<string, {
        revenue: {
            sales: number
            rent: number
            total: number
        }
        expenses: {
            operational: number
            other: number
            total: number
        }
        profit: number
    }>
}

export interface AuditTrailResponse {
    entries: LedgerEntry[]
    pagination: {
        total: number
        page: number
        limit: number
    }
}

export interface VerifyTransactionChainResponse {
    isValid: boolean
    errors: string[]
    mainEntry: LedgerEntry
    relatedEntries: LedgerEntry[]
}

export interface BalanceVerificationResponse {
    timestamp: Date
    results: {
        currency: {
            id: string
            code: string
        }
        ledgerBalances: {
            smallCounter: number
            cashVault: number
            bankAccounts: number
            accounts: Record<string, number>
        }
        actualBalances: {
            smallCounter: number
            cashVault: number
            bankAccounts: number
            accounts: Record<string, number>
        }
        discrepancies: {
            smallCounter: boolean
            cashVault: boolean
            bankAccounts: boolean
            accounts: Record<string, boolean>
        }
        isValid: boolean
    }[]
    isValid: boolean
}


export interface PaginatedLedgerResponse {
    entries: Array<LedgerEntry & { balance: number }>
    pagination: {
        total: number
        page: number
        limit: number
        hasMore: boolean
    }
}


