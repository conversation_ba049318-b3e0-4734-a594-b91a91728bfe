import { resetService } from '../services'

class ResetController {
    // Account Management
    clearAccounts = async () => {
        await resetService.clearAccounts()
        return { success: true }
    }

    clearAccountBalances = async () => {
        await resetService.clearAccountBalances()
        return { success: true }
    }

    // Container & Items
    clearContainers = async () => {
        await resetService.clearContainers()
        return { success: true }
    }

    clearCars = async () => {
        await resetService.clearCars()
        return { success: true }
    }

    clearCarParts = async () => {
        await resetService.clearCarParts()
        return { success: true }
    }

    clearElectronics = async () => {
        await resetService.clearElectronics()
        return { success: true }
    }

    clearScraps = async () => {
        await resetService.clearScraps()
        return { success: true }
    }

    // Partners
    clearPartners = async () => {
        await resetService.clearPartners()
        return { success: true }
    }

    // Sales & Payments
    clearSales = async () => {
        await resetService.clearSales()
        return { success: true }
    }

    clearPayments = async () => {
        await resetService.clearPayments()
        return { success: true }
    }

    // Cash Management
    clearSmallCounter = async () => {
        await resetService.clearSmallCounter()
        return { success: true }
    }

    clearCashVault = async () => {
        await resetService.clearCashVault()
        return { success: true }
    }

    clearBankAccounts = async () => {
        await resetService.clearBankAccounts()
        return { success: true }
    }

    // Currency & Exchange
    clearCurrencyExchanges = async () => {
        await resetService.clearCurrencyExchanges()
        return { success: true }
    }

    // Expenses
    clearExpenses = async () => {
        await resetService.clearExpenses()
        return { success: true }
    }

    // Properties & Rent
    clearProperties = async () => {
        await resetService.clearProperties()
        return { success: true }
    }

    clearRentedProperties = async () => {
        await resetService.clearRentedProperties()
        return { success: true }
    }

    // Ledger
    clearLedgerEntries = async () => {
        await resetService.clearLedgerEntries()
        return { success: true }
    }

    // Clear All Data
    clearAllData = async () => {
        await resetService.clearAllData()
        return { success: true }
    }
}

export const resetController = new ResetController()
