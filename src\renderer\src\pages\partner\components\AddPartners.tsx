import { Form, Input, Button } from 'antd'
import { FiArrowLeft } from 'react-icons/fi'
import { useApi } from '@/renderer/hooks'
import { partnerApi } from '@/renderer/services'
import { usePartnerContext, useTheme } from '@/renderer/contexts'
import type { CreatePartnerData } from '@/common/types'

interface AddPartnersProps {
  onClose: () => void
  setRefreshTrigger: (value: any) => void
}

const AddPartners = ({ onClose, setRefreshTrigger }: AddPartnersProps) => {
  const { isDarkMode } = useTheme()

  const [form] = Form.useForm()
  const { refreshPartners } = usePartnerContext()
  const { request: createPartner, isLoading } = useApi(partnerApi.createPartner)

  const handleSubmit = async (values: CreatePartnerData) => {
    try {
      await createPartner(values)
      await refreshPartners()
      setRefreshTrigger((prev) => prev + 1)
      onClose()
    } catch (error: any) {
      // Error will be handled by useApi hook
      console.error('Failed to create partner:', error)
    }
  }

  return (
    <div className="h-full overflow-y-auto p-6">
      <div className="mb-6 flex items-center">
        <Button icon={<FiArrowLeft />} onClick={onClose} className="mr-4" />
        <h1 className="text-2xl font-semibold">Add New Partner</h1>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        className={`m-auto max-w-2xl rounded-lg p-6 shadow-lg ${isDarkMode ? 'bg-neutral-900' : 'bg-slate-50'}`}
      >
        <Form.Item
          name="name"
          label="Partner Name"
          rules={[
            { required: true, message: 'Please enter partner name' },
            { min: 3, message: 'Name must be at least 3 characters' }
          ]}
        >
          <Input placeholder="Enter partner name" />
        </Form.Item>

        <div className="mt-8 flex justify-end">
          <Button onClick={onClose} className="mr-4">
            Cancel
          </Button>
          <Button type="primary" htmlType="submit" loading={isLoading}>
            Create Partner
          </Button>
        </div>
      </Form>
    </div>
  )
}

export default AddPartners
