import { useEffect, useState } from 'react'
import {
  Table,
  DatePicker,
  Input,
  Select,
  Button,
  Popconfirm,
  App,
  Switch,
  Tag,
  Tooltip,
  Card,
  Typography,
  Space
} from 'antd'
import { useApi } from '@/renderer/hooks'
import { paymentApi } from '@/renderer/services'
import { formatDate, formatCurrency } from '@/renderer/utils'
import type { GetTransfersParams, GetTransfersResponse } from '@/common/types'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { useAccountContext, useTheme } from '@/renderer/contexts'
import {
  DeleteOutlined,
  InfoCircleOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined
} from '@ant-design/icons'

const { RangePicker } = DatePicker

interface TransferListProps {
  refreshTrigger: number
}

export const TransferList = ({ refreshTrigger }: TransferListProps) => {
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [dateRange, setDateRange] = useState<[Date?, Date?]>([])
  const [search, setSearch] = useState('')
  const [currencyCode, setCurrencyCode] = useState<string>()
  const [accountId, setAccountId] = useState<string>()
  const [deleteReason, setDeleteReason] = useState('')
  const [includeDeleted, setIncludeDeleted] = useState(false)
  const [orderBy, setOrderBy] = useState<'asc' | 'desc'>('desc')
  const [tableSize, setTableSize] = useState<'small' | 'middle' | 'large'>('small')

  const { accounts, tenants } = useAccountContext()
  const { message } = App.useApp()
  const user = useSelector((state: IRootState) => state.user.data)

  const { isDarkMode } = useTheme()

  const {
    data,
    isLoading,
    request: fetchTransfers
  } = useApi<GetTransfersResponse, [GetTransfersParams]>(paymentApi.getTransfers)

  console.log(data)

  useEffect(() => {
    fetchTransfers({
      page,
      limit: pageSize,
      startDate: dateRange[0],
      endDate: dateRange[1],
      search,
      currencyCode,
      accountId,
      includeDeleted,
      orderBy
    })
  }, [
    page,
    pageSize,
    dateRange,
    search,
    currencyCode,
    accountId,
    includeDeleted,
    orderBy,
    refreshTrigger
  ])

  const handleDelete = async (entryId: string) => {
    if (!deleteReason) {
      message.error('Please enter a reason for deletion')
      return
    }

    const response = await paymentApi.deleteTransfer({
      ledgerEntryId: entryId,
      userId: user?.id || '',
      reason: deleteReason
    })

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      setDeleteReason('')
      return
    }

    message.success('Transfer deleted successfully')
    setDeleteReason('')
    fetchTransfers({
      page,
      limit: pageSize,
      startDate: dateRange[0],
      endDate: dateRange[1],
      search,
      currencyCode,
      accountId,
      orderBy
    })
  }

  const getTransferTypeTag = (record: any) => {
    if (record.account) {
      return <Tag color="blue">Account Transfer</Tag>
    }
    return <Tag color="purple">Location Transfer</Tag>
  }

  const getTransferDetails = (record: any) => {
    if (record.account) {
      // Account Transfer
      if (record.type === 'DEBIT') {
        return `From: ${record.account.name}`
      } else {
        return `To: ${record.account.name}`
      }
    } else {
      // Location Transfer
      const location = record.type === 'DEBIT' ? record.sourceType : record.destinationType
      const bankAccount = record.bankAccountId ? ' (Bank Account)' : ''
      return record.type === 'DEBIT'
        ? `From: ${location}${bankAccount}`
        : `To: ${location}${bankAccount}`
    }
  }

  const columns = [
    {
      title: 'S.No',
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      width: 60,
      render: (_: any, __: any, index: number) => {
        // Calculate serial number based on current page and page size
        return (page - 1) * pageSize + index + 1
      }
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => formatDate(date)
    },
    {
      title: 'Type',
      key: 'type',
      render: (_, record: any) => getTransferTypeTag(record)
    },
    {
      title: 'Details',
      key: 'details',
      render: (_, record: any) => (
        <div>
          <div>{getTransferDetails(record)}</div>
          <div className="text-sm text-gray-500">{record.description}</div>
        </div>
      )
    },
    {
      title: 'Amount',
      key: 'amount',
      render: (_, record: any) => (
        <div className={record.type === 'CREDIT' ? 'text-green-600' : 'text-red-600'}>
          {formatCurrency(record.amount, record.currency.code)}
        </div>
      )
    },
    {
      title: 'Status',
      key: 'status',
      render: (_, record: any) => (
        <div className="flex items-center gap-2">
          {record.isDeleted ? (
            <>
              <Tag color="red">Deleted</Tag>
              <Tooltip
                title={
                  <div>
                    <div>Created by: {record.createdBy.name}</div>
                    <div>Deleted by: {record.deletedBy.name}</div>
                    <div>Reason: {record.deleteReason}</div>
                    <div>Date: {formatDate(record.deletedAt)}</div>
                  </div>
                }
              >
                <InfoCircleOutlined className="cursor-pointer text-gray-400" />
              </Tooltip>
            </>
          ) : (
            <>
              <Tag color="green">Active</Tag>
              <Tooltip
                title={
                  <div>
                    <div>Created by: {record.createdBy.name}</div>
                    <div>Date: {formatDate(record.createdAt)}</div>
                  </div>
                }
              >
                <InfoCircleOutlined className="cursor-pointer text-gray-400" />
              </Tooltip>
            </>
          )}
        </div>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record: any) =>
        !record.isDeleted && (
          <Popconfirm
            title="Delete Transfer"
            description={
              <div className="flex flex-col gap-2">
                <p>Are you sure you want to delete this transfer?</p>
                <Input
                  placeholder="Enter reason for deletion"
                  value={deleteReason}
                  onChange={(e) => setDeleteReason(e.target.value)}
                />
              </div>
            }
            onConfirm={() => handleDelete(record.id)}
            onCancel={() => setDeleteReason('')}
            okText="Yes"
            cancelText="No"
          >
            <Button danger type="link" icon={<DeleteOutlined />}>
              Delete
            </Button>
          </Popconfirm>
        )
    }
  ]

  return (
    <Card className="mt-2">
      <Space className="mb-2 flex w-full flex-wrap items-center justify-between">
        <Space className="flex flex-wrap items-center gap-4" wrap>
          <RangePicker
            onChange={(_, dateStrings) => {
              setDateRange([
                dateStrings[0] ? new Date(dateStrings[0]) : undefined,
                dateStrings[1] ? new Date(dateStrings[1]) : undefined
              ])
            }}
            className="w-64"
          />
          <Input.Search placeholder="Search by description" onSearch={setSearch} className="w-64" />
          <Select
            placeholder="Select currency"
            allowClear
            onChange={setCurrencyCode}
            className="w-32"
          >
            <Select.Option value="USD">USD</Select.Option>
            <Select.Option value="PKR">PKR</Select.Option>
            <Select.Option value="AED">AED</Select.Option>
            <Select.Option value="AFN">AFN</Select.Option>
          </Select>

          <Select
            placeholder="Select account"
            allowClear
            onChange={setAccountId}
            className="w-56"
            options={[...accounts, ...tenants]}
          />
          <Select
            value={orderBy}
            className="w-40"
            onChange={setOrderBy}
            options={[
              {
                label: (
                  <div className="flex items-center">
                    <SortDescendingOutlined className="mr-2" /> Newest First
                  </div>
                ),
                value: 'desc'
              },
              {
                label: (
                  <div className="flex items-center">
                    <SortAscendingOutlined className="mr-2" /> Oldest First
                  </div>
                ),
                value: 'asc'
              }
            ]}
          />
          <div className="flex items-center gap-2">
            <Switch checked={includeDeleted} onChange={(checked) => setIncludeDeleted(checked)} />
            <span>Show Deleted</span>
          </div>
        </Space>

        <Space>
          <Typography.Text>Table Size:</Typography.Text>
          <Select
            value={tableSize}
            onChange={setTableSize}
            options={[
              { label: 'Small', value: 'small' },
              { label: 'Middle', value: 'middle' },
              { label: 'Large', value: 'large' }
            ]}
            style={{ width: 100 }}
          />
        </Space>
      </Space>

      <Table
        columns={columns}
        dataSource={data?.transfers}
        rowKey="id"
        loading={isLoading}
        sticky
        virtual
        size={tableSize}
        rowClassName={(record) =>
          record.isDeleted ? `bg-${isDarkMode ? 'black' : 'gray-100'}` : ''
        }
        pagination={{
          position: ['topRight'],
          showQuickJumper: true,
          showPrevNextJumpers: true,
          current: page,
          pageSize,
          total: data?.total,
          onChange: (page, pageSize) => {
            setPage(page)
            setPageSize(pageSize)
          },
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} transfers`
        }}
      />
    </Card>
  )
}
