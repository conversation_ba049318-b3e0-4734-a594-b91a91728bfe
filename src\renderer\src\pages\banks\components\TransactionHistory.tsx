import { Table, Tag, Select, DatePicker, Space, Button } from 'antd'
import { useEffect, useState } from 'react'
import { useApi } from '@/renderer/hooks'
import { bankAccountApi } from '@/renderer/services'
import { formatDate, formatCurrency } from '@/renderer/utils'
import type {
  BankTransaction,
  TransactionHistoryResponse,
  GetTransactionHistoryParams,
  BankStatementEntry
} from '@/common/types'
import { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'
import { FiRefreshCw } from 'react-icons/fi'

const { RangePicker } = DatePicker
const { Option } = Select

type SortOrder = 'asc' | 'desc'
type TableSize = 'small' | 'middle' | 'large'

interface TransactionHistoryProps {
  bankId: string
  transactions?: (BankTransaction & { runningBalance?: number })[] | BankStatementEntry[]
  showRunningBalance?: boolean
}

export const TransactionHistory = ({
  bankId,
  transactions: preloadedTransactions,
  showRunningBalance = false
}: TransactionHistoryProps) => {
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [sortOrder, setSortOrder] = useState<SortOrder>('asc')
  const [dateRange, setDateRange] = useState<[Date, Date] | null>(null)
  const [tableSize, setTableSize] = useState<TableSize>('small')

  const {
    data,
    isLoading,
    request: fetchTransactions
  } = useApi<TransactionHistoryResponse, [string, GetTransactionHistoryParams]>(
    bankAccountApi.getTransactionHistory
  )

  const loadTransactions = () => {
    if (!preloadedTransactions) {
      fetchTransactions(bankId, {
        page,
        pageSize,
        sortOrder,
        startDate: dateRange ? dateRange[0] : undefined,
        endDate: dateRange ? dateRange[1] : undefined
      })
    }
  }

  useEffect(() => {
    loadTransactions()
  }, [bankId, page, pageSize, sortOrder])

  const baseColumns: ColumnsType<BankTransaction & { runningBalance?: number }> = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: 150,
      render: (date: string) => formatDate(date)
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      render: (description: string, record: BankTransaction) => (
        <div>
          <div>{description}</div>
          <div className="text-xs text-gray-500">Created by: {record.createdBy.name}</div>
        </div>
      )
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: 'CREDIT' | 'DEBIT') => (
        <Tag color={type === 'CREDIT' ? 'green' : 'red'}>{type}</Tag>
      )
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      width: 150,
      align: 'right',
      render: (amount: number, record: BankTransaction) => (
        // make the color dynamic based on type credit or debit
        <span
          className={`font-medium ${record.type === 'CREDIT' ? 'text-green-600' : 'text-red-600'}`}
        >
          {formatCurrency(amount, 'PKR')}
        </span>
      )
    }
  ]

  const columns = showRunningBalance
    ? [
        ...baseColumns,
        {
          title: 'Running Balance',
          dataIndex: 'runningBalance',
          key: 'runningBalance',
          width: 150,
          align: 'right' as const,
          render: (balance: number) => (
            <span className={`font-medium ${balance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {formatCurrency(balance, 'PKR')}
            </span>
          )
        }
      ]
    : baseColumns

  // Add filter controls
  const renderFilters = () => {
    if (preloadedTransactions) return null

    return (
      <div className="mb-4 flex flex-wrap items-center gap-4">
        <Select
          value={sortOrder}
          onChange={(value: SortOrder) => {
            setSortOrder(value)
            setPage(1) // Reset to first page when changing sort order
          }}
          style={{ width: 150 }}
        >
          <Select.Option value="desc">Newest First</Select.Option>
          <Select.Option value="asc">Oldest First</Select.Option>
        </Select>

        <DatePicker.RangePicker
          value={dateRange ? [dayjs(dateRange[0]), dayjs(dateRange[1])] : null}
          onChange={(_, dateStrings) => {
            if (dateStrings[0] && dateStrings[1]) {
              setDateRange([new Date(dateStrings[0]), new Date(dateStrings[1])])
              setPage(1) // Reset to first page when changing date range
            } else {
              setDateRange(null)
            }
          }}
        />

        <Select value={tableSize} onChange={setTableSize} style={{ width: 120 }}>
          <Select.Option value="small">Small</Select.Option>
          <Select.Option value="middle">Medium</Select.Option>
          <Select.Option value="large">Large</Select.Option>
        </Select>

        <Button
          icon={<FiRefreshCw />}
          onClick={() => {
            loadTransactions()
          }}
        >
          Refresh
        </Button>
      </div>
    )
  }

  return (
    <div>
      {renderFilters()}
      {/* Type casting to fix TypeScript issues */}
      <Table
        columns={columns as any}
        dataSource={preloadedTransactions || (data?.transactions as any)}
        loading={isLoading}
        rowKey="id"
        size={tableSize}
        virtual
        sticky
        pagination={
          preloadedTransactions
            ? false
            : {
                current: page,
                pageSize,
                total: data?.pagination.total,
                onChange: (page, pageSize) => {
                  setPage(page)
                  setPageSize(pageSize)
                },
                showSizeChanger: true,
                pageSizeOptions: ['10', '20', '50', '100'],
                showTotal: (total) => `Total ${total} items`
              }
        }
        className="rounded-lg border border-gray-200"
        summary={
          preloadedTransactions
            ? () => (
                <Table.Summary fixed>
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0} colSpan={3} className="text-right font-medium">
                      Total Transactions:
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={1} className="text-right font-medium">
                      {preloadedTransactions.length}
                    </Table.Summary.Cell>
                    {showRunningBalance && <Table.Summary.Cell index={2} />}
                  </Table.Summary.Row>
                </Table.Summary>
              )
            : undefined
        }
      />
    </div>
  )
}
