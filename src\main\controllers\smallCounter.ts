import { InitializeSmallCounterData, IRequest } from '@/common/types';
import { smallCounterService } from '../services';

class SmallCounterController {
    async initializeSmallCounter(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        return await smallCounterService.initializeSmallCounter(req.body as InitializeSmallCounterData);
    }

    async getSmallCounterBalances() {
        return await smallCounterService.getSmallCounterBalances();
    }

    async getBalanceByCurrency(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { currency } = req.params ?? {};
        if (!currency) {
            throw new Error('Currency is required');
        }
        return await smallCounterService.getBalanceByCurrency(currency);
    }

    async adjustBalance(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { currency, adjustment, reason, userId } = req.body ?? {};
        if (!currency || !adjustment || !reason || !userId) {
            throw new Error('Missing required fields');
        }
        return await smallCounterService.adjustBalance(
            currency,
            Number(adjustment),
            reason,
            userId
        );
    }

    async getBalanceHistory(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { currency, startDate, endDate } = req.params ?? {};
        if (!currency || !startDate || !endDate) {
            throw new Error('Missing required fields');
        }
        return await smallCounterService.getBalanceHistory(
            currency,
            new Date(startDate),
            new Date(endDate)
        );
    }

    async transferToVault(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { currency, amount, userId } = req.body ?? {};
        if (!currency || !amount || !userId) {
            throw new Error('Missing required fields');
        }
        return await smallCounterService.transferToVault(
            currency,
            Number(amount),
            userId
        );
    }

    async reconcileBalance(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { currency } = req.params ?? {};
        if (!currency) {
            throw new Error('Currency is required');
        }
        return await smallCounterService.reconcileBalance(currency);
    }

    async getDailyTransactions(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { date } = req.params ?? {};
        if (!date) {
            throw new Error('Date is required');
        }
        return await smallCounterService.getDailyTransactions(new Date(date));
    }

    async getBalanceAtDate(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { currency, date } = req.params ?? {};
        if (!currency || !date) {
            throw new Error('Currency and date are required');
        }
        return await smallCounterService.getBalanceAtDate(
            currency,
            new Date(date)
        );
    }

    async getAllTransactions(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { page, pageSize, startDate, endDate } = req.query ?? {};
        return await smallCounterService.getAllTransactions(
            Number(page) || 1,
            Number(pageSize) || 20,
            startDate ? new Date(startDate) : undefined,
            endDate ? new Date(endDate) : undefined
        );
    }

}

export const smallCounterController = new SmallCounterController();