import { http } from './http'
import { Channels } from '@/common/constants'
import type { TimeRange } from '@/common/types'

export async function getCashOverview() {
    return await http.get(Channels.GET_DASHBOARD_CASH_OVERVIEW)
}

export async function getBankAccountsSummary() {
    return await http.get(Channels.GET_DASHBOARD_BANK_SUMMARY)
}

export async function getTransactionStats(timeRange: TimeRange) {
    return await http.get(Channels.GET_DASHBOARD_TRANSACTION_STATS, {
        query: { timeRange }
    })
}

export async function getAccountStats() {
    return await http.get(Channels.GET_DASHBOARD_ACCOUNT_STATS)
}

export async function getInventorySummary() {
    return await http.get(Channels.GET_DASHBOARD_INVENTORY_SUMMARY)
}

export async function getCurrencyTransactions(timeRange: TimeRange) {
    return await http.get(Channels.GET_DASHBOARD_CURRENCY_TRANSACTIONS, {
        query: { timeRange }
    })
}

export async function getAccountBalances() {
    return await http.get(Channels.GET_DASHBOARD_ACCOUNT_BALANCES)
}

export async function getLocationTransfers(timeRange: TimeRange) {
    return await http.get(Channels.GET_DASHBOARD_LOCATION_TRANSFERS, {
        query: { timeRange }
    })
}

export async function getSalesTrends(timeRange: TimeRange) {
    return await http.get(Channels.GET_DASHBOARD_SALES_TRENDS, {
        query: { timeRange }
    })
}
