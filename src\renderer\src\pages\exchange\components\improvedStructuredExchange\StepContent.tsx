import { Form, Select, Checkbox, InputNumber, Input, Space, Typography, Card } from 'antd'
import { AccountCard } from './AccountCard'
import { LocationCard } from './LocationCard'
import { TransactionPreview } from './TransactionPreview'
import { LocationBalanceResponse } from '@/renderer/services/exchange'
import { TransactionLocation } from '@prisma/client'
import { formatCurrency } from '@/renderer/utils/format'

const { Title } = Typography

interface StepContentProps {
  currentStep: number
  form: any
  accounts: any[]
  banks: any[]
  accountDetails: any
  accountLoading: boolean
  inputLocationBalance: LocationBalanceResponse | null
  outputLocationBalance: LocationBalanceResponse | null
  locationLoading: boolean
  previewData: any
  exchangeRate: number
  showReverseRate: boolean
  setShowReverseRate: (value: boolean) => void
  handleReverseRateChange: (value: number | null) => void
  getAvailableLocations: (currency: string) => { value: string; label: string }[]
}

export const StepContent = ({
  currentStep,
  form,
  accounts,
  banks,
  accountDetails,
  accountLoading,
  inputLocationBalance,
  outputLocationBalance,
  locationLoading,
  previewData,
  exchangeRate,
  showReverseRate,
  setShowReverseRate,
  handleReverseRateChange,
  getAvailableLocations
}: StepContentProps) => {
  // Get form values
  const inputCurrency = Form.useWatch('inputCurrency', form)
  const outputCurrency = Form.useWatch('outputCurrency', form)
  const inputSource = Form.useWatch('inputSource', form)
  const keepInAccount = Form.useWatch('keepInAccount', form)
  const inputLocation = Form.useWatch('inputLocation', form)
  const outputLocation = Form.useWatch('outputLocation', form)

  const CURRENCIES = ['PKR', 'USD', 'AED', 'AFN']

  switch (currentStep) {
    case 0: // Account Selection
      return (
        <>
          <Form.Item
            name="accountId"
            label="Account"
            rules={[{ required: true, message: 'Please select an account' }]}
          >
            <Select
              showSearch
              placeholder="Select account"
              optionFilterProp="label"
              options={accounts}
              onChange={(value) => {
                form.setFieldsValue({ accountId: value })
              }}
            />
          </Form.Item>
          <AccountCard accountDetails={accountDetails} loading={accountLoading} />
        </>
      )

    case 1: // Input Currency
      return (
        <>
          <Title level={5}>Input Currency</Title>
          <Space direction="vertical" className="w-full">
            <Form.Item
              name="inputCurrency"
              label="Currency"
              rules={[
                { required: true, message: 'Please select currency' },
                {
                  validator: (_, value) =>
                    value === outputCurrency
                      ? Promise.reject('Input and output currencies must be different')
                      : Promise.resolve()
                }
              ]}
            >
              <Select placeholder="Select currency">
                {CURRENCIES.map((currency) => (
                  <Select.Option key={currency} value={currency}>
                    {currency}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="inputSource"
              label="Source"
              rules={[{ required: true, message: 'Please select source' }]}
            >
              <Select placeholder="Select source">
                <Select.Option value="NEW_DEPOSIT">New Deposit</Select.Option>
                <Select.Option value="ACCOUNT_BALANCE">Use Account Balance</Select.Option>
              </Select>
            </Form.Item>

            {inputSource === 'ACCOUNT_BALANCE' && (
              <Form.Item name="allowLoan" valuePropName="checked">
                <Checkbox>
                  Allow Loan (Allow exchange even if account balance is insufficient)
                </Checkbox>
              </Form.Item>
            )}

            <Form.Item
              name="inputAmount"
              label="Amount"
              rules={[{ required: true, message: 'Please enter amount' }]}
            >
              <InputNumber
                className="w-full"
                placeholder="Enter amount"
                formatter={(value) => ` ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={(value) =>
                  Number(value!.replace(new RegExp(`${inputCurrency}\\s?|(,*)`, 'g'), ''))
                }
                min={Number(0)}
              />
            </Form.Item>

            {inputSource === 'NEW_DEPOSIT' && (
              <>
                <Form.Item
                  name="inputLocation"
                  label="Input Location"
                  rules={[{ required: true, message: 'Please select source location' }]}
                >
                  <Select
                    placeholder="Select location"
                    options={inputCurrency ? getAvailableLocations(inputCurrency) : []}
                  />
                </Form.Item>

                {inputLocation === 'BANK_ACCOUNT' && (
                  <Form.Item
                    name="inputBankId"
                    label="Bank Account"
                    rules={[{ required: true, message: 'Please select bank account' }]}
                  >
                    <Select placeholder="Select bank account" options={banks} />
                  </Form.Item>
                )}

                <LocationCard
                  location={inputLocationBalance}
                  title="Input Location Balance"
                  loading={locationLoading}
                />
              </>
            )}
          </Space>
        </>
      )

    case 2: // Exchange Details
      return (
        <>
          <Title level={5}>Exchange Details</Title>
          <Space direction="vertical" className="w-full">
            <Form.Item>
              <Checkbox
                checked={showReverseRate}
                onChange={(e) => setShowReverseRate(e.target.checked)}
              >
                Use reverse rate input (1 output currency = X input currency)
              </Checkbox>
            </Form.Item>

            {showReverseRate ? (
              <>
                <Form.Item label="Rate Helper">
                  <InputNumber
                    className="w-full"
                    placeholder={`1 ${outputCurrency || 'output currency'} = ? ${inputCurrency || 'input currency'}`}
                    onChange={handleReverseRateChange}
                    min={1}
                  />
                </Form.Item>

                <Form.Item name="exchangeRate" label="Actual Exchange Rate">
                  <InputNumber
                    className="w-full"
                    disabled
                    placeholder="Will be calculated automatically"
                  />
                </Form.Item>
              </>
            ) : (
              <Form.Item
                name="exchangeRate"
                label="Exchange Rate"
                rules={[{ required: true, message: 'Please enter exchange rate' }]}
              >
                <InputNumber className="w-full" min={0} placeholder="Enter exchange rate" />
              </Form.Item>
            )}

            <Form.Item name="description" label="Description">
              <Input.TextArea rows={3} placeholder="Enter description (optional)" />
            </Form.Item>
          </Space>
        </>
      )

    case 3: // Output Currency
      return (
        <>
          <Title level={5}>Output Currency</Title>
          <Space direction="vertical" className="w-full">
            <Form.Item
              name="outputCurrency"
              label="Currency"
              rules={[
                { required: true, message: 'Please select currency' },
                {
                  validator: (_, value) =>
                    value === inputCurrency
                      ? Promise.reject('Input and output currencies must be different')
                      : Promise.resolve()
                }
              ]}
            >
              <Select placeholder="Select currency">
                {CURRENCIES.map((currency) => (
                  <Select.Option key={currency} value={currency}>
                    {currency}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="keepInAccount"
              label="Keep in Account"
              rules={[{ required: true, message: 'Please select an option' }]}
            >
              <Select placeholder="Select option">
                <Select.Option value={true}>Yes</Select.Option>
                <Select.Option value={false}>No</Select.Option>
              </Select>
            </Form.Item>

            {keepInAccount === false && (
              <>
                <Form.Item
                  name="outputLocation"
                  label="Output Location"
                  rules={[{ required: true, message: 'Please select destination location' }]}
                >
                  <Select
                    placeholder="Select location"
                    options={outputCurrency ? getAvailableLocations(outputCurrency) : []}
                  />
                </Form.Item>

                {outputLocation === 'BANK_ACCOUNT' && (
                  <Form.Item
                    name="outputBankId"
                    label="Bank Account"
                    rules={[{ required: true, message: 'Please select bank account' }]}
                  >
                    <Select placeholder="Select bank account" options={banks} />
                  </Form.Item>
                )}

                <LocationCard
                  location={outputLocationBalance}
                  title="Output Location Balance"
                  loading={locationLoading}
                />
              </>
            )}
          </Space>
        </>
      )

    case 4: // Review
      return (
        <>
          <Title level={5}>Review Exchange</Title>
          <div className="mb-4">
            <Typography.Text>Please review the exchange details before submitting.</Typography.Text>
          </div>

          <Card size="small" title="Account" className="mb-3">
            <div>
              <Typography.Text strong>Name:</Typography.Text> {accountDetails?.name}
            </div>
            <div>
              <Typography.Text strong>Phone:</Typography.Text> {accountDetails?.phoneNumber}
            </div>
          </Card>

          <Card size="small" title="Input Currency" className="mb-3">
            <div>
              <Typography.Text strong>Currency:</Typography.Text> {inputCurrency}
            </div>
            <div>
              <Typography.Text strong>Amount:</Typography.Text>{' '}
              {previewData?.inputAmount
                ? formatCurrency(previewData.inputAmount, inputCurrency)
                : '-'}
            </div>
            <div>
              <Typography.Text strong>Source:</Typography.Text>{' '}
              {inputSource === 'NEW_DEPOSIT' ? 'New Deposit' : 'Account Balance'}
            </div>
            {inputSource === 'NEW_DEPOSIT' && (
              <div>
                <Typography.Text strong>Location:</Typography.Text>{' '}
                {inputLocation?.replace('_', ' ')}
              </div>
            )}
          </Card>

          <Card size="small" title="Exchange Details" className="mb-3">
            <div>
              <Typography.Text strong>Rate:</Typography.Text> 1 {inputCurrency} = {exchangeRate}{' '}
              {outputCurrency}
            </div>
            {previewData && (
              <div>
                <Typography.Text strong>Output Amount:</Typography.Text>{' '}
                {formatCurrency(previewData.outputAmount, outputCurrency)}
              </div>
            )}
          </Card>

          <Card size="small" title="Output Currency" className="mb-3">
            <div>
              <Typography.Text strong>Currency:</Typography.Text> {outputCurrency}
            </div>
            <div>
              <Typography.Text strong>Keep in Account:</Typography.Text>{' '}
              {keepInAccount ? 'Yes' : 'No'}
            </div>
            {!keepInAccount && (
              <div>
                <Typography.Text strong>Location:</Typography.Text>{' '}
                {outputLocation?.replace('_', ' ')}
              </div>
            )}
          </Card>

          <TransactionPreview previewData={previewData} exchangeRate={exchangeRate} />
        </>
      )

    default:
      return null
  }
}
