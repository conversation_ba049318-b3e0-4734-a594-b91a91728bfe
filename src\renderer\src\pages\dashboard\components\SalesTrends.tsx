import { useEffect, useState } from 'react'
import { Typography, Space, Button, Alert, Skeleton, Row, Col, Select } from 'antd'
import { useApi } from '@/renderer/hooks'
import { dashboardApi } from '@/renderer/services'
import { SalesTrendData, TimeRange } from '@/common/types'
import { FaChartLine, FaSync } from 'react-icons/fa'
import { useTheme } from '@/renderer/contexts'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts'
import { formatCurrency } from '@/renderer/utils'

const { Title } = Typography

const TIME_RANGES = [
  { value: '1D', label: 'Today' },
  { value: '7D', label: 'Last 7 Days' },
  { value: '14D', label: 'Last 14 Days' },
  { value: '30D', label: 'Last 30 Days' }
]

const COLORS = {
  walkIn: '#1890ff',
  registered: '#52c41a',
  total: '#722ed1'
}

const SalesTrends = () => {
  const { isDarkMode } = useTheme()
  const [timeRange, setTimeRange] = useState<TimeRange>('1D')

  const {
    data,
    isLoading,
    error,
    request: fetchSalesTrends
  } = useApi<SalesTrendData[], [TimeRange]>(dashboardApi.getSalesTrends)

  useEffect(() => {
    fetchSalesTrends(timeRange)
  }, [timeRange])

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <FaChartLine className="text-2xl text-cyan-600" />
            <Title level={5} className="!mb-0">
              Sales Trends
            </Title>
          </Space>
          <Space>
            <Select value={timeRange} disabled style={{ width: 120 }} />
            <Button icon={<FaSync />} loading />
          </Space>
        </Space>
        <Skeleton active paragraph={{ rows: 4 }} />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <FaChartLine className="text-2xl text-cyan-600" />
            <Title level={5} className="!mb-0">
              Sales Trends
            </Title>
          </Space>
          <Space>
            <Select value={timeRange} disabled style={{ width: 120 }} />
            <Button icon={<FaSync />} onClick={() => fetchSalesTrends(timeRange)} />
          </Space>
        </Space>
        <Alert message="Error" description="Failed to load sales trends" type="error" showIcon />
      </div>
    )
  }

  if (!data) return null

  const chartData = data.map((item) => ({
    date: item.date,
    'Walk-in Sales': item.walkIn.amount,
    'Registered Sales': item.registered.amount,
    'Total Sales': item.total.amount
  }))

  return (
    <div className="flex flex-col gap-4">
      <Space className="flex justify-between">
        <Space>
          <FaChartLine className="text-2xl text-cyan-600" />
          <Title level={5} className="!mb-0">
            Sales Trends
          </Title>
        </Space>
        <Space>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            options={TIME_RANGES}
            style={{ width: 120 }}
          />
          <Button icon={<FaSync />} onClick={() => fetchSalesTrends(timeRange)} />
        </Space>
      </Space>

      <div className={`rounded-lg p-4 ${isDarkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <div style={{ width: '100%', height: 300 }}>
              <ResponsiveContainer>
                <LineChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" tick={{ fill: isDarkMode ? '#fff' : '#000' }} />
                  <YAxis
                    tick={{ fill: isDarkMode ? '#fff' : '#000' }}
                    tickFormatter={(value) => formatCurrency(value, 'PKR')}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: isDarkMode ? '#1f2937' : '#fff',
                      border: 'none'
                    }}
                    labelStyle={{ color: isDarkMode ? '#fff' : '#000' }}
                    formatter={(value: number) => formatCurrency(value, 'PKR')}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="Walk-in Sales"
                    stroke={COLORS.walkIn}
                    dot={false}
                  />
                  <Line
                    type="monotone"
                    dataKey="Registered Sales"
                    stroke={COLORS.registered}
                    dot={false}
                  />
                  <Line type="monotone" dataKey="Total Sales" stroke={COLORS.total} dot={false} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </Col>
          <Col span={24}>
            <Row gutter={[16, 16]}>
              <Col span={8}>
                <div className={`rounded p-3 ${isDarkMode ? 'bg-gray-700' : 'bg-white'}`}>
                  <Title level={5} className="mb-1">
                    Walk-in Sales
                  </Title>
                  <div className="text-sm">
                    <div>Quantity: {data[0].walkIn.quantity.toLocaleString()}</div>
                    <div>Amount: {formatCurrency(data[0].walkIn.amount, 'PKR')}</div>
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div className={`rounded p-3 ${isDarkMode ? 'bg-gray-700' : 'bg-white'}`}>
                  <Title level={5} className="mb-1">
                    Registered Sales
                  </Title>
                  <div className="text-sm">
                    <div>Quantity: {data[0].registered.quantity.toLocaleString()}</div>
                    <div>Amount: {formatCurrency(data[0].registered.amount, 'PKR')}</div>
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div className={`rounded p-3 ${isDarkMode ? 'bg-gray-700' : 'bg-white'}`}>
                  <Title level={5} className="mb-1">
                    Total Sales
                  </Title>
                  <div className="text-sm">
                    <div>Quantity: {data[0].total.quantity.toLocaleString()}</div>
                    <div>Amount: {formatCurrency(data[0].total.amount, 'PKR')}</div>
                  </div>
                </div>
              </Col>
            </Row>
          </Col>
          <Col span={24}>
            <Title level={5} className="mb-2">
              Sales by Type
            </Title>
            <Row gutter={[16, 16]}>
              {Object.entries(data[0].byType).map(([type, { quantity, amount }]) => (
                <Col span={6} key={type}>
                  <div className={`rounded p-3 ${isDarkMode ? 'bg-gray-700' : 'bg-white'}`}>
                    <Title level={5} className="mb-1 capitalize">
                      {type.replace(/_/g, ' ')}
                    </Title>
                    <div className="text-sm">
                      <div>Quantity: {quantity.toLocaleString()}</div>
                      <div>Amount: {formatCurrency(amount, 'PKR')}</div>
                    </div>
                  </div>
                </Col>
              ))}
            </Row>
          </Col>
        </Row>
      </div>
    </div>
  )
}

export default SalesTrends
