import { prisma } from '../db';
import { CreatePartnerData, GetPartnersParams } from '@/common/types';
import { Prisma } from '@prisma/client';

class PartnerService {
    async createPartner(data: CreatePartnerData) {
        const existingPartner = await prisma.partner.findFirst({
            where: {
                name: { equals: data.name, mode: 'insensitive' },
                isDeleted: false
            }
        });

        if (existingPartner) {
            throw new Error('Partner with this name already exists');
        }

        return await prisma.partner.create({
            data
        });
    }

    async deletePartner(id: string, deleteReason: string) {
        const partner = await prisma.partner.findUnique({
            where: { id },
            include: { containers: true }
        });

        if (!partner) {
            throw new Error('Partner not found');
        }

        if (partner.containers.length > 0) {
            throw new Error('Cannot delete partner with existing containers');
        }

        return await prisma.partner.update({
            where: { id },
            data: {
                isDeleted: true,
                deleteReason,
                deletedAt: new Date()
            }
        });
    }

    async getPartners({ page, limit, includeDeleted = false, search }: GetPartnersParams) {
        const where: Prisma.PartnerWhereInput = {
            ...(includeDeleted ? {} : { isDeleted: false }),
            ...(search ? {
                name: { contains: search, mode: 'insensitive' }
            } : {})
        };

        const [partners, total] = await Promise.all([
            prisma.partner.findMany({
                where,
                include: {
                    _count: {
                        select: { containers: true }
                    }
                },
                orderBy: { createdAt: 'desc' },
                skip: (page - 1) * limit,
                take: limit
            }),
            prisma.partner.count({ where })
        ]);

        return {
            partners,
            total,
            page,
            totalPages: Math.ceil(total / limit)
        };
    }

    async getPartnerById(id: string) {
        const partner = await prisma.partner.findUnique({
            where: { id },
            include: {
                containers: {
                    where: { isDeleted: false },
                    orderBy: { openedAt: 'desc' },
                    // for the partner details modal to show active or completed status infront of the container number
                    include: {
                        cars: true,
                        carParts: true,
                        electronics: true,
                        scraps: true
                    }
                }
            }
        });

        if (!partner) {
            throw new Error('Partner not found');
        }

        console.log("------------------------>", partner)

        return partner;
    }


    async getPartnersForSelect() {
        const partners = await prisma.partner.findMany({
            where: { isDeleted: false },
            orderBy: { createdAt: 'desc' }
        });

        return partners.map((partner) => ({
            value: partner.id,
            label: partner.name
        }));
    }

}

export const partnerService = new PartnerService();