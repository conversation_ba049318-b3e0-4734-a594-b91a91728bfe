import { useEffect, useState } from 'react'
import { App, Card, Col, DatePicker, Row, Statistic, Table, Tag } from 'antd'
import { ArrowDownOutlined, ArrowUpOutlined } from '@ant-design/icons'
import { exchangeApi } from '@/renderer/services'
import type { MonthlyExchangeReport as MonthlyReportType } from '@/common/types'
import dayjs from 'dayjs'

export const MonthlyExchangeReport = () => {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<MonthlyReportType>({
    month: new Date(),
    totalVolume: 0,
    byCurrency: [],
    trends: []
  })
  const [selectedMonth, setSelectedMonth] = useState(dayjs())

  const { message } = App.useApp()

  const fetchReport = async (month: Date) => {
    setLoading(true)
    const response = await exchangeApi.getMonthlyExchangeReport(month)
    setLoading(false)
    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }
    setData(response.data.data)
  }

  useEffect(() => {
    fetchReport(selectedMonth.toDate())
  }, [selectedMonth])

  const volumeColumns = [
    {
      title: 'Currency',
      dataIndex: 'currency',
      key: 'currency',
      render: (currency: string) => <Tag color="blue">{currency}</Tag>
    },
    {
      title: 'Inflow',
      dataIndex: 'inflow',
      key: 'inflow',
      render: (inflow: number, record: any) => (
        <span className="flex items-center gap-2 font-medium text-green-600">
          <ArrowUpOutlined />
          {inflow.toLocaleString()} {record.currency}
        </span>
      )
    },
    {
      title: 'Outflow',
      dataIndex: 'outflow',
      key: 'outflow',
      render: (outflow: number, record: any) => (
        <span className="flex items-center gap-2 font-medium text-red-600">
          <ArrowDownOutlined />
          {outflow.toLocaleString()} {record.currency}
        </span>
      )
    },
    {
      title: 'Average Rate',
      dataIndex: 'averageRate',
      key: 'averageRate',
      render: (rate: number) => rate.toFixed(4)
    }
  ]

  const trendColumns = [
    {
      title: 'Currency',
      dataIndex: 'currency',
      key: 'currency',
      render: (currency: string) => <Tag color="blue">{currency}</Tag>
    },
    {
      title: 'Trend',
      key: 'trend',
      render: (record: any) => {
        const { trend, percentageChange } = record
        const isPositive = trend === 'UP'
        const isStable = trend === 'STABLE'
        return (
          <span
            className={`flex items-center gap-2 font-medium ${
              isStable ? 'text-gray-600' : isPositive ? 'text-green-600' : 'text-red-600'
            }`}
          >
            {isStable ? '→' : isPositive ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
            {Math.abs(percentageChange).toFixed(2)}%
          </span>
        )
      }
    }
  ]

  return (
    <div className="flex flex-col gap-4">
      <Row gutter={16} className="mb-4">
        <Col span={12}>
          <Card>
            <Statistic
              title="Total Volume"
              value={data.totalVolume}
              loading={loading}
              prefix="PKR"
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card>
            <Statistic title="Active Currencies" value={data.byCurrency.length} loading={loading} />
          </Card>
        </Col>
      </Row>

      <Card
        title="Monthly Exchange Report"
        extra={
          <DatePicker
            value={selectedMonth}
            onChange={(date) => date && setSelectedMonth(date)}
            picker="month"
            allowClear={false}
            disabledDate={(date) => date.isAfter(dayjs())}
          />
        }
      >
        <Row gutter={16}>
          <Col span={16}>
            <Card title="Volume by Currency" type="inner">
              <Table
                columns={volumeColumns}
                dataSource={data.byCurrency}
                rowKey="currency"
                loading={loading}
                pagination={false}
                size="small"
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card title="Trends vs Last Month" type="inner">
              <Table
                columns={trendColumns}
                dataSource={data.trends}
                rowKey="currency"
                loading={loading}
                pagination={false}
                size="small"
              />
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  )
}
