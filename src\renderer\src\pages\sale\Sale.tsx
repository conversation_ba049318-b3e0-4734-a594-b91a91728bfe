import { useState } from 'react'
import { Layout, Input, Button, Card, Tabs } from 'antd'
import { FiPlus } from 'react-icons/fi'
import { SaleList } from './components/SaleList'
import { CreateSale } from './components/CreateSale'
import { TransitionWrapper } from '@/renderer/components'

const { Content } = Layout
const { Search } = Input
const { TabPane } = Tabs

const Sale = () => {
  const [search, setSearch] = useState('')
  const [isCreateMode, setIsCreateMode] = useState(false)
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  return (
    <Layout className="relative h-full">
      <TransitionWrapper isVisible={!isCreateMode} direction="left">
        <Content className="p-6">
          <Card>
            <div className="mb-6 flex items-center justify-between">
              <div>
                <h1 className="mb-1 text-2xl font-semibold">Sales</h1>
                <p className="text-gray-500">Manage your sales and transactions</p>
              </div>
              <div className="flex items-center gap-4">
                <Search
                  placeholder="Search sales..."
                  allowClear
                  onChange={(e) => setSearch(e.target.value)}
                  className="w-64"
                />
                <Button
                  type="primary"
                  icon={<FiPlus />}
                  onClick={() => setIsCreateMode(true)}
                  className="bg-blue-500 hover:!bg-blue-600"
                >
                  Create Sale
                </Button>
              </div>
            </div>

            <SaleList searchQuery={search} refreshTrigger={refreshTrigger} />
          </Card>
        </Content>
      </TransitionWrapper>

      <TransitionWrapper isVisible={isCreateMode} direction="right">
        <CreateSale onClose={() => setIsCreateMode(false)} setRefreshTrigger={setRefreshTrigger} />
      </TransitionWrapper>
    </Layout>
  )
}

export default Sale
