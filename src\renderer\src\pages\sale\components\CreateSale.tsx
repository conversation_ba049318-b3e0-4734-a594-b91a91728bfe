import { Form, Button, Select, InputNumber, Radio, message, DatePicker } from 'antd'
import { FiArrowLeft } from 'react-icons/fi'
import { useState, useEffect } from 'react'
import { useApi } from '@/renderer/hooks'
import { containerApi, saleApi } from '@/renderer/services'
import type {
  CreateWalkInSaleData,
  CreateAccountSaleData,
  GetAvailableItemsResponse,
  GetContainerByStockIdResponse,
  GetContainerByStockIdParams,
  ItemType,
  TransactionLocation
} from '@/common/types'
import { useAccountContext, useBankContext, useTheme } from '@/renderer/contexts'
import { IRootState } from '@/renderer/redux'
import { useSelector } from 'react-redux'
import { ContainerDetailsCard } from './createSaleComponents'
import { handleDatePickerValue } from '@/renderer/utils'
import { AccountDetailsCard, LocationDetailsCard } from '@/renderer/components'

interface CreateSaleProps {
  onClose: () => void
  setRefreshTrigger: (value: any) => void
}

export const CreateSale = ({ onClose, setRefreshTrigger }: CreateSaleProps) => {
  const [form] = Form.useForm()
  const [isLoading, setIsLoading] = useState(false)
  const [selectedItem, setSelectedItem] = useState<any>(null)
  const [saleType, setSaleType] = useState<'WALK_IN' | 'ACCOUNT'>('WALK_IN')
  const [selectedItemType, setSelectedItemType] = useState<ItemType>('CAR')
  const [paymentDestination, setPaymentDestination] = useState<TransactionLocation>('CASH_VAULT')
  const [selectedBankId, setSelectedBankId] = useState<string | undefined>(undefined)

  const [refreshAvailableItems, setRefreshAvailableItems] = useState(0)

  const [container, setContainer] = useState(null)

  const { accounts } = useAccountContext()
  const { banks } = useBankContext()
  const selectedAccount = Form.useWatch('accountId', form)
  const user = useSelector((state: IRootState) => state.user.data)

  const { isDarkMode } = useTheme()

  const {
    data: availableItems,
    request: fetchAvailableItems,
    isLoading: isFetchingAvailableItems
  } = useApi<GetAvailableItemsResponse, [ItemType]>(saleApi.getAvailableItems)

  useEffect(() => {
    fetchAvailableItems(selectedItemType)
  }, [selectedItemType, refreshAvailableItems])

  useEffect(() => {
    const fetchContainerByStockId = async () => {
      const response = await saleApi.getContainerByStockId({
        itemId: selectedItem?.id,
        itemType: selectedItemType
      })
      if (response.error.error || response.data.error) {
        message.error(response.error.message || response.data.error.message)
        return
      }
      setContainer(response.data.data.container)
    }

    if (selectedItem && selectedItemType) {
      fetchContainerByStockId()
    } else {
      setContainer(null)
    }
  }, [selectedItem, selectedItemType])

  // Account fetching is now handled by the AccountDetailsCard component

  const handleItemSelect = (itemId: string) => {
    const selectedOption = availableItems?.options.find((option) => option.value === itemId)
    if (selectedOption) {
      const quantityMatch = selectedOption.label.match(/- (\d+(\.\d+)?)$/)
      const quantity = quantityMatch ? parseFloat(quantityMatch[1]) : undefined
      setSelectedItem({ id: itemId, quantity })

      if (selectedItemType !== 'CAR' && quantity !== undefined) {
        form.setFieldsValue({ maxQuantity: quantity })
      }
    }
  }

  const handleSubmit = async (values: any) => {
    setIsLoading(true)
    if (!user?.id) {
      message.error('User session not found')
      return
    }

    const baseData = {
      itemType: selectedItemType,
      itemId: values.itemId,
      totalAmount: values.amount,
      quantity: selectedItemType === 'CAR' ? 1 : values.quantity,
      userId: user?.id,
      date: handleDatePickerValue(values.date?.toDate())
    }

    if (saleType === 'WALK_IN') {
      const walkInData: CreateWalkInSaleData = {
        ...baseData,
        paymentDestination,
        bankAccountId: paymentDestination === 'BANK_ACCOUNT' ? values.bankAccountId : undefined
      }

      console.log(walkInData)

      const response = await saleApi.createWalkInSale(walkInData)

      console.log(response)

      if (response.error.error || response.data.error) {
        message.error(response.error.message || response.data.error.message)
        setIsLoading(false)
        return
      }
    } else {
      const accountData: CreateAccountSaleData = {
        ...baseData,
        accountId: values.accountId
      }

      const response = await saleApi.createAccountSale(accountData)
      if (response.error.error || response.data.error) {
        message.error(response.error.message || response.data.error.message)
        setIsLoading(false)
        return
      }
    }

    message.success('Sale created successfully')
    form.resetFields()
    setSelectedItem(null)
    setSelectedItemType('CAR')
    setPaymentDestination('CASH_VAULT')
    setSaleType('WALK_IN')
    setSelectedBankId(undefined)
    setIsLoading(false)
    setRefreshAvailableItems((prev: number) => prev + 1)
    setRefreshTrigger((prev: number) => prev + 1)
    onClose()
  }

  return (
    <div className="h-full overflow-y-auto p-6">
      <div className="mb-6 flex items-center">
        <Button icon={<FiArrowLeft />} onClick={onClose} className="mr-4" />
        <h1 className="text-2xl font-semibold">Create New Sale</h1>
      </div>

      <div className="flex gap-6">
        <div className="w-1/2">
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            className={`rounded-lg p-6 shadow-sm ${isDarkMode ? 'bg-neutral-900' : 'bg-white'}`}
          >
            <h2 className="mb-4 text-lg font-medium">Sale Information</h2>

            <Form.Item label="Sale Type" className="mb-6">
              <Radio.Group
                value={saleType}
                onChange={(e) => {
                  setSaleType(e.target.value)
                  form.resetFields(['accountId'])
                }}
              >
                <Radio.Button value="WALK_IN">Walk-in Sale</Radio.Button>
                <Radio.Button value="ACCOUNT">Account Sale</Radio.Button>
              </Radio.Group>
            </Form.Item>

            <div className="grid grid-cols-2 gap-4">
              <Form.Item name="itemType" label="Item Type" initialValue="CAR">
                <Select
                  onChange={(value) => {
                    setSelectedItemType(value)
                    setSelectedItem(null)
                    form.resetFields(['itemId', 'quantity'])
                  }}
                >
                  <Select.Option value="CAR">Car</Select.Option>
                  <Select.Option value="CARPART">Car Part</Select.Option>
                  <Select.Option value="ELECTRONIC">Electronic</Select.Option>
                  <Select.Option value="SCRAP">Scrap</Select.Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="itemId"
                label="Select Item"
                rules={[{ required: true, message: 'Please select an item' }]}
              >
                <Select
                  placeholder="Select item"
                  loading={isFetchingAvailableItems}
                  showSearch
                  optionFilterProp="children"
                  options={availableItems?.options}
                  onChange={handleItemSelect}
                  filterOption={(input, option) =>
                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                />
              </Form.Item>
            </div>

            {selectedItemType !== 'CAR' && selectedItem?.quantity !== undefined && (
              <Form.Item
                name="quantity"
                label="Quantity"
                rules={[
                  { required: true, message: 'Please enter quantity' },
                  { type: 'number', min: 0.01, message: 'Quantity must be greater than 0' },
                  {
                    type: 'number',
                    max: selectedItem.quantity,
                    message: `Maximum available quantity is ${selectedItem.quantity}`
                  }
                ]}
              >
                <InputNumber
                  className="w-full"
                  min={0.01}
                  max={selectedItem.quantity}
                  step={selectedItemType === 'SCRAP' ? 0.01 : 1}
                  precision={selectedItemType === 'SCRAP' ? 2 : 0}
                  parser={(value) => {
                    const parsedValue = Number(value!.toString().replace(/[^\d.-]/g, ''))
                    // Ensure the value is at least 0.01 and not more than max
                    if (parsedValue < 0.01) return 0.01
                    if (parsedValue > selectedItem.quantity) return selectedItem.quantity
                    return parsedValue
                  }}
                  onKeyDown={(e) => {
                    // Prevent the minus key (both on main keyboard and numpad)
                    if (e.key === '-') {
                      e.preventDefault()
                    }
                  }}
                />
              </Form.Item>
            )}

            <Form.Item label="Date" name="date" className="col-span-2" rules={[{ required: true }]}>
              <DatePicker className="w-full" format="YYYY-MM-DD" placeholder="Select date" />
            </Form.Item>

            <div
              className={`mt-6 rounded-lg p-6 shadow-inner ${isDarkMode ? 'bg-black' : 'bg-slate-50'}`}
            >
              <h2 className="mb-4 text-lg font-medium">Payment Details</h2>

              <Form.Item
                name="amount"
                label="Sale Amount"
                rules={[
                  { required: true, message: 'Please enter sale amount' },
                  { type: 'number', min: 1, message: 'Amount must be greater than 0' }
                ]}
              >
                <InputNumber
                  className="w-full"
                  min={Number(1)}
                  formatter={(value) => `Rs ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => {
                    const parsedValue = Number(value!.replace(/Rs\s?|(,*)/g, ''))
                    // Ensure the value is at least 1
                    return parsedValue < 1 ? 1 : parsedValue
                  }}
                  placeholder="Enter sale amount"
                  onKeyDown={(e) => {
                    // Prevent the minus key (both on main keyboard and numpad)
                    if (e.key === '-') {
                      e.preventDefault()
                    }
                  }}
                />
              </Form.Item>

              {saleType === 'WALK_IN' ? (
                <>
                  <Form.Item label="Payment Destination">
                    <Select
                      value={paymentDestination}
                      onChange={(value: TransactionLocation) => {
                        setPaymentDestination(value)
                        if (value !== 'BANK_ACCOUNT') {
                          setSelectedBankId(undefined)
                          form.resetFields(['bankAccountId'])
                        }
                      }}
                    >
                      {/* <Select.Option value="SMALL_COUNTER">Small Counter</Select.Option> */}
                      <Select.Option value="CASH_VAULT">Cash Vault</Select.Option>
                      <Select.Option value="BANK_ACCOUNT">Bank Account</Select.Option>
                    </Select>
                  </Form.Item>

                  {paymentDestination === 'BANK_ACCOUNT' && (
                    <Form.Item
                      name="bankAccountId"
                      label="Select Bank Account"
                      rules={[{ required: true, message: 'Please select a bank account' }]}
                    >
                      <Select
                        placeholder="Select bank account"
                        options={banks}
                        onChange={(value: string) => setSelectedBankId(value)}
                      ></Select>
                    </Form.Item>
                  )}
                </>
              ) : (
                <Form.Item
                  name="accountId"
                  label="Select Account"
                  rules={[{ required: true, message: 'Please select an account' }]}
                >
                  <Select
                    placeholder="Select account"
                    showSearch
                    optionFilterProp="children"
                    options={accounts}
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                  ></Select>
                </Form.Item>
              )}
            </div>

            <div className="mt-6 flex justify-end gap-4">
              <Button onClick={onClose}>Cancel</Button>
              <Button type="primary" htmlType="submit" loading={isLoading}>
                Create Sale
              </Button>
            </div>
          </Form>
        </div>

        <div className="w-1/2">
          {container && <ContainerDetailsCard container={container} />}
          {saleType === 'ACCOUNT' && selectedAccount && (
            <AccountDetailsCard accountId={selectedAccount} />
          )}
          {saleType === 'WALK_IN' &&
            (paymentDestination === 'CASH_VAULT' ? (
              <LocationDetailsCard locationType="CASH_VAULT" />
            ) : (
              paymentDestination === 'BANK_ACCOUNT' &&
              selectedBankId && (
                <LocationDetailsCard locationType="BANK_ACCOUNT" bankId={selectedBankId} />
              )
            ))}
        </div>
      </div>
    </div>
  )
}
