import { Container } from '@prisma/client';

export interface CreatePartnerData {
    name: string;
}

export interface GetPartnersParams {
    page: number;
    limit: number;
    includeDeleted?: boolean;
    search?: string;
}

export interface PartnerWithContainers {
    id: string;
    name: string;
    containers: Container[];
    createdAt: Date;
    isDeleted: boolean;
    deleteReason?: string;
    deletedAt?: Date;
}

export interface GetPartnersResponse {
    partners: PartnerWithContainers[]
    total: number
}

