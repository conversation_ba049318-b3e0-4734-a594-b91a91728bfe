import { Form, Input, Button, Select, InputN<PERSON>ber, App } from 'antd'
import { FiArrowLeft } from 'react-icons/fi'
import { useApi } from '@/renderer/hooks'
import { accountsApi } from '@/renderer/services'
import type { CreateAccountData } from '@/common/types'
import { useState } from 'react'
import { useAccountContext } from '@/renderer/contexts/AccountContext'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { useTheme } from '@/renderer/contexts'

type AccountType = 'CUSTOMER' | 'TENANT' | 'BOTH'

interface AddAccountProps {
  onClose: () => void
  setRefreshTrigger: (value: any) => void
}

export const AddAccount = ({ onClose, setRefreshTrigger }: AddAccountProps) => {
  const [isLoading, setIsLoading] = useState(false)

  const { message } = App.useApp()
  const { refreshAccounts } = useAccountContext()

  const { isDarkMode } = useTheme()

  const [form] = Form.useForm()

  const user = useSelector((state: IRootState) => state.user.data)

  const handleSubmit = async (values: CreateAccountData) => {
    setIsLoading(true)
    const response = await accountsApi.createAccount({
      ...values,
      userId: user?.id || ''
    })
    console.log(values)
    console.log(response)
    setIsLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }
    message.success('Account created successfully')
    form.resetFields()
    refreshAccounts()
    setRefreshTrigger((prev) => prev + 1)
    onClose()
  }

  const accountTypes: { label: string; value: AccountType }[] = [
    { label: 'Customer', value: 'CUSTOMER' },
    { label: 'Tenant', value: 'TENANT' }
  ]

  return (
    <div className="h-full overflow-y-auto p-6">
      <div className="mb-6 flex items-center">
        <Button icon={<FiArrowLeft />} onClick={onClose} className="mr-4" />
        <h1 className="text-2xl font-semibold">Add New Account</h1>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        className={`m-auto max-w-2xl rounded-lg p-6 shadow-lg ${
          isDarkMode ? 'bg-neutral-900' : 'bg-white'
        }`}
        initialValues={{
          type: 'CUSTOMER',
          openingBalances: { USD: 0, PKR: 0, AED: 0, AFN: 0 }
        }}
      >
        <Form.Item
          name="name"
          label="Account Name"
          rules={[
            { required: true, message: 'Please enter account name' },
            { min: 3, message: 'Name must be at least 3 characters' }
          ]}
        >
          <Input placeholder="Enter account name" />
        </Form.Item>

        <Form.Item
          name="type"
          label="Account Type"
          rules={[{ required: true, message: 'Please select account type' }]}
        >
          <Select options={accountTypes} />
        </Form.Item>

        <Form.Item
          name="phoneNumber"
          label="Phone Number"
          rules={[{ required: true, message: 'Please enter phone number' }]}
        >
          <Input placeholder="Enter phone number" />
        </Form.Item>

        <Form.Item name="address" label="Address">
          <Input.TextArea rows={3} placeholder="Enter address" />
        </Form.Item>

        <div className="mb-4">
          <h3 className="mb-4 text-lg font-medium">Opening Balances</h3>
          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name={['openingBalances', 'PKR']}
              label="PKR Balance"
              help="Enter negative value for credit"
            >
              <InputNumber
                className="w-full"
                formatter={(value) => `Rs ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={(value) => value!.replace(/Rs\s?|(,*)/g, '')}
                placeholder="Enter PKR balance"
              />
            </Form.Item>

            <Form.Item
              name={['openingBalances', 'USD']}
              label="USD Balance"
              help="Enter negative value for credit"
            >
              <InputNumber
                className="w-full"
                formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
                placeholder="Enter USD balance"
              />
            </Form.Item>

            <Form.Item
              name={['openingBalances', 'AED']}
              label="AED Balance"
              help="Enter negative value for credit"
            >
              <InputNumber
                className="w-full"
                placeholder="Enter AED balance"
                formatter={(value) => `AED ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={(value) => value!.replace(/AED\s?|(,*)/g, '')}
              />
            </Form.Item>

            <Form.Item
              name={['openingBalances', 'AFN']}
              label="AFN Balance"
              help="Enter negative value for credit"
            >
              <InputNumber
                className="w-full"
                placeholder="Enter AFN balance"
                formatter={(value) => `AFN ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={(value) => value!.replace(/AFN\s?|(,*)/g, '')}
              />
            </Form.Item>
          </div>
        </div>

        <div className="mt-8 flex justify-end">
          <Button onClick={onClose} className="mr-4">
            Cancel
          </Button>
          <Button type="primary" htmlType="submit" loading={isLoading}>
            Create Account
          </Button>
        </div>
      </Form>
    </div>
  )
}
