import { Component, ErrorInfo, ReactNode } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Typography } from 'antd'

const { Title, Text } = Typography

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    }
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error', error, errorInfo)
    this.setState({
      error,
      errorInfo
    })
  }

  resetError = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <Card className="m-4 mx-auto max-w-xl">
          <Alert
            message="Something went wrong"
            description="An error occurred in this component."
            type="error"
            showIcon
            className="mb-4"
          />

          <Title level={4}>Error details:</Title>
          <Text type="danger">{this.state.error?.toString()}</Text>

          {this.state.errorInfo && (
            <div className="mt-4">
              <Title level={5}>Component Stack:</Title>
              <pre className="overflow-auto rounded bg-gray-100 p-4 text-xs dark:bg-gray-800">
                {this.state.errorInfo.componentStack}
              </pre>
            </div>
          )}

          <div className="mt-6 flex justify-end">
            <Button type="primary" onClick={this.resetError}>
              Try Again
            </Button>
          </div>
        </Card>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary
