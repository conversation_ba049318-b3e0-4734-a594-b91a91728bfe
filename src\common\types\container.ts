import { Container, ItemStatus, ItemType } from '@prisma/client';

export interface CreateCarData {
    chassisNumber: string;
    modelNumber: string;
    name: string;
    color: string;
}

export interface CreateCarPartData {
    name: string;
    quantity: number;  // This will be used for both initial and current quantity
}

export interface CreateElectronicData {
    name: string;
    quantity: number;  // This will be used for both initial and current quantity
}

export interface CreateScrapData {
    description: string;
    quantity: number;  // This will be used for both initial and current quantity
}

export interface CreateContainerData {
    containerNumber: string;
    openedAt: Date;
    driverExpense: number;
    taxes: number;
    containerCost: number;
    routeExpense: number;
    fieldRent: number;
    partnerId?: string;
    cars?: CreateCarData[];
    carParts?: CreateCarPartData[];
    electronics?: CreateElectronicData[];
    scraps?: CreateScrapData[];
    createdById: string;
}

export interface GetContainersParams {
    page: number;
    limit: number;
    includeDeleted?: boolean;
    partnerId?: string;
    search?: string;
    startDate?: Date;
    endDate?: Date;
    orderBy?: 'asc' | 'desc';
}

export interface GetContainerStockParams {
    containerId: string;
    page: number;
    limit: number;
    itemType?: 'CAR' | 'CAR_PART' | 'ELECTRONIC' | 'SCRAP';
}

interface ContainerItem {
    id: string;
    status: ItemStatus;
    sale?: {
        totalAmount: number;
    };
}

interface Car extends ContainerItem {
    chassisNumber: string;
    modelNumber: string;
    name: string;
    color: string;
}

interface QuantityItem extends ContainerItem {
    name: string;
    quantity: number;
}

interface Scrap extends ContainerItem {
    description: string;
    quantity: number;
}

export interface ContainerByIdResponse {
    id: string;
    containerNumber: string;
    openedAt: Date;
    driverExpense: number;
    taxes: number;
    containerCost: number;
    routeExpense: number;
    fieldRent: number;
    cars: Car[];
    carParts: QuantityItem[];
    electronics: QuantityItem[];
    scraps: Scrap[];
    partner?: {
        name: string;
    };
}

export interface ContainerSummaryResponse {
    totalExpenses: number;
    totalSales: number;
    profit: number;
    salesMinusExpenses: number;
    itemsSummary: {
        cars: {
            total: number;
            sold: number;
        };
        carParts: {
            total: number;
            sold: number;
        };
        electronics: {
            total: number;
            sold: number;
        };
        scraps: {
            total: number;
            sold: number;
        };
    };
}

export interface GetContainersResponse {
    containers: (Container & {
        partner?: {
            id: string;
            name: string;
        };
        totalSales: number;
        salesMinusExpenses: number;
    })[];
    total: number;
}

export interface GetContainerByStockIdParams {
    itemId: string;
    itemType: ItemType;
}

export interface GetContainerByStockIdResponse {
    container: Container;
    itemDetails: any;
}

// Define an interface for the PDF data
export interface ContainerPDFData {
    containerNumber: string;
    openedAt: Date;
    totalCost: number;
    salesMinusExpenses: number;
    partner: string | null;
}