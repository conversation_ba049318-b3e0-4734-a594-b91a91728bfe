import { useEffect, useState } from 'react'
import { Table, Tag, Button, Space, App, Popconfirm, Typography } from 'antd'
import { DeleteOutlined, EyeOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { useApi } from '@/renderer/hooks'
import { manualEntryApi } from '@/renderer/services'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import type {
  ManualEntryItem,
  ManualEntriesResponse,
  ManualEntryFilters
} from '@/common/types/manualEntry'
import { ManualEntryDetailsModal } from './ManualEntryDetailsModal'
import { VoidManualEntryModal } from './VoidManualEntryModal'
import dayjs from 'dayjs'

const { Text } = Typography

interface ManualEntryListProps {
  filters: ManualEntryFilters
  refreshTrigger: number
  onRefresh: () => void
}

export const ManualEntryList = ({ filters, refreshTrigger, onRefresh }: ManualEntryListProps) => {
  const { message } = App.useApp()
  const currentUser = useSelector((state: IRootState) => state.user.data)

  const [selectedEntry, setSelectedEntry] = useState<ManualEntryItem | null>(null)
  const [detailsModalOpen, setDetailsModalOpen] = useState(false)
  const [voidModalOpen, setVoidModalOpen] = useState(false)
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  })

  const { data, isLoading, request } = useApi<ManualEntriesResponse, any>(
    manualEntryApi.getManualEntries
  )

  useEffect(() => {
    loadEntries()
  }, [refreshTrigger, filters, pagination.current, pagination.pageSize])

  const loadEntries = async () => {
    const params = {
      ...filters,
      page: pagination.current,
      pageSize: pagination.pageSize
    }

    await request(params)
  }

  useEffect(() => {
    if (data) {
      setPagination((prev) => ({
        ...prev,
        total: data.pagination.total
      }))
    }
  }, [data])

  const handleTableChange = (paginationConfig: any) => {
    setPagination({
      current: paginationConfig.current,
      pageSize: paginationConfig.pageSize,
      total: paginationConfig.total
    })
  }

  const handleViewDetails = (entry: ManualEntryItem) => {
    setSelectedEntry(entry)
    setDetailsModalOpen(true)
  }

  const handleVoidEntry = (entry: ManualEntryItem) => {
    setSelectedEntry(entry)
    setVoidModalOpen(true)
  }

  const handleVoidSuccess = () => {
    setVoidModalOpen(false)
    setSelectedEntry(null)
    message.success('Manual entry voided successfully')
    onRefresh()
  }

  const getTargetDisplay = (entry: ManualEntryItem) => {
    if (entry.account) {
      return (
        <div>
          <div className="font-medium">{entry.account.name}</div>
          <Text type="secondary" className="text-xs">
            {entry.account.type}
          </Text>
        </div>
      )
    }

    if (entry.bankAccount) {
      return (
        <div>
          <div className="font-medium">{entry.bankAccount.bankName}</div>
          <Text type="secondary" className="text-xs">
            {entry.bankAccount.accountNumber}
          </Text>
        </div>
      )
    }

    if (entry.targetType) {
      return <Tag color="blue">Cash Vault</Tag>
    }

    return '-'
  }

  const columns: ColumnsType<ManualEntryItem> = [
    {
      title: 'Date',
      dataIndex: 'transactionDate',
      key: 'transactionDate',
      width: 120,
      render: (date: string) => dayjs(date).format('DD/MM/YYYY'),
      sorter: true
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      width: 140,
      align: 'right',
      render: (amount: number, record: ManualEntryItem) => (
        <div>
          <span className={record.entryType === 'DEBIT' ? 'text-red-600' : 'text-green-600'}>
            {record.entryType === 'DEBIT' ? '-' : '+'}
            {amount.toLocaleString()}
          </span>
          <div className="text-xs text-gray-500">{record.currency.code}</div>
        </div>
      )
    },
    {
      title: 'Type',
      dataIndex: 'entryType',
      key: 'entryType',
      width: 80,
      render: (type: string) => <Tag color={type === 'DEBIT' ? 'red' : 'green'}>{type}</Tag>
    },
    {
      title: 'Target',
      key: 'target',
      width: 200,
      render: (_, record: ManualEntryItem) => getTargetDisplay(record)
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (description: string) => description || '-'
    },
    {
      title: 'Created By',
      key: 'createdBy',
      width: 120,
      render: (_, record: ManualEntryItem) => record.createdBy.name
    },
    {
      title: 'Status',
      key: 'status',
      width: 100,
      render: (_, record: ManualEntryItem) => {
        if (record.isDeleted) {
          return <Tag color="red">Deleted</Tag>
        }
        return <Tag color="green">Active</Tag>
      }
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      fixed: 'right',
      render: (_, record: ManualEntryItem) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetails(record)}
          />
          {!record.isDeleted && (
            <Popconfirm
              title="Void Manual Entry"
              description="Are you sure you want to void this manual entry? This action will reverse the balance changes."
              onConfirm={() => handleVoidEntry(record)}
              okText="Yes, Void"
              cancelText="Cancel"
              okButtonProps={{ danger: true }}
            >
              <Button type="text" size="small" icon={<DeleteOutlined />} danger />
            </Popconfirm>
          )}
        </Space>
      )
    }
  ]

  return (
    <>
      <Table
        columns={columns}
        dataSource={data?.entries || []}
        loading={isLoading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} entries`
        }}
        onChange={handleTableChange}
        rowKey="id"
        scroll={{ x: 1200 }}
        size="small"
        sticky
        virtual
      />

      <ManualEntryDetailsModal
        entry={selectedEntry}
        open={detailsModalOpen}
        onClose={() => {
          setDetailsModalOpen(false)
          setSelectedEntry(null)
        }}
      />

      <VoidManualEntryModal
        entry={selectedEntry}
        open={voidModalOpen}
        onClose={() => {
          setVoidModalOpen(false)
          setSelectedEntry(null)
        }}
        onSuccess={handleVoidSuccess}
        currentUserId={currentUser?.id || ''}
      />
    </>
  )
}
