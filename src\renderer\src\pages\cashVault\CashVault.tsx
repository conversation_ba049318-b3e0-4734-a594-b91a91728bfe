import { Ta<PERSON>, Card, Spin, Space } from 'antd'
import { useApi } from '@/renderer/hooks'
import { cashVaultApi } from '@/renderer/services'
import { BalanceDisplay } from './components/BalanceDisplay'
// import { BalanceActions } from './components/BalanceActions'
import { TransactionHistory } from './components/TransactionHistory'
import { ReconciliationView } from './components/ReconciliationView'
import { CashVaultStatement } from './components/CashVaultStatement'
import { InitializeCounterOrVault } from './components/InitializeCounterOrVault'
import { useEffect, useState } from 'react'

const CashVault = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const {
    data: cashVaultBalances,
    error: cashVaultError,
    request: cashVaultRequest,
    isLoading: cashVaultLoading
  } = useApi(cashVaultApi.getBalances)

  useEffect(() => {
    cashVaultRequest()
  }, [refreshTrigger])

  if (cashVaultLoading) {
    return <Spin />
  }

  const cashVaultInitialized = !cashVaultError

  return (
    <div className="flex h-full flex-col gap-4 p-6">
      <div>
        {!cashVaultInitialized ? (
          <InitializeCounterOrVault type="CASH_VAULT" setRefreshTrigger={setRefreshTrigger} />
        ) : (
          <Card>
            <BalanceDisplay type="CASH_VAULT" />
            {/* <BalanceActions type="CASH_VAULT" /> */}
          </Card>
        )}
      </div>

      {cashVaultInitialized && (
        <Tabs
          type="card"
          items={[
            {
              key: 'transactions',
              label: 'Transactions',
              children: <TransactionHistory />
            },
            {
              key: 'statement',
              label: 'Statement',
              children: <CashVaultStatement />
            },
            {
              key: 'reconciliation',
              label: 'Reconciliation',
              children: <ReconciliationView />
            }
          ]}
        />
      )}
    </div>
  )
}

export default CashVault
