import { IRequest } from '../../common';
import { manualEntryService } from '../services';
import {
    CreateManualEntryData,
    GetManualEntriesParams,
    VoidManualEntryParams
} from '../../common/types/manualEntry';

class ManualEntryController {
    async createManualEntry(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as CreateManualEntryData;

        // Validation
        if (!data.amount || data.amount <= 0) {
            throw new Error('Amount is required and must be greater than zero');
        }

        if (!data.entryType) {
            throw new Error('Entry type is required');
        }

        if (!data.transactionDate) {
            throw new Error('Transaction date is required');
        }

        if (!data.createdById) {
            throw new Error('Created by ID is required');
        }

        if (!data.currencyCode) {
            throw new Error('Currency code is required');
        }

        // Validate that exactly one target is specified
        const targetCount = [data.accountId, data.bankAccountId, data.targetType].filter(x => x !== undefined && x !== null).length;
        if (targetCount !== 1) {
            throw new Error('Exactly one target (account, bank account, or target type) must be specified');
        }

        return await manualEntryService.createManualEntry({
            ...data,
            transactionDate: new Date(data.transactionDate)
        });
    }

    async getManualEntries(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const params = req.query as any;

        // Convert date strings to Date objects if provided
        const processedParams: GetManualEntriesParams = {
            page: params.page ? Number(params.page) : undefined,
            pageSize: params.pageSize ? Number(params.pageSize) : undefined,
            startDate: params.startDate ? new Date(params.startDate) : undefined,
            endDate: params.endDate ? new Date(params.endDate) : undefined,
            entryType: params.entryType,
            targetType: params.targetType,
            accountId: params.accountId,
            bankAccountId: params.bankAccountId,
            currencyCode: params.currencyCode,
            status: params.status,
            sortOrder: params.sortOrder,
            search: params.search
        };

        return await manualEntryService.getManualEntries(processedParams);
    }

    async getManualEntryById(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params || {};

        if (!id) {
            throw new Error('Manual entry ID is required');
        }

        const entry = await manualEntryService.getManualEntryById(id);

        if (!entry) {
            throw new Error('Manual entry not found');
        }

        return entry;
    }

    async voidManualEntry(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as VoidManualEntryParams;

        if (!data.id) {
            throw new Error('Manual entry ID is required');
        }

        if (!data.deletedById) {
            throw new Error('Deleted by ID is required');
        }

        if (!data.deletionReason || data.deletionReason.trim() === '') {
            throw new Error('Deletion reason is required');
        }

        return await manualEntryService.voidManualEntry(data);
    }


}

export const manualEntryController = new ManualEntryController();