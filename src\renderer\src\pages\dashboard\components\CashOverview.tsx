import { useEffect } from 'react'
import { Typography, Space, Button, Alert, Skeleton, Row, Col, Statistic } from 'antd'
import { useApi } from '@/renderer/hooks'
import { dashboardApi } from '@/renderer/services'
import { CashOverview as CashOverviewType } from '@/common/types'
import { FaMoneyBillWave, FaSync } from 'react-icons/fa'
import { formatCurrency } from '@/renderer/utils'
import { useTheme } from '@/renderer/contexts'

const { Title } = Typography

const CashOverview = () => {
  const { isDarkMode } = useTheme()

  const {
    data,
    isLoading,
    error,
    request: fetchCashOverview
  } = useApi<CashOverviewType, []>(dashboardApi.getCashOverview)

  useEffect(() => {
    fetchCashOverview()
  }, [])

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <FaMoneyBillWave className="text-2xl text-green-600" />
            <Title level={5} className="!mb-0">
              Cash Overview
            </Title>
          </Space>
          <Button icon={<FaSync />} loading />
        </Space>
        <Skeleton active paragraph={{ rows: 4 }} />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <FaMoneyBillWave className="text-2xl text-green-600" />
            <Title level={5} className="!mb-0">
              Cash Overview
            </Title>
          </Space>
          <Button icon={<FaSync />} onClick={fetchCashOverview} />
        </Space>
        <Alert
          message="Error"
          description="Failed to load cash overview data"
          type="error"
          showIcon
        />
      </div>
    )
  }

  if (!data) return null

  return (
    <div className="flex flex-col gap-4">
      <Space className="flex justify-between">
        <Space>
          <FaMoneyBillWave className="text-2xl text-green-600" />
          <Title level={5} className="!mb-0">
            Cash Overview
          </Title>
        </Space>
        <Button icon={<FaSync />} onClick={fetchCashOverview} />
      </Space>

      <Row gutter={[16, 16]}>
        {/* Small Counter */}
        <Col span={24}>
          <div className={`rounded-lg p-4 shadow-md ${isDarkMode ? 'bg-gray-800' : 'bg-slate-50'}`}>
            <Title level={5}>Small Counter</Title>
            <Row gutter={[16, 16]}>
              <Col span={12} sm={6}>
                <Statistic
                  title="PKR"
                  value={data.smallCounter.pkr}
                  formatter={(value) => (value as number).toFixed(2)}
                />
              </Col>
              <Col span={12} sm={6}>
                <Statistic
                  title="USD"
                  value={data.smallCounter.usd}
                  formatter={(value) => (value as number).toFixed(2)}
                />
              </Col>
              <Col span={12} sm={6}>
                <Statistic
                  title="AED"
                  value={data.smallCounter.aed}
                  formatter={(value) => (value as number).toFixed(2)}
                />
              </Col>
              <Col span={12} sm={6}>
                <Statistic
                  title="AFN"
                  value={data.smallCounter.afn}
                  formatter={(value) => (value as number).toFixed(2)}
                />
              </Col>
            </Row>
          </div>
        </Col>

        {/* Cash Vault */}
        <Col span={24}>
          <div className={`rounded-lg p-4 shadow-md ${isDarkMode ? 'bg-gray-800' : 'bg-slate-50'}`}>
            <Title level={5}>Cash Vault</Title>
            <Row gutter={[16, 16]}>
              <Col span={12} sm={6}>
                <Statistic
                  title="PKR"
                  value={data.cashVault.pkr}
                  formatter={(value) => (value as number).toFixed(2)}
                />
              </Col>
              <Col span={12} sm={6}>
                <Statistic
                  title="USD"
                  value={data.cashVault.usd}
                  formatter={(value) => (value as number).toFixed(2)}
                />
              </Col>
              <Col span={12} sm={6}>
                <Statistic
                  title="AED"
                  value={data.cashVault.aed}
                  formatter={(value) => (value as number).toFixed(2)}
                />
              </Col>
              <Col span={12} sm={6}>
                <Statistic
                  title="AFN"
                  value={data.cashVault.afn}
                  formatter={(value) => (value as number).toFixed(2)}
                />
              </Col>
            </Row>
          </div>
        </Col>

        {/* Totals */}
        <Col span={24}>
          <div className={`rounded-lg p-4 shadow-md ${isDarkMode ? 'bg-gray-800' : 'bg-slate-50'}`}>
            <Title level={5}>Total Overview</Title>
            <Row gutter={[16, 16]}>
              <Col span={8}>
                <Statistic
                  title="Total PKR"
                  value={data.totalPKR}
                  formatter={(value) => (value as number).toFixed(2)}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="Bank Accounts"
                  value={data.bankAccounts}
                  formatter={(value) => (value as number).toFixed(2)}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="Total by Location"
                  value={
                    data.totalByLocation.smallCounter +
                    data.totalByLocation.cashVault +
                    data.totalByLocation.bankAccounts
                  }
                  formatter={(value) => (value as number).toFixed(2)}
                />
              </Col>
            </Row>
          </div>
        </Col>
      </Row>
    </div>
  )
}

export default CashOverview
