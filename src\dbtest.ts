import { PrismaClient } from '@prisma/client';
import { faker } from '@faker-js/faker';
import { AccountType, ItemStatus, ItemType, PaymentType, TransactionCategory, TransactionLocation, TransactionType } from '@prisma/client';

// Create a new Prisma client instance
const prisma = new PrismaClient();

// Utility function to get random number between min and max
const randomNumber = (min: number, max: number) => Math.floor(Math.random() * (max - min + 1)) + min;
const randomFloat = (min: number, max: number) => Number((Math.random() * (max - min) + min).toFixed(2));

// Function to add containers with random data
async function addContainers(count: number, batchSize = 100) {
    console.time('addContainers');
    const batches = Math.ceil(count / batchSize);
    let totalCreated = 0;

    // Get the highest existing container number
    const lastContainer = await prisma.container.findFirst({
        orderBy: { containerNumber: 'desc' }
    });

    // Extract the number from the last container number or start from 0
    const lastNumber = lastContainer
        ? parseInt(lastContainer.containerNumber.replace('CONT', ''))
        : 0;

    for (let b = 0; b < batches; b++) {
        const batchCount = Math.min(batchSize, count - (b * batchSize));

        for (let i = 0; i < batchCount; i++) {
            const containerNumber = `CONT${(lastNumber + b * batchSize + i + 1).toString().padStart(6, '0')}`;

            // Create container first
            const container = await prisma.container.create({
                data: {
                    containerNumber,
                    driverExpense: randomFloat(1000, 5000),
                    taxes: randomFloat(5000, 15000),
                    containerCost: randomFloat(50000, 150000),
                    routeExpense: randomFloat(2000, 8000),
                    fieldRent: randomFloat(1000, 3000),
                }
            });

            // Create cars
            const carCount = randomNumber(1, 5);
            await prisma.car.createMany({
                data: Array.from({ length: carCount }, () => ({
                    chassisNumber: faker.string.alphanumeric(17).toUpperCase(),
                    modelNumber: faker.string.alphanumeric(10).toUpperCase(),
                    name: faker.vehicle.model(),
                    color: faker.vehicle.color(),
                    status: ItemStatus.AVAILABLE,
                    containerId: container.id
                }))
            });

            // Create car parts
            const carPartCount = randomNumber(5, 15);
            await prisma.carPart.createMany({
                data: Array.from({ length: carPartCount }, () => ({
                    name: `${faker.vehicle.manufacturer()} ${faker.commerce.productName()}`,
                    initialQuantity: randomNumber(1, 10),
                    quantity: randomNumber(1, 10),
                    status: ItemStatus.AVAILABLE,
                    containerId: container.id
                }))
            });

            // Create electronics
            const electronicCount = randomNumber(3, 10);
            await prisma.electronic.createMany({
                data: Array.from({ length: electronicCount }, () => ({
                    name: faker.commerce.productName(),
                    initialQuantity: randomNumber(1, 20),
                    quantity: randomNumber(1, 20),
                    status: ItemStatus.AVAILABLE,
                    containerId: container.id
                }))
            });

            // Create scraps
            const scrapCount = randomNumber(2, 8);
            await prisma.scrap.createMany({
                data: Array.from({ length: scrapCount }, () => ({
                    description: faker.commerce.productDescription(),
                    initialQuantity: randomFloat(100, 1000),
                    quantity: randomFloat(100, 1000),
                    status: ItemStatus.AVAILABLE,
                    containerId: container.id
                }))
            });
        }

        totalCreated += batchCount;
        console.log(`Created ${totalCreated}/${count} containers with items`);
    }
    console.timeEnd('addContainers');
}

// Function to add accounts
async function addAccounts(count: number, batchSize = 100) {
    console.time('addAccounts');
    const batches = Math.ceil(count / batchSize);
    let totalCreated = 0;

    const accountTypes = [AccountType.CUSTOMER, AccountType.TENANT, AccountType.BOTH];

    for (let b = 0; b < batches; b++) {
        const batchCount = Math.min(batchSize, count - (b * batchSize));
        const accountBatch = Array.from({ length: batchCount }, () => ({
            name: faker.person.fullName(),
            phoneNumber: faker.phone.number(),
            address: faker.location.streetAddress(),
            type: accountTypes[randomNumber(0, 2)]
        }));

        await prisma.account.createMany({
            data: accountBatch
        });

        totalCreated += batchCount;
        console.log(`Created ${totalCreated}/${count} accounts`);
    }
    console.timeEnd('addAccounts');
}

// Function to add expenses
async function addExpenses(count: number, batchSize = 100) {
    console.time('addExpenses');
    const batches = Math.ceil(count / batchSize);
    let totalCreated = 0;

    const expenseCategories = ['Utilities', 'Rent', 'Salaries', 'Maintenance', 'Fuel', 'Office Supplies', 'Marketing'];

    for (let b = 0; b < batches; b++) {
        const batchCount = Math.min(batchSize, count - (b * batchSize));
        const expenseBatch = Array.from({ length: batchCount }, () => ({
            amount: randomFloat(1000, 50000),
            category: expenseCategories[randomNumber(0, expenseCategories.length - 1)],
            description: faker.commerce.productDescription(),
            date: faker.date.recent({ days: 30 })
        }));

        await prisma.expense.createMany({
            data: expenseBatch
        });

        totalCreated += batchCount;
        console.log(`Created ${totalCreated}/${count} expenses`);
    }
    console.timeEnd('addExpenses');
}

// Function to add currencies
async function addCurrencies() {
    console.time('addCurrencies');
    const currencies = [
        { code: 'PKR', name: 'Pakistani Rupee' },
        { code: 'USD', name: 'US Dollar' },
        { code: 'AED', name: 'UAE Dirham' },
        { code: 'AFN', name: 'Afghan Afghani' }
    ];

    // Create currencies
    await prisma.currency.createMany({
        data: currencies,
        skipDuplicates: true
    });

    console.log('Created currencies');
    console.timeEnd('addCurrencies');
}

// Function to add payments with random customers
async function addPayments(count: number, batchSize = 50) {
    console.time('addPayments');
    const batches = Math.ceil(count / batchSize);
    let totalCreated = 0;

    // Get all customer accounts
    const customers = await prisma.account.findMany({
        where: {
            OR: [
                { type: AccountType.CUSTOMER },
                { type: AccountType.BOTH }
            ]
        },
        select: { id: true }
    });

    // Get all currencies
    const currencies = await prisma.currency.findMany({
        select: { id: true }
    });

    if (customers.length === 0) {
        console.log('No customers found. Please add accounts first.');
        return;
    }

    if (currencies.length === 0) {
        console.log('No currencies found. Please add currencies first.');
        return;
    }

    for (let b = 0; b < batches; b++) {
        const batchCount = Math.min(batchSize, count - (b * batchSize));
        const paymentBatch = Array.from({ length: batchCount }, () => ({
            amount: randomFloat(1000, 100000),
            paymentType: Math.random() > 0.5 ? PaymentType.PAID : PaymentType.RECEIVED,
            description: faker.finance.transactionDescription(),
            accountId: customers[randomNumber(0, customers.length - 1)].id,
            currencyId: currencies[randomNumber(0, currencies.length - 1)].id
        }));

        await prisma.payment.createMany({
            data: paymentBatch
        });

        totalCreated += batchCount;
        console.log(`Created ${totalCreated}/${count} payments`);
    }
    console.timeEnd('addPayments');
}

// Function to add currency exchanges
async function addExchanges(count: number, batchSize = 50) {
    console.time('addExchanges');
    const batches = Math.ceil(count / batchSize);
    let totalCreated = 0;

    // Get all currencies
    const currencies = await prisma.currency.findMany();

    if (currencies.length === 0) {
        console.log('No currencies found. Please add currencies first.');
        return;
    }

    for (let b = 0; b < batches; b++) {
        const batchCount = Math.min(batchSize, count - (b * batchSize));
        const exchangeBatch = Array.from({ length: batchCount }, () => {
            const fromCurrencyIndex = randomNumber(0, currencies.length - 1);
            let toCurrencyIndex;
            do {
                toCurrencyIndex = randomNumber(0, currencies.length - 1);
            } while (toCurrencyIndex === fromCurrencyIndex);

            const fromAmount = randomFloat(1000, 100000);
            const exchangeRate = randomFloat(1, 300);
            const toAmount = fromAmount * exchangeRate;

            return {
                fromAmount,
                fromCurrency: currencies[fromCurrencyIndex].code,
                toAmount,
                toCurrency: currencies[toCurrencyIndex].code,
                exchangeRate,
                description: `Exchange from ${currencies[fromCurrencyIndex].code} to ${currencies[toCurrencyIndex].code}`
            };
        });

        await prisma.currencyExchange.createMany({
            data: exchangeBatch
        });

        totalCreated += batchCount;
        console.log(`Created ${totalCreated}/${count} exchanges`);
    }
    console.timeEnd('addExchanges');
}

// Cleanup functions
async function cleanup() {
    console.log('Cleaning up test data...');
    console.time('cleanup');

    try {
        // Disable foreign key checks and truncate all tables in the correct order
        await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 0;`;

        console.log('Truncating all tables...');
        await prisma.$executeRaw`TRUNCATE TABLE Car;`;
        await prisma.$executeRaw`TRUNCATE TABLE CarPart;`;
        await prisma.$executeRaw`TRUNCATE TABLE Electronic;`;
        await prisma.$executeRaw`TRUNCATE TABLE Scrap;`;
        await prisma.$executeRaw`TRUNCATE TABLE Container;`;
        await prisma.$executeRaw`TRUNCATE TABLE Account;`;
        await prisma.$executeRaw`TRUNCATE TABLE Expense;`;
        await prisma.$executeRaw`TRUNCATE TABLE Payment;`;
        await prisma.$executeRaw`TRUNCATE TABLE CurrencyExchange;`;
        await prisma.$executeRaw`TRUNCATE TABLE Currency;`;

        // Re-enable foreign key checks
        await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 1;`;

    } catch (error) {
        console.error('Error during cleanup:', error);
        // Re-enable foreign key checks in case of error
        await prisma.$executeRaw`SET FOREIGN_KEY_CHECKS = 1;`;
    }

    console.timeEnd('cleanup');
    console.log('Cleanup complete');
}

// Main test function
async function runTests() {
    try {
        // Clean up before running tests
        await cleanup();

        // Add test data in sequence
        // Start with very small numbers for testing
        await addCurrencies();       // Add currencies first
        await addAccounts(100);        // 5 accounts
        await addContainers(10);      // 2 containers (with items)
        await addExpenses(50);       // 10 expenses
        await addPayments(50);        // 5 payments
        await addExchanges(50);       // 5 exchanges
    } catch (error) {
        console.error('Test failed:', error);
    } finally {
        await prisma.$disconnect();
    }
}

// Export functions for individual use
export {
    addContainers,
    addAccounts,
    addExpenses,
    addPayments,
    addExchanges,
    addCurrencies,
    cleanup,
    runTests
};

// Run the tests
// runTests().catch(console.error);




await prisma.$disconnect();




















// import { PrismaClient } from '@prisma/client';
// import { faker } from '@faker-js/faker';
// import { AccountType, ItemStatus, ItemType, PaymentType, TransactionCategory, TransactionLocation, TransactionType } from '@prisma/client';

// // Create a new Prisma client instance
// const prisma = new PrismaClient();

// // Utility function to get random number between min and max
// const randomNumber = (min: number, max: number) => Math.floor(Math.random() * (max - min + 1)) + min;
// const randomFloat = (min: number, max: number) => Number((Math.random() * (max - min) + min).toFixed(2));

// // Function to add containers with random data
// async function addContainers(count: number, batchSize = 100) {
//     console.time('addContainers');
//     const batches = Math.ceil(count / batchSize);
//     let totalCreated = 0;

//     // Get the highest existing container number
//     const lastContainer = await prisma.container.findFirst({
//         orderBy: { containerNumber: 'desc' }
//     });

//     // Extract the number from the last container number or start from 0
//     const lastNumber = lastContainer
//         ? parseInt(lastContainer.containerNumber.replace('CONT', ''))
//         : 0;

//     for (let b = 0; b < batches; b++) {
//         const batchCount = Math.min(batchSize, count - (b * batchSize));

//         for (let i = 0; i < batchCount; i++) {
//             const containerNumber = `CONT${(lastNumber + b * batchSize + i + 1).toString().padStart(6, '0')}`;

//             // Create container first
//             const container = await prisma.container.create({
//                 data: {
//                     containerNumber,
//                     driverExpense: randomFloat(1000, 5000),
//                     taxes: randomFloat(5000, 15000),
//                     containerCost: randomFloat(50000, 150000),
//                     routeExpense: randomFloat(2000, 8000),
//                     fieldRent: randomFloat(1000, 3000),
//                 }
//             });

//             // Create cars
//             const carCount = randomNumber(1, 5);
//             await prisma.car.createMany({
//                 data: Array.from({ length: carCount }, () => ({
//                     chassisNumber: faker.string.alphanumeric(17).toUpperCase(),
//                     modelNumber: faker.string.alphanumeric(10).toUpperCase(),
//                     name: faker.vehicle.model(),
//                     color: faker.vehicle.color(),
//                     status: ItemStatus.AVAILABLE,
//                     containerId: container.id
//                 }))
//             });

//             // Create car parts
//             const carPartCount = randomNumber(5, 15);
//             await prisma.carPart.createMany({
//                 data: Array.from({ length: carPartCount }, () => ({
//                     name: `${faker.vehicle.manufacturer()} ${faker.commerce.productName()}`,
//                     initialQuantity: randomNumber(1, 10),
//                     quantity: randomNumber(1, 10),
//                     status: ItemStatus.AVAILABLE,
//                     containerId: container.id
//                 }))
//             });

//             // Create electronics
//             const electronicCount = randomNumber(3, 10);
//             await prisma.electronic.createMany({
//                 data: Array.from({ length: electronicCount }, () => ({
//                     name: faker.commerce.productName(),
//                     initialQuantity: randomNumber(1, 20),
//                     quantity: randomNumber(1, 20),
//                     status: ItemStatus.AVAILABLE,
//                     containerId: container.id
//                 }))
//             });

//             // Create scraps
//             const scrapCount = randomNumber(2, 8);
//             await prisma.scrap.createMany({
//                 data: Array.from({ length: scrapCount }, () => ({
//                     description: faker.commerce.productDescription(),
//                     initialQuantity: randomFloat(100, 1000),
//                     quantity: randomFloat(100, 1000),
//                     status: ItemStatus.AVAILABLE,
//                     containerId: container.id
//                 }))
//             });
//         }

//         totalCreated += batchCount;
//         console.log(`Created ${totalCreated}/${count} containers with items`);
//     }
//     console.timeEnd('addContainers');
// }

// // Function to add accounts
// async function addAccounts(count: number, batchSize = 100) {
//     console.time('addAccounts');
//     const batches = Math.ceil(count / batchSize);
//     let totalCreated = 0;

//     const accountTypes = [AccountType.CUSTOMER, AccountType.TENANT, AccountType.BOTH];

//     for (let b = 0; b < batches; b++) {
//         const batchCount = Math.min(batchSize, count - (b * batchSize));
//         const accountBatch = Array.from({ length: batchCount }, () => ({
//             name: faker.person.fullName(),
//             phoneNumber: faker.phone.number(),
//             address: faker.location.streetAddress(),
//             type: accountTypes[randomNumber(0, 2)]
//         }));

//         await prisma.account.createMany({
//             data: accountBatch
//         });

//         totalCreated += batchCount;
//         console.log(`Created ${totalCreated}/${count} accounts`);
//     }
//     console.timeEnd('addAccounts');
// }

// // Function to add expenses
// async function addExpenses(count: number, batchSize = 100) {
//     console.time('addExpenses');
//     const batches = Math.ceil(count / batchSize);
//     let totalCreated = 0;

//     const expenseCategories = ['Utilities', 'Rent', 'Salaries', 'Maintenance', 'Fuel', 'Office Supplies', 'Marketing'];

//     for (let b = 0; b < batches; b++) {
//         const batchCount = Math.min(batchSize, count - (b * batchSize));
//         const expenseBatch = Array.from({ length: batchCount }, () => ({
//             amount: randomFloat(1000, 50000),
//             category: expenseCategories[randomNumber(0, expenseCategories.length - 1)],
//             description: faker.commerce.productDescription(),
//             date: faker.date.recent({ days: 30 })
//         }));

//         await prisma.expense.createMany({
//             data: expenseBatch
//         });

//         totalCreated += batchCount;
//         console.log(`Created ${totalCreated}/${count} expenses`);
//     }
//     console.timeEnd('addExpenses');
// }

// // Function to add currencies
// async function addCurrencies() {
//     console.time('addCurrencies');
//     const currencies = [
//         { code: 'PKR', name: 'Pakistani Rupee' },
//         { code: 'USD', name: 'US Dollar' },
//         { code: 'AED', name: 'UAE Dirham' },
//         { code: 'AFN', name: 'Afghan Afghani' }
//     ];

//     // Create currencies
//     await prisma.currency.createMany({
//         data: currencies,
//         skipDuplicates: true
//     });

//     console.log('Created currencies');
//     console.timeEnd('addCurrencies');
// }

// // Function to add payments with random customers
// async function addPayments(count: number, batchSize = 50) {
//     console.time('addPayments');
//     const batches = Math.ceil(count / batchSize);
//     let totalCreated = 0;

//     // Get all customer accounts
//     const customers = await prisma.account.findMany({
//         where: {
//             OR: [
//                 { type: AccountType.CUSTOMER },
//                 { type: AccountType.BOTH }
//             ]
//         },
//         select: { id: true }
//     });

//     // Get all currencies
//     const currencies = await prisma.currency.findMany({
//         select: { id: true }
//     });

//     if (customers.length === 0) {
//         console.log('No customers found. Please add accounts first.');
//         return;
//     }

//     if (currencies.length === 0) {
//         console.log('No currencies found. Please add currencies first.');
//         return;
//     }

//     for (let b = 0; b < batches; b++) {
//         const batchCount = Math.min(batchSize, count - (b * batchSize));
//         const paymentBatch = Array.from({ length: batchCount }, () => ({
//             amount: randomFloat(1000, 100000),
//             paymentType: Math.random() > 0.5 ? PaymentType.PAID : PaymentType.RECEIVED,
//             description: faker.finance.transactionDescription(),
//             accountId: customers[randomNumber(0, customers.length - 1)].id,
//             currencyId: currencies[randomNumber(0, currencies.length - 1)].id
//         }));

//         await prisma.payment.createMany({
//             data: paymentBatch
//         });

//         totalCreated += batchCount;
//         console.log(`Created ${totalCreated}/${count} payments`);
//     }
//     console.timeEnd('addPayments');
// }

// // Function to add currency exchanges
// async function addExchanges(count: number, batchSize = 50) {
//     console.time('addExchanges');
//     const batches = Math.ceil(count / batchSize);
//     let totalCreated = 0;

//     // Get all currencies
//     const currencies = await prisma.currency.findMany();

//     if (currencies.length === 0) {
//         console.log('No currencies found. Please add currencies first.');
//         return;
//     }

//     for (let b = 0; b < batches; b++) {
//         const batchCount = Math.min(batchSize, count - (b * batchSize));
//         const exchangeBatch = Array.from({ length: batchCount }, () => {
//             const fromCurrencyIndex = randomNumber(0, currencies.length - 1);
//             let toCurrencyIndex;
//             do {
//                 toCurrencyIndex = randomNumber(0, currencies.length - 1);
//             } while (toCurrencyIndex === fromCurrencyIndex);

//             const fromAmount = randomFloat(1000, 100000);
//             const exchangeRate = randomFloat(1, 300);
//             const toAmount = fromAmount * exchangeRate;

//             return {
//                 fromAmount,
//                 fromCurrency: currencies[fromCurrencyIndex].code,
//                 toAmount,
//                 toCurrency: currencies[toCurrencyIndex].code,
//                 exchangeRate,
//                 description: `Exchange from ${currencies[fromCurrencyIndex].code} to ${currencies[toCurrencyIndex].code}`
//             };
//         });

//         await prisma.currencyExchange.createMany({
//             data: exchangeBatch
//         });

//         totalCreated += batchCount;
//         console.log(`Created ${totalCreated}/${count} exchanges`);
//     }
//     console.timeEnd('addExchanges');
// }

// // Cleanup functions
// async function cleanup() {
//     console.log('Cleaning up test data...');
//     console.time('cleanup');

//     // Delete in correct order based on foreign key relationships
//     // First delete all child records
//     console.log('Deleting cars...');
//     await prisma.car.deleteMany({});

//     console.log('Deleting car parts...');
//     await prisma.carPart.deleteMany({});

//     console.log('Deleting electronics...');
//     await prisma.electronic.deleteMany({});

//     console.log('Deleting scraps...');
//     await prisma.scrap.deleteMany({});

//     // Now delete containers since their children are gone
//     console.log('Deleting containers...');
//     await prisma.container.deleteMany({});

//     // Delete other independent tables
//     console.log('Deleting accounts...');
//     await prisma.account.deleteMany({});

//     console.log('Deleting expenses...');
//     await prisma.expense.deleteMany({});

//     console.log('Deleting payments...');
//     await prisma.payment.deleteMany({});

//     console.log('Deleting currency exchanges...');
//     await prisma.currencyExchange.deleteMany({});

//     console.log('Deleting currencies...');
//     await prisma.currency.deleteMany({});

//     console.timeEnd('cleanup');
//     console.log('Cleanup complete');
// }

// // Main test function
// async function runTests() {
//     try {
//         // Clean up before running tests
//         await cleanup();

//         // Add test data in sequence
//         // Start with very small numbers for testing
//         await addCurrencies();       // Add currencies first
//         await addAccounts(100);        // 5 accounts
//         await addContainers(10);      // 2 containers (with items)
//         await addExpenses(50);       // 10 expenses
//         await addPayments(50);        // 5 payments
//         await addExchanges(50);       // 5 exchanges
//     } catch (error) {
//         console.error('Test failed:', error);
//     } finally {
//         await prisma.$disconnect();
//     }
// }

// // Export functions for individual use
// export {
//     addContainers,
//     addAccounts,
//     addExpenses,
//     addPayments,
//     addExchanges,
//     addCurrencies,
//     cleanup,
//     runTests
// };

// // Run the tests
// runTests().catch(console.error); 