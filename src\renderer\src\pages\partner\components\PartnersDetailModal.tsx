import { Modal, Table, Empty, Tag } from 'antd'
import { useEffect } from 'react'
import { useApi } from '@/renderer/hooks'
import { partnerApi } from '@/renderer/services'
import { formatDate, formatCurrency } from '@/renderer/utils'
import type { PartnerWithContainers } from '@/common/types'
import { useTheme } from '@/renderer/contexts'

interface PartnersDetailModalProps {
  partnerId: string | null
  onClose: () => void
}

const PartnersDetailModal = ({ partnerId, onClose }: PartnersDetailModalProps) => {
  const { data: partner, request: fetchPartner } = useApi<PartnerWithContainers, [string]>(
    partnerApi.getPartnerById
  )
  const { isDarkMode } = useTheme()

  useEffect(() => {
    if (partnerId) {
      fetchPartner(partnerId)
    }
  }, [partnerId])

  const columns = [
    {
      title: 'Container Number',
      dataIndex: 'containerNumber',
      key: 'containerNumber'
    },
    {
      title: 'Opened Date',
      dataIndex: 'openedAt',
      key: 'openedAt',
      render: (date: string) => formatDate(date)
    },
    {
      title: 'Total Cost',
      key: 'totalCost',
      render: (_, record: any) =>
        formatCurrency(
          record.containerCost + record.driverExpense + record.taxes + record.fieldRent,
          'PKR'
        )
    },
    {
      title: 'Status',
      key: 'status',
      render: (_, record: any) => {
        // Get container summary from the backend
        const hasUnsolditems = [
          ...record.cars,
          ...record.carParts,
          ...record.electronics,
          ...record.scraps
        ].some((item) => item.status !== 'SOLD')

        const status = hasUnsolditems ? 'Active' : 'Completed'
        const color = hasUnsolditems ? 'blue' : 'green'
        return <Tag color={color}>{status}</Tag>
      }
    }
  ]

  if (!partner) {
    return (
      <Modal open={!!partnerId} onCancel={onClose} width={1000} title="Partner Details">
        <div className="flex h-32 items-center justify-center">
          <span>Loading...</span>
        </div>
      </Modal>
    )
  }

  return (
    <Modal
      open={!!partnerId}
      onCancel={onClose}
      width={1000}
      title={`Partner Details - ${partner.name}`}
      footer={null}
    >
      <div className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <div
            className={`shadow-large rounded-lg bg-[length:200%_200%] bg-[10%_10%] p-4 transition-[background-position] duration-500 ease-in-out hover:bg-[90%_90%] ${
              isDarkMode
                ? 'bg-gradient-to-br from-blue-950 via-slate-950 to-blue-900'
                : 'bg-gradient-to-br from-sky-200 via-white to-blue-200'
            }`}
          >
            <p className="text-sm text-gray-600">Total Containers</p>
            <p className="text-xl font-semibold">{partner.containers.length}</p>
          </div>
          <div
            className={`shadow-large rounded-lg bg-[length:200%_200%] bg-[10%_10%] p-4 transition-[background-position] duration-500 ease-in-out hover:bg-[90%_90%] ${
              isDarkMode
                ? 'bg-gradient-to-br from-blue-950 via-slate-950 to-blue-900'
                : 'bg-gradient-to-br from-sky-200 via-white to-blue-200'
            }`}
          >
            <p className="text-sm text-gray-600">Created On</p>
            <p className="text-xl font-semibold">{formatDate(partner.createdAt.toISOString())}</p>
          </div>
        </div>

        <div>
          <h3 className="mb-4 text-lg font-semibold">Containers</h3>
          {partner.containers.length > 0 ? (
            <Table
              sticky
              virtual
              columns={columns}
              dataSource={partner.containers}
              rowKey="id"
              pagination={false}
            />
          ) : (
            <Empty description="No containers found" />
          )}
        </div>
      </div>
    </Modal>
  )
}

export default PartnersDetailModal
