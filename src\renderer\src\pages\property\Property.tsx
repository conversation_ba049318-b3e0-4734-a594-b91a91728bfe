import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>, Tabs, message } from 'antd'
import { PropertyList } from './components/PropertyList'
import { PropertyForm } from './components/PropertyForm'
import { RentPropertyForm } from './components/RentPropertyForm'
import { RentedPropertyList } from './components/RentedPropertyList'
import { propertyApi, rentedPropertyApi } from '@/renderer/services'
import type { CreatePropertyData, CreateRentedPropertyData } from '@/common/types'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'

const Property = () => {
  const [isPropertyFormOpen, setIsPropertyFormOpen] = useState(false)
  const [isRentFormOpen, setIsRentFormOpen] = useState(false)
  const [selectedPropertyId, setSelectedPropertyId] = useState<string>()
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const [isCreating, setIsCreating] = useState(false)
  const [isRenting, setIsRenting] = useState(false)

  const user = useSelector((state: IRootState) => state.user.data)

  const handleCreateProperty = async (data: CreatePropertyData) => {
    setIsCreating(true)
    const response = await propertyApi.createProperty(data)
    setIsCreating(false)
    if (!response.error.error && !response.data.error) {
      message.success('Property created successfully')
      setIsPropertyFormOpen(false)
      setRefreshTrigger((prev) => prev + 1)
    } else {
      message.error(response.error?.message || response.data.error?.message)
    }
  }

  const handleRentProperty = async (data: CreateRentedPropertyData) => {
    if (!user?.id) {
      message.error('User session not found')
      return
    }

    setIsRenting(true)
    const response = await rentedPropertyApi.createRentedProperty(data, user.id)
    setIsRenting(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Property rented successfully')
    setIsRentFormOpen(false)
    setRefreshTrigger((prev) => prev + 1)
  }

  const handleRentClick = (propertyId: string) => {
    setSelectedPropertyId(propertyId)
    setIsRentFormOpen(true)
  }

  const items = [
    {
      key: 'properties',
      label: 'Properties',
      children: (
        <div className="flex flex-col gap-4">
          <div className="flex justify-end">
            <Button type="primary" onClick={() => setIsPropertyFormOpen(true)}>
              Add Property
            </Button>
          </div>
          <PropertyList refreshTrigger={refreshTrigger} onRentClick={handleRentClick} />
        </div>
      )
    },
    {
      key: 'rented',
      label: 'Rented Properties',
      children: <RentedPropertyList refreshTrigger={refreshTrigger} />
    }
  ]

  return (
    <div className="m-6 flex flex-col gap-6">
      <Card>
        <Tabs items={items} />
      </Card>

      <PropertyForm
        open={isPropertyFormOpen}
        loading={isCreating}
        onCancel={() => setIsPropertyFormOpen(false)}
        onSubmit={handleCreateProperty}
      />

      <RentPropertyForm
        open={isRentFormOpen}
        loading={isRenting}
        propertyId={selectedPropertyId}
        onCancel={() => setIsRentFormOpen(false)}
        onSubmit={handleRentProperty}
      />
    </div>
  )
}

export default Property
