import { Payment, Account, LedgerEntry, Currency, BankAccount } from '@prisma/client';

// Request Types
export interface CreatePaymentData {
    accountId: string;
    amount: number;
    paymentType: 'PAID' | 'RECEIVED';
    currencyCode: string;
    sourceLocation: 'SMALL_COUNTER' | 'CASH_VAULT' | 'BANK_ACCOUNT' | 'ACCOUNT';
    destinationType: 'SMALL_COUNTER' | 'CASH_VAULT' | 'BANK_ACCOUNT' | 'ACCOUNT';
    sourceBankId?: string;
    destinationBankId?: string;
    description?: string;
    date?: Date;
    userId: string;
}

export interface DeletePaymentParams {
    id: string;
    reason: string;
    userId: string;
}

export interface GetPaymentsParams {
    page?: number;
    limit?: number;
    startDate?: Date;
    endDate?: Date;
    paymentType?: 'PAID' | 'RECEIVED';
    includeDeleted?: boolean;
    accountId?: string;
    orderBy?: 'asc' | 'desc';
}

export interface TransferBetweenLocationsData {
    fromLocation: 'SMALL_COUNTER' | 'CASH_VAULT' | 'BANK_ACCOUNT';
    toLocation: 'SMALL_COUNTER' | 'CASH_VAULT' | 'BANK_ACCOUNT';
    amount: number;
    currencyCode: string;
    fromBankId?: string;
    toBankId?: string;
    description?: string;
    userId: string;
    date?: Date;
}

// Response Types
export interface PaymentWithRelations extends Payment {
    account: Account;
    currency: Currency;
    ledgerEntry: LedgerEntry & {
        bankAccount?: BankAccount;
    };
}

export interface GetPaymentsResponse {
    payments: PaymentWithRelations[];
    pagination: {
        total: number;
        pages: number;
        currentPage: number;
        pageSize: number;
    };
}

export interface PaymentSummary {
    [currencyCode: string]: {
        paid: number;
        received: number;
        totalTransactions: number;
    };
}

export interface DailyPaymentReport {
    date: Date;
    locationWise: {
        [location: string]: {
            [currencyCode: string]: {
                paid: number;
                received: number;
                count: number;
            };
        };
    };
    totals: {
        [currencyCode: string]: {
            totalPaid: number;
            totalReceived: number;
            totalTransactions: number;
        };
    };
}

export interface OutstandingPayment {
    saleId: string;
    accountId: string;
    accountName: string | null;
    totalAmount: number;
    amountPaid: number;
    outstandingAmount: number;
    date: Date;
}

export interface PaymentStatistics {
    period: {
        startDate: Date;
        endDate: Date;
    };
    currencyStats: {
        [currencyCode: string]: {
            totalAmount: number;
            totalTransactions: number;
            paid: number;
            received: number;
            averageAmount: number;
            maxAmount: number;
            minAmount: number;
        };
    };
    dailyDistribution: {
        [date: string]: {
            totalAmount: number;
            count: number;
        };
    };
    totalTransactions: number;
    uniqueAccounts: number;
}

export interface TransferResult {
    debitEntry: LedgerEntry;
    creditEntry: LedgerEntry;
}


export interface CreateAccountTransferData {
    sourceAccountId: string
    destinationAccountId: string
    amount: number
    currencyCode: string
    description?: string
    userId: string
    date?: Date
}

export interface GetTransfersParams {
    page: number
    limit: number
    startDate?: Date
    endDate?: Date
    search?: string
    accountId?: string
    currencyCode?: string
    type?: 'SENT' | 'RECEIVED' | 'ALL'
    includeDeleted?: boolean
    orderBy?: 'asc' | 'desc'
}

export interface GetTransfersResponse {
    transfers: Array<{
        id: string
        date: Date
        amount: number
        currency: {
            code: string
            name: string
        }
        description: string
        sourceType: string
        destinationType: string
        type: 'DEBIT' | 'CREDIT'
        account?: {
            id: string
            name: string
        }
        bankAccountId?: string
        isDeleted: boolean
        deleteReason?: string | null
        deletedAt?: Date | null
        deletedById?: string | null
    }>
    total: number
    page: number
    totalPages: number
}

export interface DeleteTransferData {
    ledgerEntryId: string
    userId: string
    reason: string
} 
