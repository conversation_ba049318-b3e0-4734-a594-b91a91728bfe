export interface CurrencyData {
    code: string;
    name: string;
}

export interface CurrencyTransaction {
    date: Date;
    amount: number;
    type: 'CREDIT' | 'DEBIT';
    description: string;
    createdBy: string;
    accountName?: string;
}

export interface PaginatedResponse<T> {
    data: T[];
    pagination: {
        total: number;
        pages: number;
        currentPage: number;
        pageSize: number;
    };
}

export interface CurrencySummary {
    inflow: number;
    outflow: number;
    netFlow: number;
}

export interface AccountCurrencyBalance {
    accountName: string;
    balance: number;
}

export interface CurrencyBalances {
    smallCounter: number;
    cashVault: number;
    bankAccounts: number;
    accountBalances: AccountCurrencyBalance[];
    totalSystemBalance: number;
}

export interface GetCurrencyTransactionsParams {
    startDate?: Date;
    endDate?: Date;
    page?: number;
    pageSize?: number;
}

export interface Currency {
    id: string
    code: string
    name: string
}

export interface CurrencyTransactionsResponse {
    transactions: Array<{
        id: string
        date: string
        amount: number
        type: 'CREDIT' | 'DEBIT'
        description: string
        currency: Currency
        createdBy: { name: string }
        account?: { name: string }
    }>
    pagination: {
        total: number
        pages: number
        currentPage: number
        pageSize: number
    }
}

export interface CurrencySummaryResponse {
    inflow: number
    outflow: number
    netFlow: number
}

export interface CurrencyBalancesResponse {
    smallCounter: number
    cashVault: number
    bankAccounts: number
    accountBalances: Array<{
        accountName: string
        balance: number
    }>
    totalSystemBalance: number
}