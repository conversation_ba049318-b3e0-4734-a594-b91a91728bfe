import { TransactionLocation as PrismaTransactionLocation, Sale, Account, Car, CarPart, Electronic, Scrap, Payment, BankAccount } from "@prisma/client";
import type { ItemType as PrismaItemType } from "@prisma/client";

export type ItemType = PrismaItemType;
export type TransactionLocation = PrismaTransactionLocation;

export interface CreateWalkInSaleData {
    itemType: ItemType;
    itemId: string;
    totalAmount: number;
    quantity: number;
    paymentDestination: TransactionLocation;
    bankAccountId?: string;
    userId: string;
    date: Date;
}

export interface CreateAccountSaleData {
    itemType: ItemType;
    itemId: string;
    totalAmount: number;
    quantity: number;
    accountId: string;
    userId: string;
    date: Date;
}

export interface DeleteSaleData {
    id: string;
    reason: string;
    userId: string;
}

export interface GetSalesParams {
    page?: number;
    pageSize?: number;
    accountId?: string;
    itemType?: ItemType;
    startDate?: Date;
    endDate?: Date;
    includeDeleted?: boolean;
    sortOrder?: 'asc' | 'desc'; // asc = oldest first, desc = newest first
}

export interface SelectOption {
    label: string
    value: string
}

export interface GetAvailableItemsResponse {
    options: SelectOption[]
}

export interface GetSaleByIdResponse extends Sale {
    account: Account | null
    car: Car | null
    carPart: CarPart | null
    electronic: Electronic | null
    scrap: Scrap | null
    payments: Payment[]
    bankAccount: BankAccount | null
    // createdBy: {
    //     id: string
    //     name: string
    // }
    // deletedBy?: {
    //     id: string
    //     name: string
    // }
}

export interface GetSalesResponse {
    sales: Array<Sale & {
        account: Account | null
        payments: Payment[]
        car: Car | null
        carPart: CarPart | null
        electronic: Electronic | null
        scrap: Scrap | null
        bankAccount: BankAccount | null
    }>
    pagination: {
        total: number
        pages: number
        currentPage: number
        pageSize: number
    }
}

// This is the type we'll use for table records
export type SaleWithRelations = Sale & {
    account: Account | null
    payments: Payment[]
    car: Car | null
    carPart: CarPart | null
    electronic: Electronic | null
    scrap: Scrap | null
    bankAccount: BankAccount | null
}