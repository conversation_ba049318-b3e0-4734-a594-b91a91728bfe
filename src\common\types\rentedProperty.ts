export interface CreateRentedPropertyData {
    propertyId: string
    tenantId: string
    startDate: Date
    endDate?: Date
    monthlyRent: number
}

export interface UpdateRentedPropertyData {
    endDate?: Date
    monthlyRent?: number
}

export interface RentedProperty {
    id: string
    startDate: Date
    endDate?: Date
    monthlyRent: number
    propertyId: string
    tenantId: string
    isDeleted: boolean
    deleteReason?: string
    deletedAt?: Date

    // Relations (optional, for populated data)
    property?: {
        name: string
        address: string
        type: string
    }
    tenant?: {
        name: string
        phoneNumber: string
    }
}

export interface GetRentedPropertiesParams {
    page?: number
    limit?: number
    tenantId?: string
    propertyId?: string
    isActive?: boolean // To filter currently active rentals
    search?: string // For searching by property name or tenant name
}

export interface GetRentedPropertiesResponse {
    rentedProperties: RentedProperty[]
    pagination: {
        total: number
        page: number
        limit: number
    }
}

// For tracking rent status
export interface RentStatus {
    rentedPropertyId: string
    propertyName: string
    tenantName: string
    monthlyRent: number
    lastPaidMonth: Date
    nextDueDate: Date
    totalDue: number
    totalPaid: number
    balance: number
    status: 'PAID' | 'PARTIALLY_PAID' | 'UNPAID' | 'OVERDUE'
}

export interface RecordRentPaymentData {
    rentedPropertyId: string
    amount: number
    userId: string
    destinationType: 'SMALL_COUNTER' | 'CASH_VAULT' | 'BANK_ACCOUNT'
    bankAccountId?: string // Required if destinationType is BANK_ACCOUNT
}

export interface AdjustRentPaymentData {
    rentedPropertyId: string
    amount: number
    type: 'CREDIT' | 'DEBIT'
    reason: string
    userId: string
    destinationType: 'SMALL_COUNTER' | 'CASH_VAULT' | 'BANK_ACCOUNT'
    bankAccountId?: string
}
