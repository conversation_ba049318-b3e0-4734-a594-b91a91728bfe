import { Table, Select, DatePicker, Spin, Typography, Tag, Card } from 'antd'
import { useEffect, useState } from 'react'
import { useApi } from '@/renderer/hooks'
import { currencyApi } from '@/renderer/services'
import type { Currency, CurrencyTransactionsResponse } from '@/common/types'
import { formatDate, formatCurrency } from '@/renderer/utils'
import { title } from 'process'

const { RangePicker } = DatePicker

export const CurrencyTransactions = () => {
  const [selectedCurrency, setSelectedCurrency] = useState<string>('PKR')
  const [dateRange, setDateRange] = useState<[Date, Date] | null>(null)
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  const {
    data: currencies,
    isLoading: loadingCurrencies,
    request: fetchCurrencies
  } = useApi<Currency[], []>(currencyApi.getAllCurrencies)

  const {
    data: transactionsData,
    isLoading: loadingTransactions,
    request: fetchTransactions
  } = useApi<CurrencyTransactionsResponse, [string, string, string, number, number]>(
    currencyApi.getCurrencyTransactions
  )

  useEffect(() => {
    fetchCurrencies()
  }, [])

  useEffect(() => {
    fetchTransactions(
      selectedCurrency,
      dateRange?.[0]?.toISOString() as string,
      dateRange?.[1]?.toISOString() as string,
      page,
      pageSize
    )
  }, [selectedCurrency, dateRange, page, pageSize])

  useEffect(() => {
    if (transactionsData) {
      console.log('transactionsData', transactionsData)
    }
  }, [transactionsData])

  const columns = [
    {
      title: 'Sr. No',
      key: 'serialNumber',
      width: 70,
      render: (_: any, __: any, index: number) => {
        // Calculate serial number based on current page and page size
        return (page - 1) * pageSize + index + 1
      }
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => formatDate(date)
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      width: 400,
      ellipsis: {
        showTitle: false
      },
      render: (description: string) => (
        <Typography.Text
          ellipsis={{
            tooltip: {
              title: description,
              mouseEnterDelay: 0.3
            }
          }}
        >
          {description}
        </Typography.Text>
      )
    },
    {
      title: 'Amount',
      key: 'amount',
      render: (record: any) => (
        <span className={record.type === 'CREDIT' ? 'text-green-600' : 'text-red-600'}>
          {formatCurrency(record.amount, record.currency.code)}
        </span>
      )
    },
    {
      title: 'Created By',
      dataIndex: ['createdBy', 'name'],
      key: 'createdBy'
    },
    {
      title: 'Account',
      dataIndex: ['account', 'name'],
      key: 'account'
    }
  ]

  if (loadingCurrencies) return <Spin />

  return (
    <Card className="rounded-lg">
      <Card className="mb-4 flex gap-4">
        <Select value={selectedCurrency} onChange={setSelectedCurrency} style={{ width: 200 }}>
          {currencies?.map((currency) => (
            <Select.Option key={currency.code} value={currency.code}>
              {currency.name} ({currency.code})
            </Select.Option>
          ))}
        </Select>

        <RangePicker
          onChange={(_, dateStrings) => {
            if (dateStrings[0] && dateStrings[1]) {
              setDateRange([new Date(dateStrings[0]), new Date(dateStrings[1])])
            } else {
              setDateRange(null)
            }
          }}
        />
      </Card>

      <Table
        columns={columns}
        dataSource={transactionsData?.transactions}
        rowKey="id"
        loading={loadingTransactions}
        sticky
        virtual
        size="small"
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          showPrevNextJumpers: true,
          position: ['topRight'],
          current: page,
          pageSize,
          total: transactionsData?.pagination.total,
          onChange: (page, pageSize) => {
            setPage(page)
            setPageSize(pageSize)
          }
        }}
      />
    </Card>
  )
}
