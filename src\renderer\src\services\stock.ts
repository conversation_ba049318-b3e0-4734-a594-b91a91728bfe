import { http } from './http';
import { Channels } from '@/common/constants';
import { GetStockParams } from '@/common/types';

export const getCars = async (params: Partial<GetStockParams> = {}) => {
    const defaultParams: GetStockParams = {
        page: params.page ?? 1,
        limit: params.limit ?? 10,
        status: params.status ?? 'AVAILABLE',
        search: params.search,
        containerId: params.containerId,
        orderBy: params.orderBy ?? 'desc'
    };

    return await http.get(Channels.GET_CARS, { query: defaultParams });
};

export const getCarParts = async (params: Partial<GetStockParams> = {}) => {
    const defaultParams: GetStockParams = {
        page: params.page ?? 1,
        limit: params.limit ?? 10,
        status: params.status ?? 'AVAILABLE',
        search: params.search,
        containerId: params.containerId,
        orderBy: params.orderBy ?? 'desc'
    };

    return await http.get(Channels.GET_CAR_PARTS, { query: defaultParams });
};

export const getElectronics = async (params: Partial<GetStockParams> = {}) => {
    const defaultParams: GetStockParams = {
        page: params.page ?? 1,
        limit: params.limit ?? 10,
        status: params.status ?? 'AVAILABLE',
        search: params.search,
        containerId: params.containerId,
        orderBy: params.orderBy ?? 'desc'
    };

    return await http.get(Channels.GET_ELECTRONICS, { query: defaultParams });
};

export const getScraps = async (params: Partial<GetStockParams> = {}) => {
    const defaultParams: GetStockParams = {
        page: params.page ?? 1,
        limit: params.limit ?? 10,
        status: params.status ?? 'AVAILABLE',
        search: params.search,
        containerId: params.containerId,
        orderBy: params.orderBy ?? 'desc'
    };

    return await http.get(Channels.GET_SCRAPS, { query: defaultParams });
};

export const getStockSummary = async () => {
    return await http.get(Channels.GET_STOCK_SUMMARY);
};