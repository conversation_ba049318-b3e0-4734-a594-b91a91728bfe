import { http } from './http';
import { Channels } from '@/common/constants';
import type {
    CreateWalkInSaleData,
    CreateAccountSaleData,
    GetSalesParams,
    GetSaleByIdResponse,
    GetAvailableItemsResponse,
    DeleteSaleData,
    GetContainerByStockIdParams
} from '@/common/types';

type ItemType = "CAR" | "CARPART" | "ELECTRONIC" | "SCRAP"



export const createWalkInSale = async (data: CreateWalkInSaleData) => {
    return await http.post(Channels.CREATE_WALK_IN_SALE, { body: data });
};

export const createAccountSale = async (data: CreateAccountSaleData) => {
    return await http.post(Channels.CREATE_ACCOUNT_SALE, { body: data });
};

export const deleteSale = async (data: DeleteSaleData) => {
    return await http.delete(Channels.DELETE_SALE, {
        body: data
    });
};

export const getSales = async (params: GetSalesParams) => {
    return await http.get(Channels.GET_SALES, { query: params });
};

export const getAvailableItems = async (itemType: ItemType) => {
    return await http.get(Channels.GET_AVAILABLE_ITEMS, {
        params: { itemType }
    });
};

export const getSaleById = async (id: string) => {
    return await http.get(Channels.GET_SALE_BY_ID, {
        params: { id }
    });
};

export const getContainerByStockId = async (params: GetContainerByStockIdParams) => {
    return await http.get(Channels.GET_CONTAINER_BY_STOCK_ID, { params });
};