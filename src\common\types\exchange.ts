import { TransactionLocation } from '@prisma/client';



export interface CreateStructuredExchangeData {
    // Account Details
    accountId: string;

    // Input Currency Details
    inputCurrency: string;
    inputAmount: number;
    inputSource: 'NEW_DEPOSIT' | 'ACCOUNT_BALANCE';  // Whether currency is being brought in or using existing balance
    inputLocation?: TransactionLocation;  // Required only if inputSource is NEW_DEPOSIT
    inputBankId?: string;  // Required if inputLocation is BANK_ACCOUNT
    allowLoan?: boolean;   // Whether to allow exchange even if account balance is insufficient

    // Exchange Details
    exchangeRate: number;
    isReverseRate?: boolean;  // Whether the exchange rate is reversed (e.g., PKR/USD instead of USD/PKR)
    displayRate?: number;     // The rate to display in UI and descriptions
    description?: string;

    // Output Details
    outputCurrency: string;
    keepInAccount: boolean;  // Whether to keep the exchanged amount in account
    outputLocation?: TransactionLocation;  // Required if not keeping in account
    outputBankId?: string;   // Required if outputLocation is BANK_ACCOUNT

    // Metadata
    userId: string;
}

export interface ExchangeValidationResult {
    isValid: boolean;
    errors: string[];
}

export interface CreateExchangeData {
    fromAmount: number;
    fromCurrency: string;  // Currency code
    toAmount: number;
    toCurrency: string;   // Currency code
    exchangeRate: number;
    description?: string;
    sourceLocation: TransactionLocation;
    destinationLocation: TransactionLocation;
    createdById: string;
}

export interface GetExchangeHistoryParams {
    page: number;
    limit: number;
    startDate?: Date;
    endDate?: Date;
    fromCurrency?: string;
    toCurrency?: string;
    location?: TransactionLocation;
    sortOrder?: 'asc' | 'desc'; // 'asc' for oldest first, 'desc' for newest first
}

export interface ExchangeStatistics {
    totalVolume: {
        currency: string;
        inflow: number;
        outflow: number;
    }[];
    totalTransactions: number;
}

export interface DailyExchangeSummary {
    date: Date;
    exchanges: {
        currency: string;
        inflow: number;
        outflow: number;
        transactions: number;
    }[];
    totalTransactions: number;
}

export interface MonthlyExchangeReport {
    month: Date;
    totalVolume: number;
    byCurrency: {
        currency: string;
        inflow: number;
        outflow: number;
        averageRate: number;
    }[];
    trends: {
        currency: string;
        trend: 'UP' | 'DOWN' | 'STABLE';
        percentageChange: number;
    }[];
}

export interface LocationExchangeHistory {
    location: TransactionLocation;
    exchanges: {
        date: Date;
        currency: string;
        inflow: number;
        outflow: number;
    }[];
    totalVolume: number;
}