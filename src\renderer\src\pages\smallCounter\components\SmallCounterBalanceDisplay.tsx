import { Card, Statistic, Row, Col, Spin } from 'antd'
import { useApi } from '@/renderer/hooks'
import { smallCounterApi } from '@/renderer/services'
import type { CounterBalances } from '@/common/types'
import { formatCurrency } from '@/renderer/utils'
import { useEffect } from 'react'
import { useTheme } from '@/renderer/contexts/ThemeContext'

export const SmallCounterBalanceDisplay = () => {
  const { isDarkMode } = useTheme()

  const {
    data: balances,
    isLoading,
    request: getBalances
  } = useApi<CounterBalances, [string]>(smallCounterApi.getBalances)

  useEffect(() => {
    getBalances('SMALL_COUNTER')
  }, [])

  if (isLoading) return <Spin />

  const currencies = [
    { code: 'PKR', balance: balances?.pkrBalance ?? 0 },
    { code: 'USD', balance: balances?.usdBalance ?? 0 },
    { code: 'AED', balance: balances?.aedBalance ?? 0 },
    { code: 'AFN', balance: balances?.afnBalance ?? 0 }
  ]

  return (
    <Row gutter={[16, 16]}>
      {currencies.map(({ code, balance }) => (
        <Col span={6} key={code}>
          <Card
            size="small"
            className={`shadow-large bg-[length:200%_200%] bg-[10%_10%] transition-[background-position] duration-500 ease-in-out hover:bg-[90%_90%] ${
              isDarkMode
                ? 'bg-gradient-to-br from-blue-950 via-slate-950 to-blue-900'
                : 'bg-gradient-to-br from-sky-200 via-white to-blue-200'
            }`}
          >
            <Statistic
              title={`${code} Balance`}
              value={balance}
              precision={2}
              formatter={(value) => formatCurrency(value as number, code)}
            />
          </Card>
        </Col>
      ))}
    </Row>
  )
}
