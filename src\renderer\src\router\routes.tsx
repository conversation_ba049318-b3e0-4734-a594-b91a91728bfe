import { App_Routes, Roles } from '@/common'
import { lazy } from 'react'

export const LazyPages = {
  AuthLayout: lazy(() =>
    import('@/renderer/layouts').then((module) => ({ default: module.AuthLayout }))
  ),
  AppLayout: lazy(() =>
    import('@/renderer/layouts').then((module) => ({ default: module.AppLayout }))
  ),
  Login: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Login }))),
  NotFound: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.NotFound }))),
  Logout: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Logout }))),
  Dashboard: lazy(() =>
    import('@/renderer/pages').then((module) => ({ default: module.Dashboard }))
  ),
  User: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.User }))),
  Staff: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Staff }))),
  Expense: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Expense }))),
  Ledger: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Ledger }))),
  Stock: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Stock }))),

  Banks: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Banks }))),
  CashVault: lazy(() =>
    import('@/renderer/pages').then((module) => ({ default: module.CashVault }))
  ),
  Accounts: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Accounts }))),
  Container: lazy(() =>
    import('@/renderer/pages').then((module) => ({ default: module.Container }))
  ),
  Currency: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Currency }))),
  Exchange: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Exchange }))),
  Partner: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Partner }))),
  Sale: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Sale }))),
  Property: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Property }))),
  License: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.License }))),
  Payment: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Payment }))),
  Reset: lazy(() => import('@/renderer/pages').then((module) => ({ default: module.Reset }))),
  SmallCounter: lazy(() =>
    import('@/renderer/pages').then((module) => ({ default: module.SmallCounter }))
  ),
  ManualEntry: lazy(() =>
    import('@/renderer/pages').then((module) => ({ default: module.ManualEntry }))
  )
}

type IAppRoutes = {
  path: `${App_Routes}`
  component: React.LazyExoticComponent<() => JSX.Element | undefined>
  // roles: `${Roles}`[];
}

export const AppRoutes: IAppRoutes[] = [
  {
    path: App_Routes.DASHBOARD,
    component: LazyPages.Dashboard
    // roles: [Roles.USER, Roles.ADMIN]
  },
  {
    path: App_Routes.USERS,
    component: LazyPages.User
    // roles: [Roles.ADMIN],
  },
  {
    path: App_Routes.STAFF,
    component: LazyPages.Staff
    // roles: [Roles.ADMIN, Roles.USER],
  },

  {
    path: App_Routes.EXPENSE,
    component: LazyPages.Expense
    // roles: [Roles.ADMIN, Roles.USER],
  },

  {
    path: App_Routes.LEDGER,
    component: LazyPages.Ledger
    // roles: [Roles.ADMIN, Roles.USER],
  },
  {
    path: App_Routes.STOCK,
    component: LazyPages.Stock
    // roles: [Roles.ADMIN, Roles.USER],
  },

  {
    path: App_Routes.ANY,
    component: LazyPages.NotFound
    // roles: [Roles.ANY],
  },

  {
    path: App_Routes.BANKS,
    component: LazyPages.Banks
  },
  {
    path: App_Routes.CASH_VAULT,
    component: LazyPages.CashVault
  },
  {
    path: App_Routes.SMALL_COUNTER,
    component: LazyPages.SmallCounter
  },
  {
    path: App_Routes.ACCOUNTS,
    component: LazyPages.Accounts
  },
  {
    path: App_Routes.CONTAINER,
    component: LazyPages.Container
  },
  {
    path: App_Routes.CURRENCY,
    component: LazyPages.Currency
  },
  {
    path: App_Routes.EXCHANGE,
    component: LazyPages.Exchange
  },
  {
    path: App_Routes.PARTNER,
    component: LazyPages.Partner
  },
  {
    path: App_Routes.SALE,
    component: LazyPages.Sale
  },
  {
    path: App_Routes.PROPERTY,
    component: LazyPages.Property
  },
  {
    path: App_Routes.LICENSE,
    component: LazyPages.License
  },
  {
    path: App_Routes.PAYMENT,
    component: LazyPages.Payment
  },
  {
    path: App_Routes.RESET,
    component: LazyPages.Reset
  },
  {
    path: App_Routes.MANUAL_ENTRY,
    component: LazyPages.ManualEntry
  }
]
