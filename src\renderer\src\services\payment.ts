import { http } from './http';
import { Channels } from '@/common/constants';
import type {
    CreatePaymentData,
    DeletePaymentParams,
    GetPaymentsParams,
    PaymentSummary,
    DailyPaymentReport,
    OutstandingPayment,
    PaymentStatistics,
    TransferBetweenLocationsData,
    CreateAccountTransferData,
    GetTransfersParams,
    DeleteTransferData
} from '@/common/types';

export const createPayment = async (data: CreatePaymentData) => {
    return await http.post(Channels.CREATE_PAYMENT, { body: data });
};

export const deletePayment = async (data: DeletePaymentParams) => {
    return await http.delete(Channels.DELETE_PAYMENT, { body: data });
};

export const getPayments = async (params: GetPaymentsParams) => {
    return await http.get(Channels.GET_PAYMENTS, { query: params });
};

export const getPaymentSummaryByAccount = async (accountId: string, startDate?: Date, endDate?: Date) => {
    return await http.get(Channels.GET_PAYMENT_SUMMARY_BY_ACCOUNT, {
        query: { accountId, startDate, endDate }
    });
};

export const getDailyPaymentReport = async (date: Date) => {
    return await http.get(Channels.GET_DAILY_PAYMENT_REPORT, {
        query: { date }
    });
};

export const getOutstandingPayments = async () => {
    return await http.get(Channels.GET_OUTSTANDING_PAYMENTS);
};

export const getPaymentStatistics = async (startDate: Date, endDate: Date) => {
    return await http.get(Channels.GET_PAYMENT_STATISTICS, {
        query: { startDate, endDate }
    });
};

export const transferBetweenLocations = async (data: TransferBetweenLocationsData) => {
    return await http.post(Channels.TRANSFER_BETWEEN_LOCATIONS, { body: data });
};

export const transferBetweenAccounts = async (data: CreateAccountTransferData) => {
    return await http.post(Channels.TRANSFER_BETWEEN_ACCOUNTS, { body: data });
};

export const getTransfers = async (params: GetTransfersParams) => {
    return await http.get(Channels.GET_TRANSFERS, { query: params });
};

export const deleteTransfer = async (data: DeleteTransferData) => {
    return await http.delete(Channels.DELETE_TRANSFER, {
        body: data
    });
};
