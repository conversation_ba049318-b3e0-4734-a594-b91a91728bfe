import { Form, Input, Button, Tooltip } from 'antd'
import type { FormInstance } from 'antd'
import { InfoCircleOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'

interface CarFormProps {
  form: FormInstance
}

export const CarForm = ({ form }: CarFormProps) => {
  const generateUniqueChassisNumber = () => {
    // Format: AUTO-YYYYMMDD-HHmmss-SSS (AUTO prefix, full date, time with seconds and milliseconds)
    const timestamp = dayjs().format('YYYY/MM/DD-HH:mm:ss-SSS')
    const uniqueChassisNumber = `AUTO-${timestamp}`
    form.setFieldValue('chassisNumber', uniqueChassisNumber)
  }

  return (
    <>
      <Form.Item
        name="chassisNumber"
        label="Chassis Number"
        rules={[{ required: true, message: 'Please enter chassis number' }]}
        extra={
          <div className="mt-1 flex items-center justify-between">
            <Button
              type="link"
              size="small"
              onClick={generateUniqueChassisNumber}
              className="h-auto p-0 text-blue-500"
            >
              No Chassis Number? Generate Auto ID
            </Button>
            <Tooltip title="This will generate a unique identifier based on current date and time">
              <InfoCircleOutlined className="text-gray-500" />
            </Tooltip>
          </div>
        }
      >
        <Input />
      </Form.Item>

      <Form.Item
        name="modelNumber"
        label="Model Number"
        rules={[{ required: true, message: 'Please enter model number' }]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        name="name"
        label="Name"
        rules={[{ required: true, message: 'Please enter name' }]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        name="color"
        label="Color"
        rules={[{ required: true, message: 'Please enter color' }]}
      >
        <Input />
      </Form.Item>
    </>
  )
}
