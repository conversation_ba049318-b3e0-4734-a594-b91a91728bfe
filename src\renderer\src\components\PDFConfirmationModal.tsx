import React from 'react'
import { Modal, Button, Space } from 'antd'

interface PDFConfirmationModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: () => void
  onPrint: () => void
  title?: string
}

const PDFConfirmationModal: React.FC<PDFConfirmationModalProps> = ({
  isOpen,
  onClose,
  onSave,
  onPrint,
  title = 'Document Actions'
}) => {
  return (
    <Modal title={title} open={isOpen} onCancel={onClose} footer={null} destroyOnClose>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Button type="primary" onClick={onSave} style={{ width: '100%' }}>
          Save PDF
        </Button>
        <Button onClick={onPrint} style={{ width: '100%' }}>
          View & Print
        </Button>
        <Button onClick={onClose} style={{ width: '100%' }}>
          Cancel
        </Button>
      </Space>
    </Modal>
  )
}

export default PDFConfirmationModal
