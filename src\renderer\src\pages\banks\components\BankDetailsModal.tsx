import { Mo<PERSON>, Ta<PERSON>, But<PERSON>, message, Popconfirm, InputNumber, Input } from 'antd'
import { useState, useEffect } from 'react'
import { useApi } from '@/renderer/hooks'
import { bankAccountApi } from '@/renderer/services'
import { TransactionHistory } from './TransactionHistory'
import type { BankAccount } from '@/common/types'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { BankStatement } from './BankStatement'

const { TabPane } = Tabs

interface BankDetailsModalProps {
  bankId: string | null
  onClose: () => void
}

export const BankDetailsModal = ({ bankId, onClose }: BankDetailsModalProps) => {
  const [isLoading, setIsLoading] = useState(false)

  const [activeTab, setActiveTab] = useState('transactions')
  const [adjustment, setAdjustment] = useState<number | null>(null)
  const [adjustmentReason, setAdjustmentReason] = useState('')

  const user = useSelector((state: IRootState) => state.user.data)

  const { data: bank, request: fetchBank } = useApi<BankAccount, [string]>(
    bankAccountApi.getBankAccountById
  )

  useEffect(() => {
    if (bankId) {
      fetchBank(bankId)
    }
  }, [bankId])

  // const handleAdjustBalance = async () => {
  //   if (!bankId || !adjustment || !adjustmentReason) return

  //   setIsLoading(true)

  //   const response = await bankAccountApi.adjustBalance(
  //     bankId,
  //     adjustment,
  //     adjustmentReason,
  //     user?.id || ''
  //   )

  //   if (response.error.error || response.data.error) {
  //     message.error(response.error.message || response.data.error.message)
  //     setIsLoading(false)
  //     return
  //   }

  //   message.success('Balance adjusted successfully')
  //   fetchBank(bankId)
  //   setAdjustment(null)
  //   setAdjustmentReason('')
  //   setIsLoading(false)
  // }

  if (!bank) return null

  return (
    <Modal
      open={!!bankId}
      onCancel={onClose}
      width={1200}
      title={`Bank Account - ${bank.bankName} (${bank.accountNumber})`}
      footer={null}
    >
      <div className="space-y-6">
        {/* <div className="grid grid-cols-3 gap-4">
          <Card className="bg-blue-50">
            <div className="text-center">
              <p className="text-sm text-gray-600">Current Balance</p>
              <p
                className={`text-2xl font-semibold ${
                  bank.balance >= 0 ? 'text-green-600' : 'text-red-600'
                }`}
              >
                {formatCurrency(bank.balance, 'PKR')}
              </p>
            </div>
          </Card>

          {reconciliation && (
            <>
              <Card className={reconciliation.isReconciled ? 'bg-green-50' : 'bg-red-50'}>
                <div className="text-center">
                  <p className="text-sm text-gray-600">Reconciliation Status</p>
                  <p
                    className={`text-xl font-semibold ${
                      reconciliation.isReconciled ? 'text-green-600' : 'text-red-600'
                    }`}
                  >
                    {reconciliation.isReconciled ? 'Balanced' : 'Unbalanced'}
                  </p>
                  {!reconciliation.isReconciled && (
                    <p className="mt-1 text-sm text-red-600">
                      Difference: {formatCurrency(Math.abs(reconciliation.difference), 'PKR')}
                    </p>
                  )}
                </div>
              </Card>

              <Card className="bg-orange-50">
                <div className="text-center">
                  <p className="text-sm text-gray-600">Calculated Balance</p>
                  <p className="text-xl font-semibold text-orange-600">
                    {formatCurrency(reconciliation.calculatedBalance, 'PKR')}
                  </p>
                </div>
              </Card>
            </>
          )}
        </div> */}

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="Transactions" key="transactions">
            <TransactionHistory bankId={bankId || ''} />
          </TabPane>

          <TabPane tab="Statement" key="statement">
            {bankId && <BankStatement bankId={bankId} />}
          </TabPane>

          {/* <TabPane tab="Adjust Balance" key="adjust">
            <div className="max-w-md space-y-4">
              <InputNumber
                className="w-full"
                value={adjustment}
                onChange={setAdjustment}
                formatter={(value) => `Rs ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={(value) => Number(value!.replace(/Rs\s?|(,*)/g, ''))}
                placeholder="Enter adjustment amount"
              />
              <Input.TextArea
                value={adjustmentReason}
                onChange={(e) => setAdjustmentReason(e.target.value)}
                placeholder="Enter reason for adjustment"
                rows={4}
              />
              <Popconfirm
                title="Confirm Balance Adjustment"
                description="Are you sure you want to adjust the balance?"
                onConfirm={handleAdjustBalance}
              >
                <Button
                  type="primary"
                  loading={isLoading}
                  disabled={!adjustment || !adjustmentReason}
                >
                  Adjust Balance
                </Button>
              </Popconfirm>
            </div>
          </TabPane> */}
        </Tabs>
      </div>
    </Modal>
  )
}
