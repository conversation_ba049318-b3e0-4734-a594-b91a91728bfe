import { Form, Input, InputNumber } from 'antd'
import type { FormInstance } from 'antd'

interface ScrapFormProps {
  form: FormInstance
}

export const ScrapForm = ({ form }: ScrapFormProps) => {
  return (
    <>
      <Form.Item
        name="description"
        label="Description"
        rules={[{ required: true, message: 'Please enter description' }]}
      >
        <Input.TextArea rows={3} />
      </Form.Item>

      <Form.Item
        name="quantity"
        label="Quantity (Weight)"
        rules={[{ required: true, message: 'Please enter quantity' }]}
        initialValue={1}
      >
        <InputNumber min={0.1} step={0.1} className="w-full" />
      </Form.Item>
    </>
  )
}
