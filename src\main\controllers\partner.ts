import { IRequest } from '@/common/types';
import { partnerService } from '../services';

class PartnerController {
    async createPartner(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { name } = req.body ?? {};

        if (!name) {
            throw new Error('Partner name is required');
        }

        return await partnerService.createPartner({ name });
    }

    async deletePartner(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        const { deleteReason } = req.body ?? {};

        if (!id) throw new Error('Partner ID is required');
        if (!deleteReason) throw new Error('Delete reason is required');

        return await partnerService.deletePartner(id, deleteReason);
    }

    async getPartners(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { page = 1, limit = 10, includeDeleted, search } = req.query ?? {};
        return await partnerService.getPartners({
            page: Number(page),
            limit: Number(limit),
            includeDeleted: Boolean(includeDeleted),
            search
        });
    }

    async getPartnerById(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        if (!id) throw new Error('Partner ID is required');

        return await partnerService.getPartnerById(id);
    }

    async getPartnersForSelect(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        return await partnerService.getPartnersForSelect();
    }
}

export const partnerController = new PartnerController();