import { IObject, LocalStorage } from "@/common";
import dayjs from 'dayjs'



export const localStorageManager = {
  setToken: (token: string) => {
    localStorage.setItem(LocalStorage.TOKEN, token);
  },
  getToken: () => {
    return localStorage.getItem(LocalStorage.TOKEN);
  },
  clearToken: () => {
    localStorage.removeItem(LocalStorage.TOKEN);
  },
};

export const getQueryStringFromObject = (data: IObject) => {
  let queryArr: string[] = [];
  Object.keys(data).map((key) => {
    let v = data[key];
    queryArr.push(`${key}=${v}`);
  });
  return `?${queryArr.join('&')}`
}


export const formatCurrency = (amount: number, currency?: string): string => {
  if (!currency) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(amount)
  }
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(amount)
}

export const formatCurrencyWithoutSymbol = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'decimal',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(amount)
}

export const formatDate = (date: string): string => {
  return dayjs(date).format('DD/MM/YYYY')
}



export const addCurrentTimeToCustomDate = (date: Date) => {
  const now = new Date()
  date.setHours(now.getHours())
  date.setMinutes(now.getMinutes())
  date.setSeconds(now.getSeconds())
  date.setMilliseconds(now.getMilliseconds())
  return date
}

// New timezone handling functions
export const convertToLocalTime = (date: Date | string) => {
  return dayjs(date).tz('Asia/Karachi').toDate()
}

export const convertToUTC = (date: Date | string) => {
  return dayjs(date).tz('Asia/Karachi').utc().toDate()
}

// Function to handle date picker values
export const handleDatePickerValue = (date: Date) => {
  // First add current time
  const dateWithTime = addCurrentTimeToCustomDate(date)
  // Then convert to Pakistan timezone
  return convertToLocalTime(dateWithTime)
}
