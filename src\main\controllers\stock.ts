import { IRequest } from '@/common/types';
import { stockService } from '../services';

class StockController {
    async getCars(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { page = 1, limit = 10, status = 'AVAILABLE', search, containerId, orderBy = 'desc' } = req.query ?? {};

        return await stockService.getCars({
            page: Number(page),
            limit: Number(limit),
            status: status as any,
            search: search as string,
            containerId: containerId as string,
            orderBy: orderBy as 'asc' | 'desc'
        });
    }

    async getCarParts(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { page = 1, limit = 10, status = 'AVAILABLE', search, containerId, orderBy = 'desc' } = req.query ?? {};

        return await stockService.getCarParts({
            page: Number(page),
            limit: Number(limit),
            status: status as any,
            search: search as string,
            containerId: containerId as string,
            orderBy: orderBy as 'asc' | 'desc'
        });
    }

    async getElectronics(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { page = 1, limit = 10, status = 'AVAILABLE', search, containerId, orderBy = 'desc' } = req.query ?? {};

        return await stockService.getElectronics({
            page: Number(page),
            limit: Number(limit),
            status: status as any,
            search: search as string,
            containerId: containerId as string,
            orderBy: orderBy as 'asc' | 'desc'
        });
    }

    async getScraps(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { page = 1, limit = 10, status = 'AVAILABLE', search, containerId, orderBy = 'desc' } = req.query ?? {};

        return await stockService.getScraps({
            page: Number(page),
            limit: Number(limit),
            status: status as any,
            search: search as string,
            containerId: containerId as string,
            orderBy: orderBy as 'asc' | 'desc'
        });
    }

    async getStockSummary(_event: Electron.IpcMainInvokeEvent) {
        return await stockService.getStockSummary();
    }
}

export const stockController = new StockController();