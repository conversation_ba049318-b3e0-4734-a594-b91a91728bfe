import { ipcMain, dialog } from "electron";
import { Channels } from "../../common";
import { catchIpcHandler } from "./handler";
import { containerController, userController, partnerController, stockController, accountController, exchangeController, smallCounterController, cashVaultController, currencyController, bankAccountController, saleController, ledgerController, expenseController, propertyController, rentedPropertyController, licenseController, paymentController, resetController, backupController, dashboardController } from "../controllers";
import { backupService } from '../services/backup';


// Container APIs
ipcMain.handle(`POST:${Channels.CREATE_CONTAINER}`, catchIpcHandler(containerController.createContainer));
ipcMain.handle(`DELETE:${Channels.DELETE_CONTAINER}`, catchIpc<PERSON>andler(containerController.deleteContainer));
ipcMain.handle(`GET:${Channels.GET_CONTAINERS}`, catchIpc<PERSON>andler(containerController.getContainers));
ipcMain.handle(`GET:${Channels.GET_CONTAINER_STOCK}`, catchIpcHandler(containerController.getContainerStock));
ipcMain.handle(`GET:${Channels.GET_CONTAINER}`, catchIpcHandler(containerController.getContainerById));
ipcMain.handle(`GET:${Channels.GET_CONTAINER_SUMMARY}`, catchIpcHandler(containerController.getContainerSummary));
ipcMain.handle(`POST:${Channels.CHECK_CAR_WITH_SAME_CHASSIS_NUMBER_EXISTS}`, catchIpcHandler(containerController.checkCarWithSameChassisNumberExists));
ipcMain.handle(`GET:${Channels.GET_CONTAINERS_FOR_PDF}`, catchIpcHandler(containerController.getContainersForPDF));

// User APIs
ipcMain.handle(`POST:${Channels.CREATE_USER}`, catchIpcHandler(userController.createUser));
ipcMain.handle(`PUT:${Channels.UPDATE_PASSWORD}`, catchIpcHandler(userController.updatePassword));
ipcMain.handle(`PUT:${Channels.RESET_PASSWORD}`, catchIpcHandler(userController.resetPassword));
ipcMain.handle(`GET:${Channels.GET_USERS}`, catchIpcHandler(userController.getUsers));
ipcMain.handle(`PUT:${Channels.DEACTIVATE_USER}`, catchIpcHandler(userController.deactivateUser));
ipcMain.handle(`POST:${Channels.LOGIN}`, catchIpcHandler(userController.login));

// Partner APIs
ipcMain.handle(`POST:${Channels.CREATE_PARTNER}`, catchIpcHandler(partnerController.createPartner));
ipcMain.handle(`DELETE:${Channels.DELETE_PARTNER}`, catchIpcHandler(partnerController.deletePartner));
ipcMain.handle(`GET:${Channels.GET_PARTNERS}`, catchIpcHandler(partnerController.getPartners));
ipcMain.handle(`GET:${Channels.GET_PARTNER}`, catchIpcHandler(partnerController.getPartnerById));
ipcMain.handle(`GET:${Channels.GET_PARTNERS_SELECT}`, catchIpcHandler(partnerController.getPartnersForSelect));

// Stock APIs
ipcMain.handle(`GET:${Channels.GET_CARS}`, catchIpcHandler(stockController.getCars));
ipcMain.handle(`GET:${Channels.GET_CAR_PARTS}`, catchIpcHandler(stockController.getCarParts));
ipcMain.handle(`GET:${Channels.GET_ELECTRONICS}`, catchIpcHandler(stockController.getElectronics));
ipcMain.handle(`GET:${Channels.GET_SCRAPS}`, catchIpcHandler(stockController.getScraps));
ipcMain.handle(`GET:${Channels.GET_STOCK_SUMMARY}`, catchIpcHandler(stockController.getStockSummary));

// Account APIs
ipcMain.handle(`POST:${Channels.CREATE_ACCOUNT}`, catchIpcHandler(accountController.createAccount));
ipcMain.handle(`DELETE:${Channels.DELETE_ACCOUNT}`, catchIpcHandler(accountController.deleteAccount));
ipcMain.handle(`PUT:${Channels.UPDATE_ACCOUNT}`, catchIpcHandler(accountController.updateAccount));
ipcMain.handle(`GET:${Channels.GET_ACCOUNTS}`, catchIpcHandler(accountController.getAccounts));
ipcMain.handle(`GET:${Channels.GET_ACCOUNTS_SELECT}`, catchIpcHandler(accountController.getAccountsForSelect));
ipcMain.handle(`GET:${Channels.GET_CUSTOMER_ACCOUNTS_SELECT}`, catchIpcHandler(accountController.getCustomerAccountsForSelect));
ipcMain.handle(`GET:${Channels.GET_TENANT_ACCOUNTS_SELECT}`, catchIpcHandler(accountController.getTenantAccountsForSelect));
ipcMain.handle(`GET:${Channels.GET_ACCOUNT_BY_ID}`, catchIpcHandler(accountController.getAccountById));
// Account Details APIs
ipcMain.handle(`GET:${Channels.GET_ACCOUNT_STATEMENT}`, catchIpcHandler(accountController.getAccountStatement));
ipcMain.handle(`GET:${Channels.GET_BALANCE_SUMMARY}`, catchIpcHandler(accountController.getBalanceSummary));
ipcMain.handle(`GET:${Channels.GET_ACCOUNT_STATISTICS}`, catchIpcHandler(accountController.getAccountStatistics));

// Exchange APIs
ipcMain.handle(`POST:${Channels.CREATE_STRUCTURED_EXCHANGE}`, catchIpcHandler(exchangeController.createStructuredExchange));
ipcMain.handle(`GET:${Channels.GET_EXCHANGE_BY_ID}`, catchIpcHandler(exchangeController.getExchangeById));
ipcMain.handle(`GET:${Channels.GET_EXCHANGE_HISTORY}`, catchIpcHandler(exchangeController.getExchangeHistory));
ipcMain.handle(`GET:${Channels.GET_EXCHANGE_STATISTICS}`, catchIpcHandler(exchangeController.getExchangeStatistics));
ipcMain.handle(`GET:${Channels.GET_DAILY_EXCHANGE_SUMMARY}`, catchIpcHandler(exchangeController.getDailyExchangeSummary));
ipcMain.handle(`GET:${Channels.GET_MONTHLY_EXCHANGE_REPORT}`, catchIpcHandler(exchangeController.getMonthlyExchangeReport));
ipcMain.handle(`GET:${Channels.GET_MOST_EXCHANGED_CURRENCIES}`, catchIpcHandler(exchangeController.getMostExchangedCurrencies));
ipcMain.handle(`GET:${Channels.GET_LOCATION_EXCHANGE_HISTORY}`, catchIpcHandler(exchangeController.getLocationExchangeHistory));
ipcMain.handle(`GET:${Channels.GET_LOCATION_BALANCE}`, catchIpcHandler(exchangeController.getLocationBalance));
// ipcMain.handle(`POST:${Channels.CREATE_EXCHANGE}`, catchIpcHandler(exchangeController.createExchange));
// ipcMain.handle(`POST:${Channels.EXCHANGE_BETWEEN_LOCATIONS}`, catchIpcHandler(exchangeController.exchangeBetweenLocations));


// Small Counter routes
ipcMain.handle(`POST:${Channels.INITIALIZE_SMALL_COUNTER}`, catchIpcHandler(smallCounterController.initializeSmallCounter));
ipcMain.handle(`GET:${Channels.GET_SMALL_COUNTER_BALANCES}`, catchIpcHandler(smallCounterController.getSmallCounterBalances));
ipcMain.handle(`GET:${Channels.GET_SMALL_COUNTER_BALANCE_BY_CURRENCY}`, catchIpcHandler(smallCounterController.getBalanceByCurrency));
ipcMain.handle(`POST:${Channels.ADJUST_SMALL_COUNTER_BALANCE}`, catchIpcHandler(smallCounterController.adjustBalance));
ipcMain.handle(`GET:${Channels.GET_SMALL_COUNTER_BALANCE_HISTORY}`, catchIpcHandler(smallCounterController.getBalanceHistory));
ipcMain.handle(`POST:${Channels.TRANSFER_TO_VAULT}`, catchIpcHandler(smallCounterController.transferToVault));
ipcMain.handle(`GET:${Channels.RECONCILE_SMALL_COUNTER_BALANCE}`, catchIpcHandler(smallCounterController.reconcileBalance));
ipcMain.handle(`GET:${Channels.GET_SMALL_COUNTER_DAILY_TRANSACTIONS}`, catchIpcHandler(smallCounterController.getDailyTransactions));
ipcMain.handle(`GET:${Channels.GET_SMALL_COUNTER_BALANCE_AT_DATE}`, catchIpcHandler(smallCounterController.getBalanceAtDate));
ipcMain.handle(`GET:${Channels.GET_ALL_SMALL_COUNTER_TRANSACTIONS}`, catchIpcHandler(smallCounterController.getAllTransactions));

// Cash Vault routes
ipcMain.handle(`POST:${Channels.INITIALIZE_CASH_VAULT}`, catchIpcHandler(cashVaultController.initializeCashVault));
ipcMain.handle(`GET:${Channels.GET_CASH_VAULT_BALANCES}`, catchIpcHandler(cashVaultController.getCashVaultBalances));
ipcMain.handle(`GET:${Channels.GET_CASH_VAULT_BALANCE_BY_CURRENCY}`, catchIpcHandler(cashVaultController.getBalanceByCurrency));
ipcMain.handle(`POST:${Channels.ADJUST_CASH_VAULT_BALANCE}`, catchIpcHandler(cashVaultController.adjustBalance));
ipcMain.handle(`GET:${Channels.GET_ALL_CASH_VAULT_TRANSACTIONS}`, catchIpcHandler(cashVaultController.getAllTransactions));
ipcMain.handle(`GET:${Channels.RECONCILE_CASH_VAULT_BALANCE}`, catchIpcHandler(cashVaultController.reconcileBalance));
ipcMain.handle(`GET:${Channels.GENERATE_CASH_VAULT_STATEMENT}`, catchIpcHandler(cashVaultController.generateCashVaultStatement));


// Currency routes
ipcMain.handle(`GET:${Channels.GET_ALL_CURRENCIES}`, catchIpcHandler(currencyController.getAllCurrencies));
ipcMain.handle(`GET:${Channels.GET_CURRENCY_TRANSACTIONS}`, catchIpcHandler(currencyController.getCurrencyTransactions));
ipcMain.handle(`GET:${Channels.GET_CURRENCY_SUMMARY}`, catchIpcHandler(currencyController.getCurrencySummary));
ipcMain.handle(`GET:${Channels.GET_CURRENCY_BALANCES}`, catchIpcHandler(currencyController.getCurrencyBalances));

// Bank Account routes
ipcMain.handle(`POST:${Channels.CREATE_BANK_ACCOUNT}`, catchIpcHandler(bankAccountController.createBankAccount));
ipcMain.handle(`GET:${Channels.GET_ALL_BANK_ACCOUNTS}`, catchIpcHandler(bankAccountController.getAllBankAccounts));
ipcMain.handle(`GET:${Channels.GET_BANK_ACCOUNT_BY_ID}`, catchIpcHandler(bankAccountController.getBankAccountById));
ipcMain.handle(`POST:${Channels.ADJUST_BANK_ACCOUNT_BALANCE}`, catchIpcHandler(bankAccountController.adjustBalance));
ipcMain.handle(`POST:${Channels.DEACTIVATE_BANK_ACCOUNT}`, catchIpcHandler(bankAccountController.deactivateAccount));
ipcMain.handle(`GET:${Channels.GET_BANK_ACCOUNT_TRANSACTIONS}`, catchIpcHandler(bankAccountController.getTransactionHistory));
ipcMain.handle(`DELETE:${Channels.DELETE_BANK_ACCOUNT}`, catchIpcHandler(bankAccountController.deleteBankAccount));
ipcMain.handle(`GET:${Channels.GENERATE_BANK_STATEMENT}`, catchIpcHandler(bankAccountController.generateBankStatement));
ipcMain.handle(`GET:${Channels.RECONCILE_BANK_ACCOUNT}`, catchIpcHandler(bankAccountController.reconcileBalance));
ipcMain.handle(`GET:${Channels.GET_BANK_ACCOUNTS_FOR_SELECT}`, catchIpcHandler(bankAccountController.getBankAccountsForSelect));

// Sale routes
ipcMain.handle(`POST:${Channels.CREATE_WALK_IN_SALE}`, catchIpcHandler(saleController.createWalkInSale));
ipcMain.handle(`POST:${Channels.CREATE_ACCOUNT_SALE}`, catchIpcHandler(saleController.createAccountSale));
ipcMain.handle(`DELETE:${Channels.DELETE_SALE}`, catchIpcHandler(saleController.deleteSale));
ipcMain.handle(`GET:${Channels.GET_SALES}`, catchIpcHandler(saleController.getSales));
ipcMain.handle(`GET:${Channels.GET_AVAILABLE_ITEMS}`, catchIpcHandler(saleController.getAvailableItems));
ipcMain.handle(`GET:${Channels.GET_SALE_BY_ID}`, catchIpcHandler(saleController.getSaleById));
ipcMain.handle(`GET:${Channels.GET_CONTAINER_BY_STOCK_ID}`, catchIpcHandler(saleController.getContainerByStockId));

// Expense routes
ipcMain.handle(
    `POST:${Channels.CREATE_EXPENSE}`,
    catchIpcHandler(expenseController.createExpense)
);

ipcMain.handle(
    `DELETE:${Channels.DELETE_EXPENSE}`,
    catchIpcHandler(expenseController.deleteExpense)
);

ipcMain.handle(
    `GET:${Channels.GET_EXPENSES}`,
    catchIpcHandler(expenseController.getExpenses)
);


// Property APIs
ipcMain.handle(`POST:${Channels.CREATE_PROPERTY}`, catchIpcHandler(propertyController.createProperty));
ipcMain.handle(`PUT:${Channels.UPDATE_PROPERTY}`, catchIpcHandler(propertyController.updateProperty));
ipcMain.handle(`DELETE:${Channels.DELETE_PROPERTY}`, catchIpcHandler(propertyController.deleteProperty));
ipcMain.handle(`GET:${Channels.GET_PROPERTY}`, catchIpcHandler(propertyController.getProperty));
ipcMain.handle(`GET:${Channels.GET_PROPERTIES}`, catchIpcHandler(propertyController.getProperties));
ipcMain.handle(`GET:${Channels.IS_PROPERTY_AVAILABLE}`, catchIpcHandler(propertyController.isPropertyAvailable));
ipcMain.handle(`GET:${Channels.GET_PROPERTY_HISTORY}`, catchIpcHandler(propertyController.getPropertyHistory));
ipcMain.handle(`GET:${Channels.GET_VACANT_PROPERTIES}`, catchIpcHandler(propertyController.getVacantProperties));

// Rented Property APIs
ipcMain.handle(`POST:${Channels.CREATE_RENTED_PROPERTY}`, catchIpcHandler(rentedPropertyController.createRentedProperty));
ipcMain.handle(`PUT:${Channels.UPDATE_RENTED_PROPERTY}`, catchIpcHandler(rentedPropertyController.updateRentedProperty));
ipcMain.handle(`PUT:${Channels.TERMINATE_RENTAL}`, catchIpcHandler(rentedPropertyController.terminateRental));
ipcMain.handle(`GET:${Channels.GET_RENTED_PROPERTY}`, catchIpcHandler(rentedPropertyController.getRentedProperty));
ipcMain.handle(`GET:${Channels.GET_RENTED_PROPERTIES}`, catchIpcHandler(rentedPropertyController.getRentedProperties));
ipcMain.handle(`PUT:${Channels.EXTEND_RENTAL}`, catchIpcHandler(rentedPropertyController.extendRental));
ipcMain.handle(`PUT:${Channels.ADJUST_RENT}`, catchIpcHandler(rentedPropertyController.adjustRent));
ipcMain.handle(`POST:${Channels.END_RENTAL}`, catchIpcHandler(rentedPropertyController.endRental));
ipcMain.handle(`POST:${Channels.RECORD_RENT_PAYMENT}`, catchIpcHandler(rentedPropertyController.recordRentPayment));
ipcMain.handle(`POST:${Channels.ADJUST_RENT_PAYMENT}`, catchIpcHandler(rentedPropertyController.adjustRentPayment));

// Ledger APIs
ipcMain.handle(`GET:${Channels.GET_ACCOUNT_STATEMENT_LEDGER}`, catchIpcHandler(ledgerController.getAccountStatement));
ipcMain.handle(`POST:${Channels.RECONCILE_BALANCE}`, catchIpcHandler(ledgerController.reconcileBalance));
ipcMain.handle(`GET:${Channels.GET_DAILY_LEDGER}`, catchIpcHandler(ledgerController.getDailyLedger));
ipcMain.handle(`GET:${Channels.GET_TRANSACTIONS_BY_CATEGORY}`, catchIpcHandler(ledgerController.getTransactionsByCategory));
ipcMain.handle(`GET:${Channels.GET_TRANSACTIONS_BY_LOCATION}`, catchIpcHandler(ledgerController.getTransactionsByLocation));
ipcMain.handle(`GET:${Channels.GET_BALANCE_SHEET}`, catchIpcHandler(ledgerController.getBalanceSheet));
ipcMain.handle(`GET:${Channels.GET_PROFIT_LOSS_STATEMENT}`, catchIpcHandler(ledgerController.getProfitLossStatement));
ipcMain.handle(`GET:${Channels.GET_PAGINATED_LEDGER}`, catchIpcHandler(ledgerController.getPaginatedLedgerEntries));
ipcMain.handle(`GET:${Channels.GET_ACCOUNT_LEDGER_ENTRIES_FOR_PDF}`, catchIpcHandler(ledgerController.getAccountLedgerEntriesForPDF));

// License APIs
ipcMain.handle(`GET:${Channels.GET_LICENSE}`, catchIpcHandler(licenseController.getLicense));
ipcMain.handle(`POST:${Channels.VERIFY_LICENSE}`, catchIpcHandler(licenseController.verifyLicense));

// Payment routes
ipcMain.handle(`POST:${Channels.CREATE_PAYMENT}`, catchIpcHandler(paymentController.createPayment));
ipcMain.handle(`DELETE:${Channels.DELETE_PAYMENT}`, catchIpcHandler(paymentController.deletePayment));
ipcMain.handle(`GET:${Channels.GET_PAYMENTS}`, catchIpcHandler(paymentController.getPayments));
ipcMain.handle(`GET:${Channels.GET_PAYMENT_SUMMARY_BY_ACCOUNT}`, catchIpcHandler(paymentController.getPaymentSummaryByAccount));
ipcMain.handle(`GET:${Channels.GET_DAILY_PAYMENT_REPORT}`, catchIpcHandler(paymentController.getDailyPaymentReport));
ipcMain.handle(`GET:${Channels.GET_OUTSTANDING_PAYMENTS}`, catchIpcHandler(paymentController.getOutstandingPayments));
ipcMain.handle(`GET:${Channels.GET_PAYMENT_STATISTICS}`, catchIpcHandler(paymentController.getPaymentStatistics));
ipcMain.handle(`POST:${Channels.TRANSFER_BETWEEN_LOCATIONS}`, catchIpcHandler(paymentController.transferBetweenLocations));

// Reset routes
ipcMain.handle(`POST:${Channels.CLEAR_ACCOUNTS}`, catchIpcHandler(resetController.clearAccounts));
ipcMain.handle(`POST:${Channels.CLEAR_ACCOUNT_BALANCES}`, catchIpcHandler(resetController.clearAccountBalances));
ipcMain.handle(`POST:${Channels.CLEAR_CONTAINERS}`, catchIpcHandler(resetController.clearContainers));
ipcMain.handle(`POST:${Channels.CLEAR_CARS}`, catchIpcHandler(resetController.clearCars));
ipcMain.handle(`POST:${Channels.CLEAR_CAR_PARTS}`, catchIpcHandler(resetController.clearCarParts));
ipcMain.handle(`POST:${Channels.CLEAR_ELECTRONICS}`, catchIpcHandler(resetController.clearElectronics));
ipcMain.handle(`POST:${Channels.CLEAR_SCRAPS}`, catchIpcHandler(resetController.clearScraps));
ipcMain.handle(`POST:${Channels.CLEAR_PARTNERS}`, catchIpcHandler(resetController.clearPartners));
ipcMain.handle(`POST:${Channels.CLEAR_SALES}`, catchIpcHandler(resetController.clearSales));
ipcMain.handle(`POST:${Channels.CLEAR_PAYMENTS}`, catchIpcHandler(resetController.clearPayments));
ipcMain.handle(`POST:${Channels.CLEAR_SMALL_COUNTER}`, catchIpcHandler(resetController.clearSmallCounter));
ipcMain.handle(`POST:${Channels.CLEAR_CASH_VAULT}`, catchIpcHandler(resetController.clearCashVault));
ipcMain.handle(`POST:${Channels.CLEAR_BANK_ACCOUNTS}`, catchIpcHandler(resetController.clearBankAccounts));
ipcMain.handle(`POST:${Channels.CLEAR_CURRENCY_EXCHANGES}`, catchIpcHandler(resetController.clearCurrencyExchanges));
ipcMain.handle(`POST:${Channels.CLEAR_EXPENSES}`, catchIpcHandler(resetController.clearExpenses));
ipcMain.handle(`POST:${Channels.CLEAR_PROPERTIES}`, catchIpcHandler(resetController.clearProperties));
ipcMain.handle(`POST:${Channels.CLEAR_RENTED_PROPERTIES}`, catchIpcHandler(resetController.clearRentedProperties));
ipcMain.handle(`POST:${Channels.CLEAR_LEDGER_ENTRIES}`, catchIpcHandler(resetController.clearLedgerEntries));
ipcMain.handle(`POST:${Channels.CLEAR_ALL_DATA}`, catchIpcHandler(resetController.clearAllData));

// Transfer operations
ipcMain.handle(`POST:${Channels.TRANSFER_BETWEEN_ACCOUNTS}`, catchIpcHandler(paymentController.transferBetweenAccounts));
ipcMain.handle(`GET:${Channels.GET_TRANSFERS}`, catchIpcHandler(paymentController.getTransfers));
ipcMain.handle(`DELETE:${Channels.DELETE_TRANSFER}`, catchIpcHandler(paymentController.deleteTransfer));

// Database backup handlers
ipcMain.handle(`POST:${Channels.CREATE_BACKUP}`, catchIpcHandler(backupController.createBackup));
ipcMain.handle(`POST:${Channels.RESTORE_BACKUP}`, catchIpcHandler(backupController.restoreBackup));

// Dashboard routes
ipcMain.handle(`GET:${Channels.GET_DASHBOARD_CASH_OVERVIEW}`, catchIpcHandler(dashboardController.getCashOverview));
ipcMain.handle(`GET:${Channels.GET_DASHBOARD_BANK_SUMMARY}`, catchIpcHandler(dashboardController.getBankAccountsSummary));
ipcMain.handle(`GET:${Channels.GET_DASHBOARD_TRANSACTION_STATS}`, catchIpcHandler(dashboardController.getTransactionStats));
ipcMain.handle(`GET:${Channels.GET_DASHBOARD_ACCOUNT_STATS}`, catchIpcHandler(dashboardController.getAccountStats));
ipcMain.handle(`GET:${Channels.GET_DASHBOARD_INVENTORY_SUMMARY}`, catchIpcHandler(dashboardController.getInventorySummary));
ipcMain.handle(`GET:${Channels.GET_DASHBOARD_CURRENCY_TRANSACTIONS}`, catchIpcHandler(dashboardController.getCurrencyTransactions));
ipcMain.handle(`GET:${Channels.GET_DASHBOARD_ACCOUNT_BALANCES}`, catchIpcHandler(dashboardController.getAccountBalances));
ipcMain.handle(`GET:${Channels.GET_DASHBOARD_LOCATION_TRANSFERS}`, catchIpcHandler(dashboardController.getLocationTransfers));
ipcMain.handle(`GET:${Channels.GET_DASHBOARD_SALES_TRENDS}`, catchIpcHandler(dashboardController.getSalesTrends));

// Manual Entry APIs
ipcMain.handle(`POST:${Channels.CREATE_MANUAL_ENTRY}`, catchIpcHandler(manualEntryController.createManualEntry));
ipcMain.handle(`GET:${Channels.GET_MANUAL_ENTRIES}`, catchIpcHandler(manualEntryController.getManualEntries));
ipcMain.handle(`GET:${Channels.GET_MANUAL_ENTRY_BY_ID}`, catchIpcHandler(manualEntryController.getManualEntryById));
ipcMain.handle(`POST:${Channels.VOID_MANUAL_ENTRY}`, catchIpcHandler(manualEntryController.voidManualEntry));


