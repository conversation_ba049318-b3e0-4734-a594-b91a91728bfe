import { useState } from 'react'
import { Tabs, Typography, theme } from 'antd'
import { FaBook, FaChartLine, FaBalanceScale, FaHistory } from 'react-icons/fa'
import { AccountLedger } from './components/AccountLedger'
import { DailyLedger } from './components/DailyLedger'
import { FinancialReports } from './components/FinancialReports'
// import { AuditTrail } from './components/AuditTrail'

const { Title } = Typography
const { useToken } = theme

const Ledger = () => {
  const { token } = useToken()
  const [activeTab, setActiveTab] = useState('1')

  const items = [
    {
      key: '1',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <FaBook />
          Account Ledger
        </span>
      ),
      children: <AccountLedger />
    },
    {
      key: '2',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <FaHistory />
          Daily Ledger
        </span>
      ),
      children: <DailyLedger />
    }
    // {
    //   key: '3',
    //   label: (
    //     <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
    //       <FaChartLine />
    //       Financial Reports
    //     </span>
    //   ),
    //   children: <FinancialReports />
    // }
    // {
    //   key: '4',
    //   label: (
    //     <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
    //       <FaBalanceScale />
    //       Audit & Verification
    //     </span>
    //   ),
    //   children: <AuditTrail />
    // }
  ]

  return (
    <div style={{ padding: 24 }}>
      <Tabs
        activeKey={activeTab}
        items={items}
        onChange={setActiveTab}
        size="large"
        style={{
          marginBottom: 24,
          background: token.colorBgContainer,
          padding: '16px 24px',
          borderRadius: token.borderRadius,
          boxShadow: token.boxShadow
        }}
      />
    </div>
  )
}

export default Ledger
