import { prisma } from '../db';
import { GetStockParams } from '@/common/types';
import { Prisma, ItemStatus } from '@prisma/client';

class StockService {
    private getStatusCondition(status?: ItemStatus | 'ALL') {
        if (!status || status === 'ALL') return {};
        return { status };
    }

    async getCars({ page, limit, status, search, containerId, orderBy = 'desc' }: GetStockParams) {
        const where: Prisma.CarWhereInput = {
            isDeleted: false,
            ...this.getStatusCondition(status),
            ...(containerId && { containerId }),
            ...(search && {
                OR: [
                    { chassisNumber: { contains: search, mode: 'insensitive' } },
                    { modelNumber: { contains: search, mode: 'insensitive' } },
                    { name: { contains: search, mode: 'insensitive' } }
                ]
            })
        };

        const [items, total] = await Promise.all([
            prisma.car.findMany({
                where,
                include: {
                    container: {
                        select: {
                            containerNumber: true,
                            openedAt: true
                        }
                    },
                    sale: {
                        where: { isDeleted: false },
                        select: {
                            id: true,
                            date: true,
                            totalAmount: true,
                            quantity: true
                        }
                    }
                },
                orderBy: { container: { openedAt: orderBy } },
                skip: (page - 1) * limit,
                take: limit
            }),
            prisma.car.count({ where })
        ]);

        return {
            items,
            total,
            page,
            totalPages: Math.ceil(total / limit)
        };
    }

    async getCarParts({ page, limit, status, search, containerId, orderBy = 'desc' }: GetStockParams) {
        const where: Prisma.CarPartWhereInput = {
            isDeleted: false,
            ...this.getStatusCondition(status),
            ...(containerId && { containerId }),
            ...(search && {
                name: { contains: search, mode: 'insensitive' }
            })
        };

        const [items, total] = await Promise.all([
            prisma.carPart.findMany({
                where,
                include: {
                    container: {
                        select: {
                            containerNumber: true,
                            openedAt: true
                        }
                    },
                    sale: {
                        where: { isDeleted: false },
                        select: {
                            id: true,
                            date: true,
                            totalAmount: true,
                            quantity: true
                        }
                    }
                },
                orderBy: { container: { openedAt: orderBy } },
                skip: (page - 1) * limit,
                take: limit
            }),
            prisma.carPart.count({ where })
        ]);

        return {
            items,
            total,
            page,
            totalPages: Math.ceil(total / limit)
        };
    }

    async getElectronics({ page, limit, status, search, containerId, orderBy = 'desc' }: GetStockParams) {
        const where: Prisma.ElectronicWhereInput = {
            isDeleted: false,
            ...this.getStatusCondition(status),
            ...(containerId && { containerId }),
            ...(search && { name: { contains: search, mode: 'insensitive' } })
        };

        const [items, total] = await Promise.all([
            prisma.electronic.findMany({
                where,
                include: {
                    container: {
                        select: {
                            containerNumber: true,
                            openedAt: true
                        }
                    },
                    sale: {
                        where: { isDeleted: false },
                        select: {
                            id: true,
                            date: true,
                            totalAmount: true,
                            quantity: true
                        }
                    }
                },
                orderBy: { container: { openedAt: orderBy } },
                skip: (page - 1) * limit,
                take: limit
            }),
            prisma.electronic.count({ where })
        ]);

        return { items, total, page, totalPages: Math.ceil(total / limit) };
    }

    async getScraps({ page, limit, status, search, containerId, orderBy = 'desc' }: GetStockParams) {
        const where: Prisma.ScrapWhereInput = {
            isDeleted: false,
            ...this.getStatusCondition(status),
            ...(containerId && { containerId }),
            ...(search && { description: { contains: search, mode: 'insensitive' } })
        };

        const [items, total] = await Promise.all([
            prisma.scrap.findMany({
                where,
                include: {
                    container: {
                        select: {
                            containerNumber: true,
                            openedAt: true
                        }
                    },
                    sale: {
                        where: { isDeleted: false },
                        select: {
                            id: true,
                            date: true,
                            totalAmount: true,
                            quantity: true
                        }
                    }
                },
                orderBy: { container: { openedAt: orderBy } },
                skip: (page - 1) * limit,
                take: limit
            }),
            prisma.scrap.count({ where })
        ]);

        return { items, total, page, totalPages: Math.ceil(total / limit) };
    }

    // Additional useful methods
    async getStockSummary() {
        const [cars, carParts, electronics, scraps]: any = await Promise.all([
            // Cars - simple count
            prisma.$queryRaw`
                SELECT 
                    status,
                    COUNT(*) as count
                FROM "Car"
                WHERE "isDeleted" = false
                GROUP BY status
            `,

            // Car Parts - calculate quantities in a single query
            prisma.$queryRaw`
                SELECT 
                    status,
                    COUNT(*) as count,
                    SUM("initialQuantity") as total_quantity,
                    SUM("quantity") as remaining_quantity,
                    SUM("initialQuantity" - "quantity") as sold_quantity
                FROM "CarPart"
                WHERE "isDeleted" = false
                GROUP BY status
            `,

            // Electronics - calculate quantities in a single query
            prisma.$queryRaw`
                SELECT 
                    status,
                    COUNT(*) as count,
                    SUM("initialQuantity") as total_quantity,
                    SUM("quantity") as remaining_quantity,
                    SUM("initialQuantity" - "quantity") as sold_quantity
                FROM "Electronic"
                WHERE "isDeleted" = false
                GROUP BY status
            `,

            // Scraps - calculate quantities in a single query
            prisma.$queryRaw`
                SELECT 
                    status,
                    COUNT(*) as count,
                    SUM("initialQuantity") as total_quantity,
                    SUM("quantity") as remaining_quantity,
                    SUM("initialQuantity" - "quantity") as sold_quantity
                FROM "Scrap"
                WHERE "isDeleted" = false
                GROUP BY status
            `
        ]);

        // Transform raw results to match the expected format
        return {
            cars: cars.map((group: any) => ({
                status: group.status,
                _count: { _all: Number(group.count) }
            })),
            carParts: carParts.map((group: any) => ({
                status: group.status,
                _count: { _all: Number(group.count) },
                _sum: {
                    total: Number(group.total_quantity) || 0,
                    remaining: Number(group.remaining_quantity) || 0,
                    sold: Number(group.sold_quantity) || 0
                }
            })),
            electronics: electronics.map((group: any) => ({
                status: group.status,
                _count: { _all: Number(group.count) },
                _sum: {
                    total: Number(group.total_quantity) || 0,
                    remaining: Number(group.remaining_quantity) || 0,
                    sold: Number(group.sold_quantity) || 0
                }
            })),
            scraps: scraps.map((group: any) => ({
                status: group.status,
                _count: { _all: Number(group.count) },
                _sum: {
                    total: Number(group.total_quantity) || 0,
                    remaining: Number(group.remaining_quantity) || 0,
                    sold: Number(group.sold_quantity) || 0
                }
            }))
        };
    }
}

export const stockService = new StockService();