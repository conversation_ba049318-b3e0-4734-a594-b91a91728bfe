import { createContext, useContext, useState, useEffect } from 'react'
import { accountsApi } from '../services'
import { useApi } from '../hooks'

export interface AccountOption {
  value: string
  label: string
}

interface AccountContextType {
  tenants: AccountOption[] | []
  accounts: AccountOption[] | []
  loading: boolean
  error: any
  errorMessage: string | null
  refreshAccounts: () => Promise<void>
}

const AccountContext = createContext<AccountContextType>({
  tenants: [],
  accounts: [],
  loading: false,
  error: null,
  errorMessage: null,
  refreshAccounts: async () => {}
})

export const useAccountContext = () => useContext(AccountContext)

export const AccountProvider = ({ children }: { children: React.ReactNode }) => {
  const [accounts, setAccounts] = useState<AccountOption[]>([])
  const [tenants, setTenants] = useState<AccountOption[]>([])

  const {
    data: accountsData,
    isLoading: loading,
    request: getAccounts,
    error,
    errorMessage
  } = useApi<AccountOption[], []>(accountsApi.getCustomerAccountsForSelect)

  const {
    data: tenantsData,
    isLoading: tenantsLoading,
    request: getTenants,
    error: tenantsError,
    errorMessage: tenantsErrorMessage
  } = useApi<AccountOption[], []>(accountsApi.getTenantAccountsForSelect)

  const fetchAccounts = async () => {
    if (!accounts.length) {
      await getAccounts()
    }
    if (!tenants.length) {
      await getTenants()
    }
  }

  const refreshAccounts = async () => {
    setAccounts([])
    setTenants([])
    await getTenants()
    await getAccounts()
  }

  useEffect(() => {
    fetchAccounts()
  }, [])

  useEffect(() => {
    // console.log('accountsData', accountsData)
    if (accountsData) {
      setAccounts(accountsData)
    }
  }, [accountsData])

  useEffect(() => {
    if (tenantsData) {
      setTenants(tenantsData)
    }
  }, [tenantsData])

  return (
    <AccountContext.Provider
      value={{ accounts, tenants, loading, error, errorMessage, refreshAccounts }}
    >
      {children}
    </AccountContext.Provider>
  )
}
