import { rentedPropertyService } from '../services/rentedProperty'
import type { AdjustRentPaymentData, CreateRentedPropertyData, GetRentedPropertiesParams, IRequest, RecordRentPaymentData, UpdateRentedPropertyData } from '@/common/types'

class RentedPropertyController {
    async createRentedProperty(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body?.data as CreateRentedPropertyData
        const userId = req.body?.userId as string
        if (!data || !userId) throw new Error('Data and user ID are required')

        return rentedPropertyService.createRentedProperty(data, userId)
    }

    async updateRentedProperty(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const id = req.params?.id as string
        if (!id) throw new Error('Rental ID is required')

        const data = req.body as UpdateRentedPropertyData
        return rentedPropertyService.updateRentedProperty(id, data)
    }

    async terminateRental(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const id = req.params?.id as string
        if (!id) throw new Error('Rental ID is required')

        const reason = req.body?.reason as string
        const endDate = req.body?.endDate as Date
        if (!reason || !endDate) throw new Error('Reason and end date are required')

        return rentedPropertyService.terminateRental(id, reason, endDate)
    }

    async recordRentPayment(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as RecordRentPaymentData
        if (!data.amount) throw new Error('Amount is required')
        if (!data.rentedPropertyId) throw new Error('Rental ID is required')
        if (!data.userId) throw new Error('User ID is required')

        return rentedPropertyService.recordRentPayment(data)
    }

    async getRentedProperty(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const id = req.params?.id as string
        if (!id) throw new Error('Rental ID is required')

        return rentedPropertyService.getRentedProperty(id)
    }

    async getRentedProperties(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const params = req.query as GetRentedPropertiesParams
        return rentedPropertyService.getRentedProperties(params)
    }

    async extendRental(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const id = req.params?.id as string
        if (!id) throw new Error('Rental ID is required')

        const newEndDate = req.body?.newEndDate as Date
        if (!newEndDate) throw new Error('New end date is required')

        return rentedPropertyService.extendRental(id, newEndDate)
    }

    async adjustRent(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const id = req.params?.id as string
        if (!id) throw new Error('Rental ID is required')

        const newAmount = req.body?.newAmount as number
        if (!newAmount) throw new Error('New amount is required')

        return rentedPropertyService.adjustRent(id, newAmount)
    }

    async endRental(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const rentalId = req.body?.rentalId as string
        if (!rentalId) throw new Error('Rental ID is required')

        return rentedPropertyService.endRental(rentalId)
    }

    async adjustRentPayment(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as AdjustRentPaymentData
        if (!data.rentedPropertyId) throw new Error('Rental ID is required')
        if (!data.amount) throw new Error('Amount is required')
        if (!data.userId) throw new Error('User ID is required')

        return rentedPropertyService.adjustRentPayment(data)
    }
}
export const rentedPropertyController = new RentedPropertyController();

