import { useEffect, useState } from 'react'
import { App, DatePicker, Form, Select, Space, Table, Tag } from 'antd'
import type { GetExchangeHistoryParams } from '@/common/types'
import { exchangeApi } from '@/renderer/services'
import dayjs from 'dayjs'
import { title } from 'process'

const { RangePicker } = DatePicker

// type TransactionLocation =
//   | 'SMALL_COUNTER'
//   | 'CASH_VAULT'
//   | 'BANK_ACCOUNT'
//   | 'ACCOUNT'
//   | 'EXTERNAL'
//   | 'OTHER'

const TransactionLocation = {
  // SMALL_COUNTER: 'SMALL_COUNTER',
  CASH_VAULT: 'CASH_VAULT',
  BANK_ACCOUNT: 'BANK_ACCOUNT',
  ACCOUNT: 'ACCOUNT',
  EXTERNAL: 'EXTERNAL',
  OTHER: 'OTHER'
}

const CURRENCIES = ['PKR', 'USD', 'AED', 'AFN']
const LOCATIONS = [
  // { value: TransactionLocation.SMALL_COUNTER, label: 'Small Counter' },
  { value: TransactionLocation.CASH_VAULT, label: 'Cash Vault' },
  { value: TransactionLocation.BANK_ACCOUNT, label: 'Bank Account' }
]

export const ExchangeHistory = ({ refreshTrigger }: { refreshTrigger: any }) => {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<any>({ exchanges: [], total: 0 })
  const [filters, setFilters] = useState<GetExchangeHistoryParams>({
    page: 1,
    limit: 20,
    sortOrder: 'asc' // Default to oldest first (ascending)
  })

  const { message } = App.useApp()

  const fetchHistory = async () => {
    // try {
    setLoading(true)
    const response = await exchangeApi.getExchangeHistory(filters)
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    setData(response.data.data)
    // } catch (error) {
    //   console.error('Failed to fetch exchange history:', error)
    // } finally {
    //   setLoading(false)
    // }
  }

  useEffect(() => {
    fetchHistory()
  }, [filters, refreshTrigger])

  const handleDateRangeChange = (_: any, dateStrings: [string, string]) => {
    if (!dateStrings[0] || !dateStrings[1]) {
      const { startDate, endDate, ...rest } = filters
      setFilters(rest)
      return
    }

    setFilters({
      ...filters,
      startDate: new Date(dateStrings[0]),
      endDate: new Date(dateStrings[1])
    })
  }

  const columns = [
    {
      title: 'Sr. No',
      key: 'serialNumber',
      width: 70,
      render: (_: any, __: any, index: number) => {
        // Calculate serial number based on current page and page size
        return (filters.page - 1) * filters.limit + index + 1
      }
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => dayjs(date).format('DD/MM/YYYY')
    },
    {
      title: 'From',
      key: 'from',
      render: (record: any) => (
        <Space direction="horizontal" className="gap-0" size="small">
          <Tag color="blue">{record.fromCurrency}</Tag>
          {record.fromAmount.toLocaleString()}
        </Space>
      )
    },
    {
      title: 'To',
      key: 'to',
      render: (record: any) => (
        <Space direction="horizontal" className="gap-0" size="small">
          <Tag color="green">{record.toCurrency}</Tag>
          {record.toAmount.toLocaleString()}
        </Space>
      )
    },
    {
      title: 'Rate',
      dataIndex: 'displayRate',
      key: 'rate',
      render: (rate: number) => rate.toFixed(2)
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description'
      // width: '30%'
    }
  ]

  return (
    <div className="flex flex-col gap-4">
      {/* Filters */}
      <div className="flex flex-wrap gap-4">
        <Form.Item label="Date Range" className="mb-0">
          <RangePicker
            value={
              filters.startDate && filters.endDate
                ? [dayjs(filters.startDate), dayjs(filters.endDate)]
                : null
            }
            onChange={handleDateRangeChange}
          />
        </Form.Item>

        <Form.Item label="From Currency" className="mb-0">
          <Select
            allowClear
            placeholder="Select currency"
            value={filters.fromCurrency}
            onChange={(value) => setFilters({ ...filters, fromCurrency: value })}
            // style={{ width: 120 }}
          >
            {CURRENCIES.map((currency) => (
              <Select.Option key={currency} value={currency}>
                {currency}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item label="To Currency" className="mb-0">
          <Select
            allowClear
            placeholder="Select currency"
            value={filters.toCurrency}
            onChange={(value) => setFilters({ ...filters, toCurrency: value })}
            // style={{ width: 120 }}
          >
            {CURRENCIES.map((currency) => (
              <Select.Option key={currency} value={currency}>
                {currency}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item label="Sort Order" className="mb-0">
          <Select
            value={filters.sortOrder}
            onChange={(value: 'asc' | 'desc') => setFilters({ ...filters, sortOrder: value })}
            style={{ width: 150 }}
          >
            <Select.Option value="asc">Oldest First</Select.Option>
            <Select.Option value="desc">Newest First</Select.Option>
          </Select>
        </Form.Item>

        {/* <Form.Item label="Location" className="mb-0">
          <Select
            allowClear
            placeholder="Select location"
            value={filters.location}
            onChange={(value) => setFilters({ ...filters, location: value })}
            // style={{ width: 150 }}
            options={LOCATIONS}
          />
        </Form.Item> */}
      </div>

      {/* Table */}
      <Table
        columns={columns}
        dataSource={data.exchanges}
        rowKey="id"
        loading={loading}
        virtual
        sticky
        size="small"
        pagination={{
          total: data.total,
          current: filters.page,
          pageSize: filters.limit,
          position: ['topRight'],
          onChange: (page) => setFilters({ ...filters, page }),
          showSizeChanger: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
        }}
        // scroll={{ x: 'max-content' }}
      />
    </div>
  )
}
