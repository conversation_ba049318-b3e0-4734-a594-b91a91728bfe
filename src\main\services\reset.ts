import { prisma } from '../db'

class ResetService {
    // Account Management
    async clearAccounts() {
        await prisma.account.deleteMany()
    }

    async clearAccountBalances() {
        await prisma.accountBalance.deleteMany()
    }

    // Container & Items
    async clearContainers() {
        await prisma.container.deleteMany()
    }

    async clearCars() {
        await prisma.car.deleteMany()
    }

    async clearCarParts() {
        await prisma.carPart.deleteMany()
    }

    async clearElectronics() {
        await prisma.electronic.deleteMany()
    }

    async clearScraps() {
        await prisma.scrap.deleteMany()
    }

    // Partners
    async clearPartners() {
        await prisma.partner.deleteMany()
    }

    // Sales & Payments
    async clearSales() {
        await prisma.sale.deleteMany()
    }

    async clearPayments() {
        await prisma.payment.deleteMany()
    }

    // Cash Management
    async clearSmallCounter() {
        await prisma.smallCounter.deleteMany()
    }

    async clearCashVault() {
        await prisma.cashVault.deleteMany()
    }

    async clearBankAccounts() {
        await prisma.bankAccount.deleteMany()
    }

    async clearCurrencyExchanges() {
        await prisma.currencyExchange.deleteMany()
    }

    // Expenses
    async clearExpenses() {
        await prisma.expense.deleteMany()
    }

    // Properties & Rent
    async clearProperties() {
        await prisma.property.deleteMany()
    }

    async clearRentedProperties() {
        await prisma.rentedProperty.deleteMany()
    }

    // Ledger
    async clearLedgerEntries() {
        await prisma.ledgerEntry.deleteMany()
    }


    // Clear All Data
    async clearAllData() {
        // Clear in order of dependencies
        await this.clearRentedProperties()
        await this.clearProperties()
        await this.clearLedgerEntries()
        await this.clearCurrencyExchanges()
        await this.clearExpenses()
        await this.clearPayments()
        await this.clearSales()
        await this.clearAccountBalances()
        await this.clearAccounts()
        await this.clearCars()
        await this.clearCarParts()
        await this.clearElectronics()
        await this.clearScraps()
        await this.clearContainers()
        await this.clearPartners()
        await this.clearSmallCounter()
        await this.clearCashVault()
        await this.clearBankAccounts()
    }
}

export const resetService = new ResetService()
