import { Card, Row, Col, Typography, Spin } from 'antd'
import { UserOutlined } from '@ant-design/icons'
import { formatCurrency } from '@/renderer/utils'

const { Text } = Typography

interface AccountCardProps {
  accountDetails: any
  loading: boolean
}

export const AccountCard = ({ accountDetails, loading }: AccountCardProps) => {
  if (!accountDetails) return null

  return (
    <Card 
      className="mb-4" 
      size="small" 
      title={
        <div className="flex items-center">
          <UserOutlined className="mr-2" />
          <span>Account Details</span>
        </div>
      }
      loading={loading}
    >
      <Row gutter={16}>
        <Col span={12}>
          <div className="mb-2">
            <Text strong>Name:</Text> {accountDetails.name}
          </div>
          <div className="mb-2">
            <Text strong>Phone:</Text> {accountDetails.phoneNumber}
          </div>
          <div>
            <Text strong>Type:</Text> {accountDetails.type}
          </div>
        </Col>
        <Col span={12}>
          <div>
            <Text strong>Balances:</Text>
            {accountDetails.balances.map((balance: any) => (
              <div key={balance.currency.code} className="flex justify-between">
                <span>{balance.currency.code}:</span>
                <span className={balance.balance >= 0 ? 'text-green-600' : 'text-red-600'}>
                  {formatCurrency(balance.balance, balance.currency.code)}
                </span>
              </div>
            ))}
          </div>
        </Col>
      </Row>
    </Card>
  )
}
