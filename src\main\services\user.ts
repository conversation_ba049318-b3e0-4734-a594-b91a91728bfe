import { prisma } from '../db';
import { hash, compare } from 'bcryptjs';
import { CreateUserData, UpdatePasswordData, ResetPasswordData, GetUsersParams, LoginData } from '@/common/types';

class UserService {
    async login(data: LoginData) {
        // Find user by username
        const user = await prisma.user.findUnique({
            where: { username: data.username }
        });

        if (!user) {
            throw new Error('Invalid username or password');
        }

        // Check if user is active
        if (!user.isActive) {
            throw new Error('Your account has been deactivated. Please contact an administrator.');
        }

        // Verify password
        const isValid = await compare(data.password, user.password);
        if (!isValid) {
            throw new Error('Invalid username or password');
        }

        // Return user data without sensitive information
        return {
            id: user.id,
            username: user.username,
            name: user.name,
            role: user.role,
            isActive: user.isActive
        };
    }

    async createUser(data: CreateUserData) {
        const existingUser = await prisma.user.findUnique({
            where: { username: data.username }
        });

        if (existingUser) {
            throw new Error('Username already exists');
        }

        const hashedPassword = await hash(data.password, 10);

        return await prisma.user.create({
            data: {
                ...data,
                password: hashedPassword
            },
            select: {
                id: true,
                username: true,
                name: true,
                role: true,
                isActive: true
            }
        });
    }

    async updatePassword(data: UpdatePasswordData) {
        const user = await prisma.user.findUnique({
            where: { id: data.userId }
        });

        if (!user) {
            throw new Error('User not found');
        }

        const isValid = await compare(data.currentPassword, user.password);
        if (!isValid) {
            throw new Error('Current password is incorrect');
        }

        const hashedPassword = await hash(data.newPassword, 10);

        return await prisma.user.update({
            where: { id: data.userId },
            data: { password: hashedPassword },
            select: {
                id: true,
                username: true,
                name: true,
                role: true
            }
        });
    }

    async resetPassword(data: ResetPasswordData) {
        const admin = await prisma.user.findUnique({
            where: { id: data.adminId }
        });

        if (!admin || admin.role !== 'ADMIN') {
            throw new Error('Unauthorized');
        }

        const hashedPassword = await hash(data.newPassword, 10);

        return await prisma.user.update({
            where: { id: data.userId },
            data: { password: hashedPassword },
            select: {
                id: true,
                username: true,
                name: true,
                role: true
            }
        });
    }

    async getUsers({ page, limit, includeInactive = false }: GetUsersParams) {
        const where = includeInactive ? {} : { isActive: true };

        const [users, total] = await Promise.all([
            prisma.user.findMany({
                where,
                select: {
                    id: true,
                    username: true,
                    name: true,
                    role: true,
                    isActive: true,
                    createdAt: true
                },
                skip: (page - 1) * limit,
                take: limit
            }),
            prisma.user.count({ where })
        ]);

        return { users, total, page, totalPages: Math.ceil(total / limit) };
    }

    async deactivateUser(userId: string, adminId: string) {
        const admin = await prisma.user.findUnique({
            where: { id: adminId }
        });

        if (!admin || admin.role !== 'ADMIN') {
            throw new Error('Unauthorized');
        }

        return await prisma.user.update({
            where: { id: userId },
            data: { isActive: false }
        });
    }
}

export const userService = new UserService();