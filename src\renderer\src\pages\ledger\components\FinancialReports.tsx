import { useState } from 'react'
import {
  Card,
  DatePicker,
  Table,
  Space,
  Typography,
  Tabs,
  Tag,
  Statistic,
  Row,
  Col,
  App
} from 'antd'
import { FaChartBar, FaMapMarkerAlt, FaBalanceScale, FaChartLine } from 'react-icons/fa'
import { ledgerApi } from '@/renderer/services'
import { formatCurrency } from '@/renderer/utils'
import dayjs from 'dayjs'

const { Title } = Typography
const { RangePicker } = DatePicker

export const FinancialReports = () => {
  const [loading, setLoading] = useState(false)
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>()
  const [categoryData, setCategoryData] = useState<any[]>([])
  const [locationData, setLocationData] = useState<any[]>([])
  const [balanceSheet, setBalanceSheet] = useState<any>({})
  const [profitLoss, setProfitLoss] = useState<any>({})

  const { message } = App.useApp()

  const handleDateRangeChange = async (dates: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
    if (!dates) return
    setDateRange(dates)
  }

  const loadCategoryReport = async () => {
    if (!dateRange) return

    setLoading(true)
    const response = await ledgerApi.getTransactionsByCategory({
      startDate: dateRange[0].toDate(),
      endDate: dateRange[1].toDate()
    })
    setLoading(false)

    console.log('loadCategoryReport response', response)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    setCategoryData(response.data.data)
    message.success('Category report loaded successfully')
  }

  const loadLocationReport = async () => {
    if (!dateRange) return
    setLoading(true)
    const response = await ledgerApi.getTransactionsByLocation({
      startDate: dateRange[0].toDate(),
      endDate: dateRange[1].toDate()
    })
    setLoading(false)

    console.log('loadLocationReport response', response)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    setLocationData(response.data.data)
    message.success('Location report loaded successfully')
  }

  const loadBalanceSheet = async () => {
    setLoading(true)
    const response = await ledgerApi.getBalanceSheet({
      date: dateRange?.[1].toDate() // Use end date for balance sheet
    })

    setLoading(false)

    console.log('loadBalanceSheet response', response)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    setBalanceSheet(response.data.data)
    message.success('Balance sheet loaded successfully')
  }

  const loadProfitLoss = async () => {
    if (!dateRange) return
    setLoading(true)
    const response = await ledgerApi.getProfitLossStatement({
      startDate: dateRange[0].toDate(),
      endDate: dateRange[1].toDate()
    })
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    console.log('loadProfitLoss response', response)

    setProfitLoss(response.data.data)
    message.success('Profit & loss loaded successfully')
  }

  const categoryColumns = [
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      render: (category: string) => <Tag color="blue">{category.replace(/_/g, ' ')}</Tag>
    },
    {
      title: 'Total Amount',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount: number, record: any) => formatCurrency(amount, record.currency)
    },
    {
      title: 'Transaction Count',
      dataIndex: 'count',
      key: 'count'
    }
  ]

  const locationColumns = [
    {
      title: 'Location',
      dataIndex: 'location',
      key: 'location',
      render: (location: string) => <Tag color="purple">{location.replace(/_/g, ' ')}</Tag>
    },
    {
      title: 'Total Amount',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount: number, record: any) => formatCurrency(amount, record.currency)
    },
    {
      title: 'Transaction Count',
      dataIndex: 'count',
      key: 'count'
    }
  ]

  const items = [
    {
      key: '1',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <FaChartBar />
          Transactions by Category
        </span>
      ),
      children: (
        <Table
          columns={categoryColumns}
          dataSource={categoryData}
          loading={loading}
          rowKey="category"
          pagination={false}
        />
      )
    },
    {
      key: '2',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <FaMapMarkerAlt />
          Transactions by Location
        </span>
      ),
      children: (
        <Table
          columns={locationColumns}
          dataSource={locationData}
          loading={loading}
          rowKey="location"
          pagination={false}
        />
      )
    },
    {
      key: '3',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <FaBalanceScale />
          Balance Sheet
        </span>
      ),
      children: (
        <Row gutter={[16, 16]}>
          {Object.entries(balanceSheet).map(([currency, data]: [string, any]) => (
            <Col key={currency} xs={24} sm={12} md={8}>
              <Card size="small" title={currency}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Statistic
                    title="Total Assets"
                    value={data.totalAssets}
                    precision={2}
                    valueStyle={{ color: '#52c41a' }}
                  />
                  <Statistic
                    title="Total Liabilities"
                    value={data.totalLiabilities}
                    precision={2}
                    valueStyle={{ color: '#f5222d' }}
                  />
                  <Statistic
                    title="Net Worth"
                    value={data.netWorth}
                    precision={2}
                    valueStyle={{ color: data.netWorth >= 0 ? '#52c41a' : '#f5222d' }}
                  />
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
      )
    },
    {
      key: '4',
      label: (
        <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <FaChartLine />
          Profit & Loss
        </span>
      ),
      children: (
        <Row gutter={[16, 16]}>
          {Object.entries(profitLoss).map(([currency, data]: [string, any]) => (
            <Col key={currency} xs={24} sm={12} md={8}>
              <Card size="small" title={currency}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Statistic
                    title="Total Revenue"
                    value={data.totalRevenue}
                    precision={2}
                    valueStyle={{ color: '#52c41a' }}
                  />
                  <Statistic
                    title="Total Expenses"
                    value={data.totalExpenses}
                    precision={2}
                    valueStyle={{ color: '#f5222d' }}
                  />
                  <Statistic
                    title="Net Profit"
                    value={data.netProfit}
                    precision={2}
                    valueStyle={{ color: data.netProfit >= 0 ? '#52c41a' : '#f5222d' }}
                  />
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
      )
    }
  ]

  const handleTabChange = (key: string) => {
    switch (key) {
      case '1':
        loadCategoryReport()
        break
      case '2':
        loadLocationReport()
        break
      case '3':
        loadBalanceSheet()
        break
      case '4':
        loadProfitLoss()
        break
    }
  }

  return (
    <Card>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Space wrap align="center" style={{ justifyContent: 'space-between', width: '100%' }}>
          <Title level={4} style={{ margin: 0 }}>
            Financial Reports
          </Title>
          <RangePicker
            onChange={(dates) => handleDateRangeChange(dates as [dayjs.Dayjs, dayjs.Dayjs])}
            style={{ width: 300 }}
          />
        </Space>

        <Tabs items={items} onChange={handleTabChange} />
      </Space>
    </Card>
  )
}
