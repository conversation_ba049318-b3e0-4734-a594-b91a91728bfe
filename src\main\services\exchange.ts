import { prisma } from '../db';
import {
    CreateExchangeData,
    GetExchangeHistoryParams,
    ExchangeStatistics,
    DailyExchangeSummary,
    MonthlyExchangeReport,
    LocationExchangeHistory,
    CreateStructuredExchangeData,
    ExchangeValidationResult
} from '@/common/types';
import { AccountBalance, CashVault, LedgerEntry, Prisma, SmallCounter, TransactionLocation } from '@prisma/client';

class ExchangeService {


    async createStructuredExchange(data: CreateStructuredExchangeData & { isReverseRate?: boolean, displayRate?: number }) {
        return await prisma.$transaction(async (tx) => {
            // Validate the exchange
            const validation = await this.validateExchangeInput(tx, data);
            if (!validation.isValid) {
                throw new Error(validation.errors.join(', '));
            }

            // Calculate output amount with proper precision
            const outputAmount = Number((data.inputAmount * data.exchangeRate).toFixed(2));

            // Format description based on whether it's a reverse rate or not
            const rateDescription = data.isReverseRate
                ? `@ ${data.displayRate?.toFixed(2)} ${data.inputCurrency}/${data.outputCurrency}`
                : `@ ${data.exchangeRate} ${data.outputCurrency}/${data.inputCurrency}`;

            let inputDeposit: LedgerEntry | null = null;

            // Step 1: Handle input currency based on source
            if (data.inputSource === 'NEW_DEPOSIT') {
                // New deposit: Record incoming currency to account's ledger and update location balance
                if (!data.inputLocation) {
                    throw new Error('Input location is required for new deposits');
                }

                inputDeposit = await tx.ledgerEntry.create({
                    data: {
                        amount: data.inputAmount,
                        type: 'CREDIT',
                        description: `Received ${data.inputAmount} ${data.inputCurrency} for exchange, ${data.description || ''}`,
                        sourceType: data.inputLocation,
                        destinationType: 'ACCOUNT',
                        transactionType: 'CURRENCY_DEPOSIT',
                        currency: { connect: { code: data.inputCurrency } },
                        account: { connect: { id: data.accountId } },
                        createdBy: { connect: { id: data.userId } },
                        ...(data.inputLocation === 'BANK_ACCOUNT' && {
                            bankAccount: { connect: { id: data.inputBankId! } }
                        })
                    }
                });

                // Update input location balance (increase since we're receiving currency)
                await this.updateLocationBalance(
                    tx,
                    data.inputLocation,
                    data.inputCurrency,
                    data.inputAmount,  // Positive amount to increase balance
                    data.inputBankId
                );

                // Update account balance (increase)
                await this.updateAccountBalance(
                    tx,
                    data.accountId,
                    data.inputCurrency,
                    data.inputAmount
                );
            }

            // Step 2: Exchange the input currency (remove from account's ledger)
            const exchangeDebit = await tx.ledgerEntry.create({
                data: {
                    amount: data.inputAmount,
                    type: 'DEBIT',
                    description: `Exchange ${data.inputAmount} ${data.inputCurrency} to ${outputAmount.toFixed(2)} ${data.outputCurrency} ${rateDescription}, ${data.description || ''}`,
                    sourceType: 'ACCOUNT',
                    destinationType: 'ACCOUNT',
                    transactionType: 'CURRENCY_EXCHANGE',
                    currency: { connect: { code: data.inputCurrency } },
                    account: { connect: { id: data.accountId } },
                    createdBy: { connect: { id: data.userId } }
                }
            });

            // Update account balance (decrease input currency)
            await this.updateAccountBalance(
                tx,
                data.accountId,
                data.inputCurrency,
                -data.inputAmount
            );

            // Step 3: Credit the output currency to account's ledger
            const exchangeCredit = await tx.ledgerEntry.create({
                data: {
                    amount: outputAmount,
                    type: 'CREDIT',
                    description: `Received ${outputAmount} ${data.outputCurrency} from exchange, ${data.description || ''}`,
                    sourceType: 'ACCOUNT',
                    destinationType: 'ACCOUNT',
                    transactionType: 'CURRENCY_DEPOSIT',
                    currency: { connect: { code: data.outputCurrency } },
                    account: { connect: { id: data.accountId } },
                    createdBy: { connect: { id: data.userId } }
                }
            });

            // Update account balance (increase output currency)
            await this.updateAccountBalance(
                tx,
                data.accountId,
                data.outputCurrency,
                outputAmount
            );

            let outputWithdrawal: LedgerEntry | null = null;

            // Step 4: If not keeping in account, withdraw the exchanged amount
            if (!data.keepInAccount) {
                if (!data.outputLocation) {
                    throw new Error('Output location is required when not keeping in account');
                }

                outputWithdrawal = await tx.ledgerEntry.create({
                    data: {
                        amount: outputAmount,
                        type: 'DEBIT',
                        description: `Withdrawn ${outputAmount} ${data.outputCurrency} from exchange, ${data.description || ''}`,
                        sourceType: 'ACCOUNT',
                        destinationType: data.outputLocation,
                        transactionType: 'CURRENCY_WITHDRAWAL',
                        currency: { connect: { code: data.outputCurrency } },
                        account: { connect: { id: data.accountId } },
                        createdBy: { connect: { id: data.userId } },
                        ...(data.outputLocation === 'BANK_ACCOUNT' && {
                            bankAccount: { connect: { id: data.outputBankId! } }
                        })
                    }
                });

                // Update account balance (decrease output currency)
                await this.updateAccountBalance(
                    tx,
                    data.accountId,
                    data.outputCurrency,
                    -outputAmount
                );

                // Update output location balance (decrease since we're giving currency)
                await this.updateLocationBalance(
                    tx,
                    data.outputLocation,
                    data.outputCurrency,
                    -outputAmount,
                    data.outputBankId
                );
            }

            // Create the exchange record with display rate
            const exchange = await tx.currencyExchange.create({
                data: {
                    fromAmount: data.inputAmount,
                    fromCurrency: data.inputCurrency,
                    toAmount: outputAmount,
                    toCurrency: data.outputCurrency,
                    exchangeRate: data.exchangeRate,
                    displayRate: data.displayRate || data.exchangeRate,
                    description: data.description,
                    fromEntry: { connect: { id: exchangeDebit.id } },
                    toEntry: { connect: { id: exchangeCredit.id } }
                }
            });

            // Get final balances for response
            const accountBalances = await this.getAccountBalances(tx, data.accountId);
            const locationBalances = {
                ...(data.inputLocation && {
                    [data.inputLocation]: await this.getLocationBalanceInternal(tx, data.inputLocation, data.inputCurrency, data.inputBankId)
                }),
                ...(data.outputLocation && {
                    [data.outputLocation]: await this.getLocationBalanceInternal(tx, data.outputLocation, data.outputCurrency, data.outputBankId)
                })
            };

            return {
                success: true,
                exchange,
                transactions: {
                    ...(inputDeposit && { inputDeposit }),
                    exchangeDebit,
                    exchangeCredit,
                    ...(outputWithdrawal && { outputWithdrawal })
                },
                balances: {
                    account: accountBalances,
                    locations: locationBalances
                }
            };
        });
    }


    private async validateExchangeInput(
        tx: Prisma.TransactionClient,
        data: CreateStructuredExchangeData
    ): Promise<ExchangeValidationResult> {
        const errors: string[] = [];

        // Validate input source and location
        if (data.inputSource === 'NEW_DEPOSIT') {
            if (!data.inputLocation) {
                errors.push('Input location is required for new deposits');
                return { isValid: false, errors };
            }

            // Validate bank account for PKR transactions
            if (data.inputLocation === 'BANK_ACCOUNT') {
                if (data.inputCurrency !== 'PKR') {
                    errors.push('Only PKR is supported for bank transactions');
                    return { isValid: false, errors };
                }
                if (!data.inputBankId) {
                    errors.push('Bank account ID is required for bank transactions');
                    return { isValid: false, errors };
                }
            }
        } else {
            // Using account balance
            const currency = await tx.currency.findFirst({ where: { code: data.inputCurrency } })

            if (!currency) {
                errors.push('Currency not found');
                return { isValid: false, errors };
            }

            // Check account balance only if allowLoan is false
            if (!data.allowLoan) {
                const accountBalance = await tx.accountBalance.findUnique({
                    where: {
                        accountId_currencyId: {
                            accountId: data.accountId,
                            currencyId: currency.id
                        }
                    }
                });

                if (!accountBalance || accountBalance.balance < data.inputAmount) {
                    errors.push(`Insufficient ${data.inputCurrency} balance in account`);
                    return { isValid: false, errors };
                }
            }
        }

        // Validate output location if not keeping in account
        if (!data.keepInAccount) {
            if (!data.outputLocation) {
                errors.push('Output location is required when not keeping in account');
                return { isValid: false, errors };
            }

            if (data.outputLocation === 'BANK_ACCOUNT') {
                if (data.outputCurrency !== 'PKR') {
                    errors.push('Only PKR is supported for bank transactions');
                    return { isValid: false, errors };
                }
                if (!data.outputBankId) {
                    errors.push('Bank account ID is required for bank transactions');
                    return { isValid: false, errors };
                }
            }
        }

        return { isValid: true, errors: [] };
    }

    private async getLocationBalanceInternal(
        tx: Prisma.TransactionClient,
        location: TransactionLocation,
        currency: string,
        bankId?: string
    ): Promise<number> {
        switch (location) {
            case 'SMALL_COUNTER':
                const counter = await tx.smallCounter.findFirst();
                return this.getBalanceForCurrency(counter, currency);

            case 'CASH_VAULT':
                const vault = await tx.cashVault.findFirst();
                return this.getBalanceForCurrency(vault, currency);

            case 'BANK_ACCOUNT':
                if (!bankId) throw new Error('Bank ID required for bank balance check');
                const bank = await tx.bankAccount.findUnique({ where: { id: bankId } });
                return bank?.balance ?? 0;

            case 'ACCOUNT':
                throw new Error('Use getAccountBalance for account balances');

            default:
                throw new Error(`Invalid location: ${location}`);
        }
    }

    /**
     * Get the balance for a specific location and currency
     * This is a public API for the frontend to use
     */
    async getLocationBalance(
        location: TransactionLocation,
        currency: string,
        bankId?: string
    ): Promise<{
        location: TransactionLocation;
        currency: string;
        balance: number;
        bankName?: string;
        bankAccountNumber?: string;
    }> {
        // Validate currency for bank accounts
        if (location === 'BANK_ACCOUNT') {
            if (currency !== 'PKR') {
                throw new Error('Bank accounts only support PKR currency');
            }
            if (!bankId) {
                throw new Error('Bank ID is required for bank account balances');
            }
        }

        let balance = 0;
        let bankDetails: { bankName: string; bankAccountNumber: string } | undefined = undefined;

        switch (location) {
            case 'SMALL_COUNTER':
                const counter = await prisma.smallCounter.findFirst();
                balance = this.getBalanceForCurrency(counter, currency);
                break;

            case 'CASH_VAULT':
                const vault = await prisma.cashVault.findFirst();
                balance = this.getBalanceForCurrency(vault, currency);
                break;

            case 'BANK_ACCOUNT':
                if (!bankId) throw new Error('Bank ID required for bank balance check');
                const bank = await prisma.bankAccount.findUnique({
                    where: { id: bankId }
                });
                if (!bank) throw new Error('Bank account not found');

                balance = bank.balance;
                bankDetails = {
                    bankName: bank.bankName,
                    bankAccountNumber: bank.accountNumber
                };
                break;

            default:
                throw new Error(`Invalid location: ${location}`);
        }

        return {
            location,
            currency,
            balance,
            ...(bankDetails ? bankDetails : {})
        };
    }

    private getBalanceForCurrency(
        location: SmallCounter | CashVault | null,
        currency: string
    ): number {
        if (!location) return 0;
        switch (currency) {
            case 'PKR': return location.pkrBalance;
            case 'USD': return location.usdBalance;
            case 'AED': return location.aedBalance;
            case 'AFN': return location.afnBalance;
            default: throw new Error(`Unsupported currency: ${currency}`);
        }
    }

    private async updateLocationBalance(
        tx: Prisma.TransactionClient,
        location: TransactionLocation,
        currency: string,
        amount: number,
        bankId?: string
    ): Promise<void> {
        const updateData = this.getUpdateDataForCurrency(currency, amount);

        switch (location) {
            case 'SMALL_COUNTER':
                await tx.smallCounter.updateMany({
                    data: updateData
                });
                break;

            case 'CASH_VAULT':
                await tx.cashVault.updateMany({
                    data: updateData
                });
                break;

            case 'BANK_ACCOUNT':
                if (!bankId) throw new Error('Bank ID required for balance update');
                await tx.bankAccount.update({
                    where: { id: bankId },
                    data: { balance: { increment: amount } }
                });
                break;
        }
    }

    private getUpdateDataForCurrency(currency: string, amount: number) {
        switch (currency) {
            case 'PKR': return { pkrBalance: { increment: amount } };
            case 'USD': return { usdBalance: { increment: amount } };
            case 'AED': return { aedBalance: { increment: amount } };
            case 'AFN': return { afnBalance: { increment: amount } };
            default: throw new Error(`Unsupported currency: ${currency}`);
        }
    }

    private async getAccountBalances(
        tx: Prisma.TransactionClient,
        accountId: string
    ): Promise<Record<string, number>> {
        const balances = await tx.accountBalance.findMany({
            where: { accountId },
            include: { currency: true }
        });

        return balances.reduce((acc, balance) => {
            acc[balance.currency.code] = balance.balance;
            return acc;
        }, {} as Record<string, number>);
    }

    private async updateAccountBalance(
        tx: Prisma.TransactionClient,
        accountId: string,
        currency: string,
        amount: number
    ): Promise<void> {

        const fetchedCurrency = await tx.currency.findFirst({
            where: {
                code: currency
            }
        })

        if (!fetchedCurrency) throw new Error('Currency not found in the database')

        await tx.accountBalance.update({
            where: {
                accountId_currencyId: {
                    accountId,
                    currencyId: fetchedCurrency.id
                }
            },
            data: {
                balance: {
                    increment: amount
                }
            }
        })
    }

    async getExchangeById(id: string) {
        return await prisma.currencyExchange.findUnique({
            where: { id },
            include: {
                fromEntry: true,
                toEntry: true
            }
        });
    }

    async getExchangeHistory({
        page,
        limit,
        startDate,
        endDate,
        fromCurrency,
        toCurrency,
        location,
        sortOrder = 'asc' // Default to oldest first (ascending)
    }: GetExchangeHistoryParams) {
        const where: Prisma.CurrencyExchangeWhereInput = {
            ...(startDate && endDate && {
                date: {
                    gte: startDate,
                    lte: endDate
                }
            }),
            ...(fromCurrency && { fromCurrency }),
            ...(toCurrency && { toCurrency }),
            ...(location && {
                OR: [
                    { fromEntry: { sourceType: location } },
                    { toEntry: { destinationType: location } }
                ]
            })
        };

        const [exchanges, total] = await Promise.all([
            prisma.currencyExchange.findMany({
                where,
                include: {
                    fromEntry: true,
                    toEntry: true
                },
                orderBy: { date: sortOrder }, // Use the sortOrder parameter
                skip: (page - 1) * limit,
                take: limit
            }),
            prisma.currencyExchange.count({ where })
        ]);

        return {
            exchanges,
            total,
            page,
            totalPages: Math.ceil(total / limit)
        };
    }

    async getExchangeStatistics() {
        const exchanges = await prisma.currencyExchange.findMany({
            include: {
                fromEntry: true,
                toEntry: true
            }
        });

        const volumeMap = new Map<string, { inflow: number; outflow: number }>();

        exchanges.forEach(exchange => {
            // Handle fromCurrency
            const fromStats = volumeMap.get(exchange.fromCurrency) || { inflow: 0, outflow: 0 };
            fromStats.outflow += exchange.fromAmount;
            volumeMap.set(exchange.fromCurrency, fromStats);

            // Handle toCurrency
            const toStats = volumeMap.get(exchange.toCurrency) || { inflow: 0, outflow: 0 };
            toStats.inflow += exchange.toAmount;
            volumeMap.set(exchange.toCurrency, toStats);
        });

        return {
            totalVolume: Array.from(volumeMap.entries()).map(([currency, stats]) => ({
                currency,
                ...stats
            })),
            totalTransactions: exchanges.length
        };
    }

    async getDailyExchangeSummary(date: Date) {
        const startOfDay = new Date(date);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(date);
        endOfDay.setHours(23, 59, 59, 999);

        const exchanges = await prisma.currencyExchange.findMany({
            where: {
                date: {
                    gte: startOfDay,
                    lte: endOfDay
                }
            },
            include: {
                fromEntry: true,
                toEntry: true
            }
        });

        // Process exchanges to get currency-wise summary
        const currencyMap = new Map<string, { inflow: number; outflow: number; transactions: number }>();

        exchanges.forEach(exchange => {
            // Process fromCurrency
            const fromStats = currencyMap.get(exchange.fromCurrency) ||
                { inflow: 0, outflow: 0, transactions: 0 };
            fromStats.outflow += exchange.fromAmount;
            fromStats.transactions += 1;
            currencyMap.set(exchange.fromCurrency, fromStats);

            // Process toCurrency
            const toStats = currencyMap.get(exchange.toCurrency) ||
                { inflow: 0, outflow: 0, transactions: 0 };
            toStats.inflow += exchange.toAmount;
            toStats.transactions += 1;
            currencyMap.set(exchange.toCurrency, toStats);
        });

        return {
            date: startOfDay,
            exchanges: Array.from(currencyMap.entries()).map(([currency, stats]) => ({
                currency,
                ...stats
            })),
            totalTransactions: exchanges.length
        };
    }

    async getMonthlyExchangeReport(month: Date) {
        const startOfMonth = new Date(month.getFullYear(), month.getMonth(), 1);
        const endOfMonth = new Date(month.getFullYear(), month.getMonth() + 1, 0, 23, 59, 59, 999);

        const [currentMonthExchanges, previousMonthExchanges] = await Promise.all([
            prisma.currencyExchange.findMany({
                where: {
                    date: {
                        gte: startOfMonth,
                        lte: endOfMonth
                    }
                }
            }),
            prisma.currencyExchange.findMany({
                where: {
                    date: {
                        gte: new Date(month.getFullYear(), month.getMonth() - 1, 1),
                        lte: new Date(month.getFullYear(), month.getMonth(), 0)
                    }
                }
            })
        ]);

        const currencyStats = new Map<string, {
            inflow: number;
            outflow: number;
            rates: number[];
            previousVolume: number;
        }>();

        // Process current month
        currentMonthExchanges.forEach(exchange => {
            const fromStats = currencyStats.get(exchange.fromCurrency) ||
                { inflow: 0, outflow: 0, rates: [], previousVolume: 0 };
            fromStats.outflow += exchange.fromAmount;
            fromStats.rates.push(exchange.exchangeRate);
            currencyStats.set(exchange.fromCurrency, fromStats);

            const toStats = currencyStats.get(exchange.toCurrency) ||
                { inflow: 0, outflow: 0, rates: [], previousVolume: 0 };
            toStats.inflow += exchange.toAmount;
            currencyStats.set(exchange.toCurrency, toStats);
        });

        // Process previous month for trends
        previousMonthExchanges.forEach(exchange => {
            const fromStats = currencyStats.get(exchange.fromCurrency) ||
                { inflow: 0, outflow: 0, rates: [], previousVolume: 0 };
            fromStats.previousVolume += exchange.fromAmount;
            currencyStats.set(exchange.fromCurrency, fromStats);

            const toStats = currencyStats.get(exchange.toCurrency) ||
                { inflow: 0, outflow: 0, rates: [], previousVolume: 0 };
            toStats.previousVolume += exchange.toAmount;
            currencyStats.set(exchange.toCurrency, toStats);
        });

        const totalVolume = currentMonthExchanges.reduce((sum, exchange) =>
            sum + exchange.fromAmount, 0);

        return {
            month: startOfMonth,
            totalVolume,
            byCurrency: Array.from(currencyStats.entries()).map(([currency, stats]) => ({
                currency,
                inflow: stats.inflow,
                outflow: stats.outflow,
                averageRate: stats.rates.length > 0
                    ? stats.rates.reduce((a, b) => a + b) / stats.rates.length
                    : 0
            })),
            trends: Array.from(currencyStats.entries()).map(([currency, stats]) => {
                const currentVolume = stats.inflow + stats.outflow;
                const percentageChange = stats.previousVolume === 0
                    ? 100
                    : ((currentVolume - stats.previousVolume) / stats.previousVolume) * 100;

                return {
                    currency,
                    trend: percentageChange > 0 ? 'UP' : percentageChange < 0 ? 'DOWN' : 'STABLE',
                    percentageChange
                };
            })
        };
    }

    async getMostExchangedCurrencies(limit: number = 5) {
        const exchanges = await prisma.currencyExchange.findMany();
        const volumeMap = new Map<string, number>();

        exchanges.forEach(exchange => {
            volumeMap.set(
                exchange.fromCurrency,
                (volumeMap.get(exchange.fromCurrency) || 0) + exchange.fromAmount
            );
            volumeMap.set(
                exchange.toCurrency,
                (volumeMap.get(exchange.toCurrency) || 0) + exchange.toAmount
            );
        });

        return Array.from(volumeMap.entries())
            .map(([currency, volume]) => ({ currency, volume }))
            .sort((a, b) => b.volume - a.volume)
            .slice(0, limit);
    }

    // async exchangeBetweenLocations(data: CreateExchangeData) {
    //     // Validate locations
    //     if (data.sourceLocation === data.destinationLocation) {
    //         throw new Error('Source and destination locations must be different');
    //     }

    //     // Create exchange with location tracking
    //     return this.createExchange(data);
    // }

    async getLocationExchangeHistory(location: TransactionLocation) {
        const exchanges = await prisma.currencyExchange.findMany({
            where: {
                OR: [
                    { fromEntry: { sourceType: location } },
                    { toEntry: { destinationType: location } }
                ]
            },
            include: {
                fromEntry: true,
                toEntry: true
            },
            orderBy: { date: 'desc' }
        });

        const currencyStats = new Map<string, { inflow: number; outflow: number }>();
        let totalVolume = 0;

        exchanges.forEach(exchange => {
            if (exchange.fromEntry?.sourceType === location) {
                const stats = currencyStats.get(exchange.fromCurrency) || { inflow: 0, outflow: 0 };
                stats.outflow += exchange.fromAmount;
                currencyStats.set(exchange.fromCurrency, stats);
                totalVolume += exchange.fromAmount;
            }
            if (exchange.toEntry?.destinationType === location) {
                const stats = currencyStats.get(exchange.toCurrency) || { inflow: 0, outflow: 0 };
                stats.inflow += exchange.toAmount;
                currencyStats.set(exchange.toCurrency, stats);
                totalVolume += exchange.toAmount;
            }
        });

        return {
            location,
            exchanges: Array.from(currencyStats.entries()).map(([currency, stats]) => ({
                currency,
                ...stats
            })),
            totalVolume
        };
    }
}

export const exchangeService = new ExchangeService();