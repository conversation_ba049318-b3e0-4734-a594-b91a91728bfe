import { useEffect, useState } from 'react'
import { Typography, Space, Button, Alert, Skeleton, Row, Col, Select, Table } from 'antd'
import { useApi } from '@/renderer/hooks'
import { dashboardApi } from '@/renderer/services'
import { LocationTransferSummary, TimeRange } from '@/common/types'
import { FaMapMarkerAlt, FaSync } from 'react-icons/fa'
import { formatCurrency } from '@/renderer/utils'
import { useTheme } from '@/renderer/contexts'

const { Title } = Typography

const TIME_RANGES = [
  { value: '1D', label: 'Today' },
  { value: '7D', label: 'Last 7 Days' },
  { value: '14D', label: 'Last 14 Days' },
  { value: '30D', label: 'Last 30 Days' }
]

const LocationTransfers = () => {
  const { isDarkMode } = useTheme()
  const [timeRange, setTimeRange] = useState<TimeRange>('1D')

  const {
    data,
    isLoading,
    error,
    request: fetchLocationTransfers
  } = useApi<LocationTransferSummary[], [TimeRange]>(dashboardApi.getLocationTransfers)

  useEffect(() => {
    fetchLocationTransfers(timeRange)
  }, [timeRange])

  const expandedColumns = (record: LocationTransferSummary) => [
    {
      title: 'Currency',
      dataIndex: 'currency',
      key: 'currency'
    },
    {
      title: 'Inflow',
      dataIndex: 'inflow',
      key: 'inflow',
      align: 'right' as const,
      render: (_: any, row: any) => formatCurrency(record.inflow[row.currency], row.currency)
    },
    {
      title: 'Outflow',
      dataIndex: 'outflow',
      key: 'outflow',
      align: 'right' as const,
      render: (_: any, row: any) => formatCurrency(record.outflow[row.currency], row.currency)
    },
    {
      title: 'Net',
      dataIndex: 'net',
      key: 'net',
      align: 'right' as const,
      render: (_: any, row: any) => formatCurrency(record.net[row.currency], row.currency)
    }
  ]

  const columns = [
    {
      title: 'Location',
      dataIndex: 'location',
      key: 'location',
      render: (text: string) => text.replace(/_/g, ' ')
    }
  ]

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <FaMapMarkerAlt className="text-2xl text-green-600" />
            <Title level={5} className="!mb-0">
              Location Transfers
            </Title>
          </Space>
          <Space>
            <Select value={timeRange} disabled style={{ width: 120 }} />
            <Button icon={<FaSync />} loading />
          </Space>
        </Space>
        <Skeleton active paragraph={{ rows: 4 }} />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <FaMapMarkerAlt className="text-2xl text-green-600" />
            <Title level={5} className="!mb-0">
              Location Transfers
            </Title>
          </Space>
          <Space>
            <Select value={timeRange} disabled style={{ width: 120 }} />
            <Button icon={<FaSync />} onClick={() => fetchLocationTransfers(timeRange)} />
          </Space>
        </Space>
        <Alert
          message="Error"
          description="Failed to load location transfers"
          type="error"
          showIcon
        />
      </div>
    )
  }

  if (!data) return null

  // Get all unique currencies from the data
  const currencies = Array.from(
    new Set(
      data.flatMap((item) =>
        Object.keys(item.inflow).concat(Object.keys(item.outflow), Object.keys(item.net))
      )
    )
  )

  // Create expanded data for each location
  const getExpandedData = (record: LocationTransferSummary) =>
    currencies.map((currency) => ({
      currency,
      key: `${record.location}_${currency}`
    }))

  return (
    <div className="flex flex-col gap-4">
      <Space className="flex justify-between">
        <Space>
          <FaMapMarkerAlt className="text-2xl text-green-600" />
          <Title level={5} className="!mb-0">
            Location Transfers
          </Title>
        </Space>
        <Space>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            options={TIME_RANGES}
            style={{ width: 120 }}
          />
          <Button icon={<FaSync />} onClick={() => fetchLocationTransfers(timeRange)} />
        </Space>
      </Space>

      <div className={`rounded-lg p-4 ${isDarkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Table
              dataSource={data}
              columns={columns}
              expandable={{
                expandedRowRender: (record) => (
                  <Table
                    columns={expandedColumns(record)}
                    dataSource={getExpandedData(record)}
                    pagination={false}
                    size="small"
                  />
                )
              }}
              pagination={false}
              size="small"
              className={isDarkMode ? 'dark-table' : ''}
            />
          </Col>
        </Row>
      </div>
    </div>
  )
}

export default LocationTransfers
