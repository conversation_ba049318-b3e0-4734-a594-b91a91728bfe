import { createContext, useContext, useState, useEffect } from 'react'
import { partnerApi } from '../services'
import { useApi } from '../hooks'

export interface PartnerOption {
  value: string
  label: string
}

interface PartnerContextType {
  partners: PartnerOption[] | []
  loading: boolean
  error: any
  errorMessage: string | null
  refreshPartners: () => Promise<void>
}

const PartnerContext = createContext<PartnerContextType>({
  partners: [],
  loading: false,
  error: null,
  errorMessage: null,
  refreshPartners: async () => {}
})

export const usePartnerContext = () => useContext(PartnerContext)

export const PartnerProvider = ({ children }: { children: React.ReactNode }) => {
  const [partners, setPartners] = useState<PartnerOption[]>([])

  const {
    data: partnersData,
    isLoading: loading,
    request: getPartners,
    error,
    errorMessage
  } = useApi<PartnerOption[], []>(partnerApi.getPartnersForSelect)

  const fetchPartners = async () => {
    if (!partners.length) {
      await getPartners()
    }
  }

  const refreshPartners = async () => {
    setPartners([])
    await getPartners()
  }

  useEffect(() => {
    fetchPartners()
  }, [])

  useEffect(() => {
    // console.log('partnersData', partnersData)
    if (partnersData) {
      setPartners(partnersData)
    }
  }, [partnersData])

  return (
    <PartnerContext.Provider value={{ partners, loading, error, errorMessage, refreshPartners }}>
      {children}
    </PartnerContext.Provider>
  )
}
