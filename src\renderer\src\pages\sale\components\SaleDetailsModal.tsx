import { Modal, Descriptions, Tag, Card, Button, Spin, App, Tooltip } from 'antd'
import { useState, useEffect } from 'react'
import { useApi } from '@/renderer/hooks'
import { saleApi } from '@/renderer/services'
import { formatDate, formatCurrency } from '@/renderer/utils'
import { FiPrinter } from 'react-icons/fi'
import type { GetSaleByIdResponse } from '@/common'
import { useTheme } from '@/renderer/contexts'

interface SaleDetailsModalProps {
  saleId: string | null
  onClose: () => void
}

export const SaleDetailsModal = ({ saleId, onClose }: SaleDetailsModalProps) => {
  const { isDarkMode } = useTheme()

  const {
    data: sale,
    isLoading,
    request: fetchSale
  } = useApi<GetSaleByIdResponse, [string]>(saleApi.getSaleById)

  const [isPrinting, setIsPrinting] = useState(false)

  const { message } = App.useApp()

  useEffect(() => {
    if (saleId) {
      fetchSale(saleId)
    }
  }, [saleId])

  const getItemDetails = (sale: GetSaleByIdResponse) => {
    const item = sale.car || sale.carPart || sale.electronic || sale.scrap

    if (sale.itemType === 'CAR' && sale.car) {
      return {
        id: sale.car.id,
        name: sale.car.name,
        details: (
          <div className="flex flex-wrap gap-2">
            <Tooltip title="Chassis Number">
              <Tag color="purple">{sale.car.chassisNumber}</Tag>
            </Tooltip>
            <Tooltip title="Model Number">
              <Tag color="blue">{sale.car.modelNumber}</Tag>
            </Tooltip>
            <Tooltip title="Color">
              <Tag color="green">{sale.car.color}</Tag>
            </Tooltip>
          </div>
        ),
        showQuantity: false
      }
    } else if (sale.itemType === 'CARPART' && sale.carPart) {
      return {
        id: sale.carPart.id,
        name: sale.carPart.name,
        details: null,
        showQuantity: true
      }
    } else if (sale.itemType === 'ELECTRONIC' && sale.electronic) {
      return {
        id: sale.electronic.id,
        name: sale.electronic.name,
        details: null,
        showQuantity: true
      }
    } else if (sale.itemType === 'SCRAP' && sale.scrap) {
      return {
        id: sale.scrap.id,
        name: sale.scrap.description,
        details: null,
        showQuantity: true
      }
    }

    return {
      id: item?.id || '',
      name: 'Unknown',
      details: null,
      showQuantity: sale.itemType !== 'CAR'
    }
  }

  const handlePrint = async () => {
    if (!sale) return

    setIsPrinting(true)
    try {
      //   await saleApi.generateInvoice(sale.id)

      console.log('will print and make a function to print')
      message.success('Invoice generated successfully')
    } catch (error: any) {
      message.error('Failed to generate invoice')
    } finally {
      setIsPrinting(false)
    }
  }

  return (
    <Modal open={!!saleId} onCancel={onClose} footer={null} width={800} title="Sale Details">
      {isLoading ? (
        <div className="flex justify-center p-8">
          <Spin size="large" />
        </div>
      ) : sale ? (
        <div className="space-y-6">
          <div className="flex items-start justify-between">
            <div>
              <h2 className="mb-2 text-xl font-semibold">
                {sale.account ? 'Account Sale' : 'Walk-in Sale'}
              </h2>
              <p className="text-gray-500">Sale ID: {sale.id}</p>
            </div>
            {/* <Button icon={<FiPrinter />} onClick={handlePrint} loading={isPrinting}>
              Print Invoice
            </Button> */}
          </div>

          <Card
            className={`shadow-large bg-[length:200%_200%] bg-[10%_10%] transition-[background-position] duration-500 ease-in-out hover:bg-[90%_90%] ${
              isDarkMode
                ? 'bg-gradient-to-br from-blue-950 via-slate-950 to-blue-900'
                : 'bg-gradient-to-br from-sky-200 via-white to-blue-200'
            }`}
          >
            <Descriptions column={2} size="small">
              <Descriptions.Item label="Date">{formatDate(sale.date.toString())}</Descriptions.Item>
              <Descriptions.Item label="Status">
                <Tag color={sale.isDeleted ? 'red' : 'green'}>
                  {sale.isDeleted ? 'Deleted' : 'Active'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Amount">
                <span className="text-lg font-medium text-green-600">
                  {formatCurrency(sale.totalAmount, 'PKR')}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="Item Type">
                <Tag color="blue">{sale.itemType}</Tag>
              </Descriptions.Item>
              {!sale.account && sale.paymentLocation && (
                <Descriptions.Item label="Payment Location">
                  <Tag color="purple">{sale.paymentLocation}</Tag>
                </Descriptions.Item>
              )}
            </Descriptions>
          </Card>

          <Card title="Item Details" size="small">
            {sale && (
              <Descriptions column={1}>
                <Descriptions.Item label="Item ID">{getItemDetails(sale).id}</Descriptions.Item>
                <Descriptions.Item label="Name">{getItemDetails(sale).name}</Descriptions.Item>
                {getItemDetails(sale).details && (
                  <Descriptions.Item label="Details">
                    {getItemDetails(sale).details}
                  </Descriptions.Item>
                )}
                {getItemDetails(sale).showQuantity && (
                  <Descriptions.Item label="Quantity">
                    <Tag color="orange">{sale.quantity}</Tag>
                  </Descriptions.Item>
                )}
              </Descriptions>
            )}
          </Card>

          {sale.account ? (
            <Card title="Account Information" size="small">
              <Descriptions column={1}>
                <Descriptions.Item label="Account Name">{sale.account.name}</Descriptions.Item>
                <Descriptions.Item label="Contact">{sale.account.phoneNumber}</Descriptions.Item>
                <Descriptions.Item label="Address">{sale.account.address}</Descriptions.Item>
              </Descriptions>
            </Card>
          ) : (
            <>
              <Card title="Payment Information" size="small">
                <Descriptions column={1}>
                  {sale.payments?.map((payment, index) => (
                    <Descriptions.Item key={payment.id} label={`Payment ${index + 1}`}>
                      <div className="flex w-full items-center justify-between">
                        <span>{formatCurrency(payment.amount, 'PKR')}</span>
                        <span className="text-gray-500">{formatDate(payment.date.toString())}</span>
                      </div>
                    </Descriptions.Item>
                  ))}
                </Descriptions>
              </Card>

              {/* Display bank information for bank payments */}
              {sale.paymentLocation === 'BANK_ACCOUNT' && sale.bankAccount && (
                <Card title="Bank Information" size="small" className="">
                  <Descriptions column={1}>
                    <Descriptions.Item label="Bank Name">
                      {sale.bankAccount.bankName}
                    </Descriptions.Item>
                    <Descriptions.Item label="Account Number">
                      {sale.bankAccount.accountNumber}
                    </Descriptions.Item>
                    {sale.bankAccount.branchCode && (
                      <Descriptions.Item label="Branch Code">
                        {sale.bankAccount.branchCode}
                      </Descriptions.Item>
                    )}
                  </Descriptions>
                </Card>
              )}
            </>
          )}

          {sale.isDeleted && (
            <Card title="Deletion Details" size="small" className="bg-red-50">
              <Descriptions column={1}>
                <Descriptions.Item label="Deleted At">
                  {formatDate(sale.deletedAt!.toString())}
                </Descriptions.Item>
                <Descriptions.Item label="Reason">{sale.deleteReason}</Descriptions.Item>
                <Descriptions.Item label="Deleted By">{'sale.createdBy?.name'}</Descriptions.Item>
              </Descriptions>
            </Card>
          )}
        </div>
      ) : null}
    </Modal>
  )
}
