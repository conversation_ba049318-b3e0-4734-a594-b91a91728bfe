import { Form, Input, Modal, Select } from 'antd'
import type { CreatePropertyData } from '@/common/types'

interface PropertyFormProps {
  open: boolean
  loading?: boolean
  onCancel: () => void
  onSubmit: (data: CreatePropertyData) => Promise<void>
}

export const PropertyForm = ({ open, loading, onCancel, onSubmit }: PropertyFormProps) => {
  const [form] = Form.useForm()

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      await onSubmit(values)
      form.resetFields()
    } catch (error) {
      // Form validation error, no need to handle
    }
  }

  return (
    <Modal
      title="Add Property"
      open={open}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={600}
    >
      <Form form={form} layout="vertical" className="mt-4">
        <Form.Item
          name="name"
          label="Property Name"
          rules={[{ required: true, message: 'Please enter property name' }]}
        >
          <Input placeholder="Enter property name" />
        </Form.Item>

        <Form.Item
          name="type"
          label="Property Type"
          rules={[{ required: true, message: 'Please select property type' }]}
        >
          <Select
            placeholder="Select property type"
            options={[
              { value: 'SHOP', label: 'SHOP' },
              { value: 'BUILDING', label: 'BUILDING' },
              { value: 'LAND', label: 'LAND' },
              { value: 'YARD', label: 'YARD' },
              { value: 'OTHER', label: 'OTHER' },
            ]}
            
          />
        </Form.Item>

        <Form.Item
          name="address"
          label="Address"
          rules={[{ required: true, message: 'Please enter address' }]}
        >
          <Input.TextArea
            placeholder="Enter property address"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>

        <Form.Item name="description" label="Description">
          <Input.TextArea
            placeholder="Enter property description"
            autoSize={{ minRows: 3, maxRows: 6 }}
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}
