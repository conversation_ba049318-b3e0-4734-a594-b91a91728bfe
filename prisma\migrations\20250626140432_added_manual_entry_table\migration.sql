-- CreateEnum
CREATE TYPE "ManualEntryTarget" AS ENUM ('CASH_VAULT');

-- AlterEnum
ALTER TYPE "TransactionCategory" ADD VALUE 'MANUAL_ENTRY';

-- CreateTable
CREATE TABLE "ManualEntry" (
    "id" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "description" TEXT,
    "transactionDate" TIMESTAMP(3) NOT NULL,
    "entryType" "TransactionType" NOT NULL,
    "accountId" TEXT,
    "bankAccountId" TEXT,
    "targetType" "ManualEntryTarget",
    "currencyId" TEXT NOT NULL,
    "ledgerEntryId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdById" TEXT NOT NULL,
    "isDeleted" BOOLEAN NOT NULL DEFAULT false,
    "deletedAt" TIMESTAMP(3),
    "deletedById" TEXT,
    "deletionReason" TEXT,

    CONSTRAINT "ManualEntry_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ManualEntry_ledgerEntryId_key" ON "ManualEntry"("ledgerEntryId");

-- CreateIndex
CREATE INDEX "ManualEntry_accountId_idx" ON "ManualEntry"("accountId");

-- CreateIndex
CREATE INDEX "ManualEntry_bankAccountId_idx" ON "ManualEntry"("bankAccountId");

-- CreateIndex
CREATE INDEX "ManualEntry_targetType_idx" ON "ManualEntry"("targetType");

-- CreateIndex
CREATE INDEX "ManualEntry_currencyId_idx" ON "ManualEntry"("currencyId");

-- CreateIndex
CREATE INDEX "ManualEntry_transactionDate_idx" ON "ManualEntry"("transactionDate");

-- CreateIndex
CREATE INDEX "ManualEntry_entryType_idx" ON "ManualEntry"("entryType");

-- CreateIndex
CREATE INDEX "ManualEntry_isDeleted_idx" ON "ManualEntry"("isDeleted");

-- AddForeignKey
ALTER TABLE "ManualEntry" ADD CONSTRAINT "ManualEntry_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ManualEntry" ADD CONSTRAINT "ManualEntry_bankAccountId_fkey" FOREIGN KEY ("bankAccountId") REFERENCES "BankAccount"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ManualEntry" ADD CONSTRAINT "ManualEntry_currencyId_fkey" FOREIGN KEY ("currencyId") REFERENCES "Currency"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ManualEntry" ADD CONSTRAINT "ManualEntry_ledgerEntryId_fkey" FOREIGN KEY ("ledgerEntryId") REFERENCES "LedgerEntry"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ManualEntry" ADD CONSTRAINT "ManualEntry_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ManualEntry" ADD CONSTRAINT "ManualEntry_deletedById_fkey" FOREIGN KEY ("deletedById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
