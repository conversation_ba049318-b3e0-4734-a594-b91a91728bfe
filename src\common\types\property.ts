import { Account, PropertyType } from '@prisma/client'
import { RentedProperty } from './rentedProperty'

export interface CreatePropertyData {
    name: string
    address: string
    type: 'SHOP' | 'BUILDING' | 'LAND' | 'YARD' | 'OTHER'
    description?: string
}

export interface UpdatePropertyData {
    name?: string
    address?: string
    type?: PropertyType
    description?: string
}



// export interface RentedProperty {
//     id: string
//     propertyId: string
//     property: Property
//     tenantId: string
//     tenant: any // We'll define the Tenant type later if needed
//     startDate: Date
//     endDate?: Date
//     monthlyRent: number
//     createdAt: Date
//     updatedAt: Date
// }

export interface GetPropertiesParams {
    search?: string
    type?: PropertyType
    isAvailable?: boolean
    page?: number
    limit?: number
}

export interface Property {
    id: string
    name: string
    type: 'SHOP' | 'BUILDING' | 'LAND' | 'YARD' | 'OTHER'
    address: string
    description?: string
    createdAt: Date
    updatedAt: Date
    currentTenantId?: string
    currentTenant?: Account
    isRented: boolean
    currentRent: number
}

export interface GetPropertiesResponse {
    properties: Property[]
    pagination: {
        total: number
        page: number
        limit: number
    }
}
