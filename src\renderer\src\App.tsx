import { useEffect } from 'react'
import { Channels } from '@/common'
import Router from './router/Router'
import './main.scss'
import { Bank<PERSON>rovider, PartnerProvider, AccountProvider } from './contexts'
import { App as AntdApp } from 'antd'
import { useDispatch } from 'react-redux'
import { userActions } from './redux'
import { licenseApi } from './services'
import { Provider } from 'react-redux'
import { store } from './redux'
import { ErrorBoundary } from './components'

function AppContent(): JSX.Element {
  const ipcRenderer = window.electron.ipcRenderer
  const dispatch = useDispatch()

  // useEffect(() => {
  //   ipcRenderer.send(Channels.PING)

  //   const checkLicense = async () => {
  //     const response = await licenseApi.getLicense()
  //     // console.log('check license in App.tsx response', response)

  //     // console.log('App.tsx license response', response)
  //     // console.log(response.data.data)

  //     // If there's a data error, it means no license exists - this is expected
  //     if (response.data?.error) {
  //       dispatch(userActions.initLicense(null))
  //       return
  //     }

  //     // If there's a server/unexpected error
  //     if (response.error.error) {
  //       console.error('Failed to check license:', response.error.message)
  //       return
  //     }

  //     // If we have a valid license
  //     if (response.data?.data) {
  //       dispatch(userActions.initLicense(response.data.data))
  //     }
  //   }
  //   checkLicense()
  // }, [])

  return (
    <AntdApp message={{ maxCount: 2 }}>
      <ErrorBoundary>
        <AccountProvider>
          <PartnerProvider>
            <BankProvider>
              <Router />
            </BankProvider>
          </PartnerProvider>
        </AccountProvider>
      </ErrorBoundary>
    </AntdApp>
  )
}

function App(): JSX.Element {
  return (
    <Provider store={store}>
      <AppContent />
    </Provider>
  )
}

export default App
