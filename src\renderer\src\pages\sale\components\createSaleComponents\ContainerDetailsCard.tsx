import { Card, Descriptions } from 'antd'
import dayjs from 'dayjs'
import { formatCurrency } from '@/renderer/utils'

interface ContainerDetailsCardProps {
  container: {
    id: string
    containerNumber: string
    containerCost: number
    driverExpense: number
    fieldRent: number
    openedAt: Date
    routeExpense: number
    taxes: number
  }
}

export const ContainerDetailsCard = ({ container }: ContainerDetailsCardProps) => {
  if (!container) return null

  // console.log('container', container)

  return (
    <Card title="Container Details" className="mb-4 shadow-lg">
      <Descriptions column={2}>
        <Descriptions.Item label="Container Number">{container.containerNumber}</Descriptions.Item>
        <Descriptions.Item label="Container Cost">
          {formatCurrency(container.containerCost, 'PKR')}
        </Descriptions.Item>
        <Descriptions.Item label="Driver Expense">
          {formatCurrency(container.driverExpense, 'PKR')}
        </Descriptions.Item>
        <Descriptions.Item label="Field Rent">
          {formatCurrency(container.fieldRent, 'PKR')}
        </Descriptions.Item>
        <Descriptions.Item label="Route Expense">
          {formatCurrency(container.routeExpense, 'PKR')}
        </Descriptions.Item>
        <Descriptions.Item label="Taxes">
          {formatCurrency(container.taxes, 'PKR')}
        </Descriptions.Item>
        <Descriptions.Item label="Opened At">
          {dayjs(container.openedAt).format('DD/MM/YYYY')}
        </Descriptions.Item>
      </Descriptions>
    </Card>
  )
}
