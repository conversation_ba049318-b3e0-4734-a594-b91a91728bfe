import { IRequest } from '@/common/types';
import { currencyService } from '../services';

class CurrencyController {
    async getAllCurrencies() {
        return await currencyService.getAllCurrencies();
    }

    async getCurrencyTransactions(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { currencyCode } = req.params ?? {};
        const { startDate, endDate, page, pageSize } = req.params ?? {};

        console.log('currencyCode', currencyCode, 'startDate', startDate, 'endDate', endDate, 'page', page, 'pageSize', pageSize);

        if (!currencyCode) {
            throw new Error('Currency code is required');
        }

        return await currencyService.getCurrencyTransactions(
            currencyCode,
            startDate ? new Date(startDate) : undefined,
            endDate ? new Date(endDate) : undefined,
            Number(page) || 1,
            Number(pageSize) || 20
        );
    }

    async getCurrencySummary(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { currencyCode } = req.params ?? {};
        const { startDate, endDate } = req.params ?? {};

        console.log('currencyCode', currencyCode, 'startDate', startDate, 'endDate', endDate);

        if (!currencyCode) {
            throw new Error('Currency code is required');
        }

        return await currencyService.getCurrencySummary(
            currencyCode,
            startDate ? new Date(startDate) : undefined,
            endDate ? new Date(endDate) : undefined
        );
    }

    async getCurrencyBalances(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { currencyCode } = req.params ?? {};

        console.log('currencyCode', currencyCode);
        if (!currencyCode) {
            throw new Error('Currency code is required');
        }

        return await currencyService.getCurrencyBalances(currencyCode);
    }
}

export const currencyController = new CurrencyController();