import { Prisma, PaymentType, TransactionLocation, TransactionType } from '@prisma/client';
import { prisma } from '../db';
import { CreatePaymentData, DeletePaymentParams, GetPaymentsParams, CreateAccountTransferData, GetTransfersParams, DeleteTransferData } from '@/common/types';

class PaymentService {
    async createPayment(data: CreatePaymentData) {
        return await prisma.$transaction(async (tx) => {
            // Get currency ID
            const currency = await tx.currency.findUnique({
                where: { code: data.currencyCode }
            });

            if (!currency) {
                throw new Error(`Currency ${data.currencyCode} not found`);
            }

            // Create the payment record with direct currency relation
            const payment = await tx.payment.create({
                data: {
                    amount: data.amount,
                    date: data.date || new Date(),
                    paymentType: data.paymentType,
                    description: data.description,
                    account: { connect: { id: data.accountId } },
                    currency: { connect: { id: currency.id } }
                }
            });

            // Create ledger entry based on payment type
            if (data.paymentType === 'PAID') {
                // When paying, money leaves the source location and is given to the account
                await tx.ledgerEntry.create({
                    data: {
                        amount: data.amount,
                        date: data.date || new Date(),
                        type: 'DEBIT', // Money leaving source location
                        description: data.description || 'Payment made',
                        sourceType: data.sourceLocation,
                        destinationType: data.destinationType,
                        transactionType: 'PAYMENT',
                        currency: { connect: { id: currency.id } },
                        account: { connect: { id: data.accountId } },
                        payment: { connect: { id: payment.id } },
                        ...(data.sourceLocation === 'BANK_ACCOUNT' && {
                            bankAccount: { connect: { id: data.sourceBankId! } }
                        }),
                        createdBy: { connect: { id: data.userId } }
                    }
                });

                // Update source balance (decrease since money is leaving)
                if (data.sourceLocation === 'BANK_ACCOUNT') {
                    await tx.bankAccount.update({
                        where: { id: data.sourceBankId! },
                        data: { balance: { decrement: data.amount } }
                    });
                } else if (data.sourceLocation === 'SMALL_COUNTER' || data.sourceLocation === 'CASH_VAULT') {
                    await this.updateLocationBalance(tx, data.sourceLocation, -data.amount, data.currencyCode);
                }

                // Update account balance (decrease since they received physical money)
                await this.updateAccountBalance(tx, data.accountId, currency.id, -data.amount);

            } else {
                // When receiving, money enters the destination location and is taken from the account
                await tx.ledgerEntry.create({
                    data: {
                        amount: data.amount,
                        date: data.date || new Date(),
                        type: 'CREDIT', // Money entering destination location
                        description: data.description || 'Payment received',
                        sourceType: data.sourceLocation,
                        destinationType: data.destinationType,
                        transactionType: 'PAYMENT',
                        currency: { connect: { id: currency.id } },
                        account: { connect: { id: data.accountId } },
                        payment: { connect: { id: payment.id } },
                        ...(data.destinationType === 'BANK_ACCOUNT' && {
                            bankAccount: { connect: { id: data.destinationBankId! } }
                        }),
                        createdBy: { connect: { id: data.userId } }
                    }
                });

                // Update destination balance (increase since money is entering)
                if (data.destinationType === 'BANK_ACCOUNT') {
                    await tx.bankAccount.update({
                        where: { id: data.destinationBankId! },
                        data: { balance: { increment: data.amount } }
                    });
                } else if (data.destinationType === 'SMALL_COUNTER' || data.destinationType === 'CASH_VAULT') {
                    await this.updateLocationBalance(tx, data.destinationType, data.amount, data.currencyCode);
                }

                // Update account balance (increase since they gave physical money)
                await this.updateAccountBalance(tx, data.accountId, currency.id, data.amount);
            }

            return payment;
        });
    }

    private async updateLocationBalance(
        tx: Prisma.TransactionClient,
        location: TransactionLocation,
        amount: number,
        currencyCode: string
    ) {
        const currencyField = `${currencyCode.toLowerCase()}Balance`;

        if (location === 'SMALL_COUNTER') {
            await tx.smallCounter.update({
                where: { id: await this.getSmallCounterId() },
                data: { [currencyField]: { increment: amount } }
            });
        } else if (location === 'CASH_VAULT') {
            await tx.cashVault.update({
                where: { id: await this.getCashVaultId() },
                data: { [currencyField]: { increment: amount } }
            });
        }
    }

    private async updateAccountBalance(
        tx: Prisma.TransactionClient,
        accountId: string,
        currencyId: string,
        amount: number
    ) {
        await tx.accountBalance.upsert({
            where: {
                accountId_currencyId: {
                    accountId,
                    currencyId
                }
            },
            create: {
                accountId,
                currencyId,
                balance: amount
            },
            update: {
                balance: { increment: amount }
            }
        });
    }

    private async getSmallCounterId(): Promise<string> {
        const counter = await prisma.smallCounter.findFirst();
        if (!counter) throw new Error('Small counter not found');
        return counter.id;
    }

    private async getCashVaultId(): Promise<string> {
        const vault = await prisma.cashVault.findFirst();
        if (!vault) throw new Error('Cash vault not found');
        return vault.id;
    }

    async deletePayment({ id, reason, userId }: DeletePaymentParams) {
        return await prisma.$transaction(async (tx) => {
            // Get payment with its relations
            const payment = await tx.payment.findUnique({
                where: { id },
                include: {
                    account: true,
                    ledgerEntry: true
                }
            });

            if (!payment) {
                throw new Error('Payment not found');
            }

            if (payment.isDeleted) {
                throw new Error('Payment is already deleted');
            }

            const ledgerEntry = payment.ledgerEntry;
            if (!ledgerEntry) {
                throw new Error('Ledger entry not found for payment');
            }

            // Get currency
            const currency = await tx.currency.findFirst({
                where: { id: ledgerEntry.currencyId }
            });

            if (!currency) {
                throw new Error('Currency not found');
            }

            // Revert balances based on payment type
            if (payment.paymentType === 'PAID') {
                // Revert source balance (increase)
                if (ledgerEntry.sourceType === 'BANK_ACCOUNT' && ledgerEntry.bankAccountId) {
                    await tx.bankAccount.update({
                        where: { id: ledgerEntry.bankAccountId },
                        data: { balance: { increment: payment.amount } }
                    });
                } else {
                    await this.updateLocationBalance(
                        tx,
                        ledgerEntry.sourceType,
                        payment.amount,
                        currency.code
                    );
                }

                // Revert account balance (decrease)
                await this.updateAccountBalance(
                    tx,
                    payment.accountId!,
                    ledgerEntry.currencyId,
                    payment.amount
                );
            } else {
                // Revert destination balance (decrease)
                if (ledgerEntry.destinationType === 'BANK_ACCOUNT' && ledgerEntry.bankAccountId) {
                    await tx.bankAccount.update({
                        where: { id: ledgerEntry.bankAccountId },
                        data: { balance: { decrement: payment.amount } }
                    });
                } else {
                    await this.updateLocationBalance(
                        tx,
                        ledgerEntry.destinationType,
                        -payment.amount,
                        currency.code
                    );
                }

                // Revert account balance (increase)
                await this.updateAccountBalance(
                    tx,
                    payment.accountId!,
                    ledgerEntry.currencyId,
                    -payment.amount
                );
            }

            // Soft delete the payment and its ledger entry
            await Promise.all([
                tx.payment.update({
                    where: { id },
                    data: {
                        isDeleted: true,
                        deleteReason: reason,
                        deletedAt: new Date()
                    }
                }),
                tx.ledgerEntry.update({
                    where: { id: ledgerEntry.id },
                    data: {
                        isDeleted: true,
                        deleteReason: reason,
                        deletedAt: new Date(),
                        deletedById: userId
                    }
                })
            ]);

            return payment;
        });
    }

    async getPayments({
        page = 1,
        limit = 20,
        startDate,
        endDate,
        paymentType,
        includeDeleted = false,
        accountId,
        orderBy = 'desc'
    }: GetPaymentsParams) {
        // Build where clause
        const where: Prisma.PaymentWhereInput = {
            ...(!includeDeleted && { isDeleted: false }),
            ...(accountId && { accountId }),
            ...(paymentType && { paymentType }),
        };

        if (startDate && endDate) {
            // If same day, get all transfers for that day
            if (startDate.toDateString() === endDate.toDateString()) {
                const start = new Date(startDate);
                start.setHours(0, 0, 0, 0);
                const end = new Date(startDate);
                end.setHours(23, 59, 59, 999);
                where.date = {
                    gte: start,
                    lte: end
                };
            } else {
                // Different days - ensure we get full days
                const start = new Date(startDate);
                start.setHours(0, 0, 0, 0);
                const end = new Date(endDate);
                end.setHours(23, 59, 59, 999);
                where.date = {
                    gte: start,
                    lte: end
                };
            }
        }

        // Get payments with pagination
        const [payments, total] = await Promise.all([
            prisma.payment.findMany({
                where,
                include: {
                    account: true,
                    sale: true,
                    currency: true,
                    ledgerEntry: {
                        include: {
                            bankAccount: true
                        }
                    }
                },
                orderBy: { date: orderBy },
                skip: (page - 1) * limit,
                take: limit
            }),
            prisma.payment.count({ where })
        ]);

        return {
            payments,
            pagination: {
                total,
                pages: Math.ceil(total / limit),
                currentPage: page,
                pageSize: limit
            }
        };
    }

    async getPaymentSummaryByAccount(accountId: string, startDate?: Date, endDate?: Date) {
        const where: Prisma.PaymentWhereInput = {
            accountId,
            isDeleted: false,
            ...(startDate && endDate && {
                date: { gte: startDate, lte: endDate }
            })
        };

        const payments = await prisma.payment.findMany({
            where,
            include: {
                currency: true
            }
        });

        // Group by currency and payment type
        const summary = payments.reduce((acc: any, payment) => {
            const currency = payment.currency.code;
            if (!acc[currency]) {
                acc[currency] = {
                    paid: 0,
                    received: 0,
                    totalTransactions: 0
                };
            }

            if (payment.paymentType === 'PAID') {
                acc[currency].paid += payment.amount;
            } else {
                acc[currency].received += payment.amount;
            }
            acc[currency].totalTransactions++;

            return acc;
        }, {});

        return summary;
    }

    async getDailyPaymentReport(date: Date) {
        const startOfDay = new Date(date);
        startOfDay.setHours(0, 0, 0, 0);

        const endOfDay = new Date(date);
        endOfDay.setHours(23, 59, 59, 999);

        const payments = await prisma.payment.findMany({
            where: {
                date: {
                    gte: startOfDay,
                    lte: endOfDay
                },
                isDeleted: false
            },
            include: {
                currency: true,
                ledgerEntry: true
            }
        });

        // Group by location, currency, and payment type
        const report = payments.reduce((acc: any, payment) => {
            const currency = payment.currency.code;
            const location = payment.paymentType === 'PAID'
                ? payment.ledgerEntry?.sourceType
                : payment.ledgerEntry?.destinationType;

            if (!location) return acc;

            if (!acc[location]) {
                acc[location] = {};
            }
            if (!acc[location][currency]) {
                acc[location][currency] = {
                    paid: 0,
                    received: 0,
                    count: 0
                };
            }

            if (payment.paymentType === 'PAID') {
                acc[location][currency].paid += payment.amount;
            } else {
                acc[location][currency].received += payment.amount;
            }
            acc[location][currency].count++;

            return acc;
        }, {});

        // Calculate totals
        const totals = payments.reduce((acc: any, payment) => {
            const currency = payment.currency.code;
            if (!acc[currency]) {
                acc[currency] = {
                    totalPaid: 0,
                    totalReceived: 0,
                    totalTransactions: 0
                };
            }

            if (payment.paymentType === 'PAID') {
                acc[currency].totalPaid += payment.amount;
            } else {
                acc[currency].totalReceived += payment.amount;
            }
            acc[currency].totalTransactions++;

            return acc;
        }, {});

        return {
            date,
            locationWise: report,
            totals
        };
    }

    async getOutstandingPayments() {
        const sales = await prisma.sale.findMany({
            where: {
                isDeleted: false,
                account: { isNot: null } // Only account sales
            },
            include: {
                account: true,
                payments: {
                    where: {
                        isDeleted: false
                    }
                }
            }
        });

        const outstandingPayments = sales.map(sale => {
            const totalPaid = sale.payments.reduce((sum, payment) => sum + payment.amount, 0);
            const outstanding = sale.totalAmount - totalPaid;

            return {
                saleId: sale.id,
                accountId: sale.accountId,
                accountName: sale.account?.name,
                totalAmount: sale.totalAmount,
                amountPaid: totalPaid,
                outstandingAmount: outstanding,
                date: sale.date
            };
        }).filter(sale => sale.outstandingAmount > 0);

        return outstandingPayments;
    }

    async getPaymentStatistics(startDate: Date, endDate: Date) {
        const payments = await prisma.payment.findMany({
            where: {
                date: { gte: startDate, lte: endDate },
                isDeleted: false
            },
            include: {
                ledgerEntry: {
                    include: {
                        currency: true
                    }
                }
            }
        });

        // Currency-wise statistics
        const currencyStats = payments.reduce((acc: any, payment) => {
            const currency = payment.ledgerEntry?.currency.code || 'UNKNOWN';
            if (!acc[currency]) {
                acc[currency] = {
                    totalAmount: 0,
                    totalTransactions: 0,
                    paid: 0,
                    received: 0,
                    averageAmount: 0,
                    maxAmount: 0,
                    minAmount: Infinity
                };
            }

            acc[currency].totalAmount += payment.amount;
            acc[currency].totalTransactions++;
            if (payment.paymentType === 'PAID') {
                acc[currency].paid += payment.amount;
            } else {
                acc[currency].received += payment.amount;
            }
            acc[currency].maxAmount = Math.max(acc[currency].maxAmount, payment.amount);
            acc[currency].minAmount = Math.min(acc[currency].minAmount, payment.amount);
            acc[currency].averageAmount = acc[currency].totalAmount / acc[currency].totalTransactions;

            return acc;
        }, {});

        // Daily distribution
        const dailyDistribution = payments.reduce((acc: any, payment) => {
            const day = payment.date.toISOString().split('T')[0];
            if (!acc[day]) {
                acc[day] = {
                    totalAmount: 0,
                    count: 0
                };
            }
            acc[day].totalAmount += payment.amount;
            acc[day].count++;
            return acc;
        }, {});

        return {
            period: { startDate, endDate },
            currencyStats,
            dailyDistribution,
            totalTransactions: payments.length,
            uniqueAccounts: new Set(payments.map(p => p.accountId)).size
        };
    }

    async transferBetweenLocations(data: {
        fromLocation: TransactionLocation;
        toLocation: TransactionLocation;
        amount: number;
        currencyCode: string;
        fromBankId?: string;
        toBankId?: string;
        description?: string;
        userId: string;
        date?: Date;
    }) {
        return await prisma.$transaction(async (tx) => {

            // Get currency
            const currency = await tx.currency.findUnique({
                where: { code: data.currencyCode }
            });

            if (!currency) {
                throw new Error(`Currency ${data.currencyCode} not found`);
            }

            // Create debit entry (from location)
            const debitEntry = await tx.ledgerEntry.create({
                data: {
                    amount: data.amount,
                    date: data.date || new Date(),
                    type: 'DEBIT',
                    description: data.description || 'Transfer between locations',
                    sourceType: data.fromLocation,
                    destinationType: 'EXTERNAL',
                    transactionType: 'TRANSFER',
                    currency: { connect: { id: currency.id } },
                    ...(data.fromLocation === 'BANK_ACCOUNT' && {
                        bankAccount: { connect: { id: data.fromBankId! } }
                    }),
                    createdBy: { connect: { id: data.userId } }
                }
            });

            // Create credit entry (to location)
            const creditEntry = await tx.ledgerEntry.create({
                data: {
                    amount: data.amount,
                    date: data.date || new Date(),
                    type: 'CREDIT',
                    description: data.description || 'Transfer between locations',
                    sourceType: 'EXTERNAL',
                    destinationType: data.toLocation,
                    transactionType: 'TRANSFER',
                    currency: { connect: { id: currency.id } },
                    ...(data.toLocation === 'BANK_ACCOUNT' && {
                        bankAccount: { connect: { id: data.toBankId! } }
                    }),
                    createdBy: { connect: { id: data.userId } }
                }
            });

            // Update source balance
            if (data.fromLocation === 'BANK_ACCOUNT') {
                await tx.bankAccount.update({
                    where: { id: data.fromBankId! },
                    data: { balance: { decrement: data.amount } }
                });
            } else {
                await this.updateLocationBalance(tx, data.fromLocation, -data.amount, data.currencyCode);
            }

            // Update destination balance
            if (data.toLocation === 'BANK_ACCOUNT') {
                await tx.bankAccount.update({
                    where: { id: data.toBankId! },
                    data: { balance: { increment: data.amount } }
                });
            } else {
                await this.updateLocationBalance(tx, data.toLocation, data.amount, data.currencyCode);
            }

            return {
                debitEntry,
                creditEntry
            };
        });
    }

    async transferBetweenAccounts(data: CreateAccountTransferData) {
        return await prisma.$transaction(async (tx) => {
            // Get currency
            const currency = await tx.currency.findUnique({
                where: { code: data.currencyCode }
            });

            if (!currency) {
                throw new Error(`Currency ${data.currencyCode} not found`);
            }

            // Get source account
            const sourceAccount = await tx.account.findUnique({
                where: { id: data.sourceAccountId }
            });

            if (!sourceAccount) {
                throw new Error('Source account not found');
            }

            // Get destination account
            const destinationAccount = await tx.account.findUnique({
                where: { id: data.destinationAccountId }
            });

            if (!destinationAccount) {
                throw new Error('Destination account not found');
            }

            // Create debit entry (from source account)
            const debitEntry = await tx.ledgerEntry.create({
                data: {
                    amount: data.amount,
                    date: data.date || new Date(),
                    type: 'DEBIT',
                    description: data.description || 'Transfer between accounts',
                    sourceType: 'ACCOUNT',
                    destinationType: 'ACCOUNT',
                    transactionType: 'TRANSFER',
                    currency: { connect: { id: currency.id } },
                    account: { connect: { id: data.sourceAccountId } },
                    createdBy: { connect: { id: data.userId } }
                }
            });

            // Create credit entry (to destination account)
            const creditEntry = await tx.ledgerEntry.create({
                data: {
                    amount: data.amount,
                    date: data.date || new Date(),
                    type: 'CREDIT',
                    description: data.description || 'Transfer between accounts',
                    sourceType: 'ACCOUNT',
                    destinationType: 'ACCOUNT',
                    transactionType: 'TRANSFER',
                    currency: { connect: { id: currency.id } },
                    account: { connect: { id: data.destinationAccountId } },
                    createdBy: { connect: { id: data.userId } }
                }
            });

            await tx.accountBalance.update({
                where: {
                    accountId_currencyId: {
                        accountId: data.sourceAccountId,
                        currencyId: currency.id
                    }
                },
                data: {
                    balance: { decrement: data.amount }
                }
            })

            await tx.accountBalance.update({
                where: {
                    accountId_currencyId: {
                        accountId: data.destinationAccountId,
                        currencyId: currency.id
                    }
                },
                data: {
                    balance: { increment: data.amount }
                }
            })


            return {
                debitEntryId: debitEntry.id,
                creditEntryId: creditEntry.id
            };
        });
    }

    async getTransfers(params: GetTransfersParams) {
        // Get currency ID first
        let currency;

        if (params.currencyCode) {
            currency = await prisma.currency.findUnique({
                where: { code: params.currencyCode }
            });

            if (!currency && params.currencyCode) {
                throw new Error(`Currency ${params.currencyCode} not found`);
            }
        }

        // Handle date filtering
        let dateFilter = {};
        if (params.startDate && params.endDate) {
            // If same day, get all transfers for that day
            if (params.startDate.toDateString() === params.endDate.toDateString()) {
                const start = new Date(params.startDate);
                start.setHours(0, 0, 0, 0);
                const end = new Date(params.startDate);
                end.setHours(23, 59, 59, 999);
                dateFilter = {
                    date: {
                        gte: start,
                        lte: end
                    }
                };
            } else {
                // Different days - ensure we get full days
                const start = new Date(params.startDate);
                start.setHours(0, 0, 0, 0);
                const end = new Date(params.endDate);
                end.setHours(23, 59, 59, 999);
                dateFilter = {
                    date: {
                        gte: start,
                        lte: end
                    }
                };
            }
        }

        const where: Prisma.LedgerEntryWhereInput = {
            transactionType: 'TRANSFER',
            ...(!params.includeDeleted && {
                isDeleted: false
            }),
            ...dateFilter,
            ...(params.search && {
                description: {
                    contains: params.search,
                    mode: 'insensitive' as Prisma.QueryMode
                }
            }),
            ...(params.accountId && {
                accountId: params.accountId
            }),
            ...(params.currencyCode && {
                currencyId: currency!.id
            }),
        };

        const [transfers, total] = await Promise.all([
            prisma.ledgerEntry.findMany({
                where,
                include: {
                    currency: true,
                    account: true,
                    createdBy: {
                        select: {
                            name: true
                        }
                    },
                    deletedBy: {
                        select: {
                            name: true
                        }
                    }
                },
                orderBy: {
                    date: params.orderBy || 'desc'
                },
                skip: (params.page - 1) * params.limit,
                take: params.limit
            }),
            prisma.ledgerEntry.count({ where })
        ]);

        return {
            transfers,
            total,
            page: params.page,
            totalPages: Math.ceil(total / params.limit)
        };
    }

    async deleteTransfer(data: DeleteTransferData) {
        return await prisma.$transaction(async (tx) => {
            // Get the ledger entry
            const entry = await tx.ledgerEntry.findUnique({
                where: { id: data.ledgerEntryId },
                include: {
                    currency: true,
                    account: true,
                    bankAccount: true
                }
            });

            if (!entry) {
                throw new Error('Ledger entry not found');
            }

            if (entry.transactionType !== 'TRANSFER') {
                throw new Error('This entry is not a transfer');
            }

            // Handle account transfers
            if (entry.account) {
                const multiplier = entry.type === 'DEBIT' ? 1 : -1;
                await tx.accountBalance.update({
                    where: {
                        accountId_currencyId: {
                            accountId: entry.account.id,
                            currencyId: entry.currency.id
                        }
                    },
                    data: {
                        balance: { increment: entry.amount * multiplier }
                    }
                });
            }
            // Handle location transfers
            else {
                // If it's a DEBIT entry, revert source location
                if (entry.type === 'DEBIT') {
                    if (entry.sourceType === 'SMALL_COUNTER') {
                        await tx.smallCounter.updateMany({
                            data: this.getUpdateDataForCurrency(entry.currency.code, entry.amount)
                        });
                    } else if (entry.sourceType === 'CASH_VAULT') {
                        await tx.cashVault.updateMany({
                            data: this.getUpdateDataForCurrency(entry.currency.code, entry.amount)
                        });
                    } else if (entry.sourceType === 'BANK_ACCOUNT' && entry.bankAccountId) {
                        await tx.bankAccount.update({
                            where: { id: entry.bankAccountId },
                            data: { balance: { increment: entry.amount } }
                        });
                    }
                }
                // If it's a CREDIT entry, revert destination location
                else if (entry.type === 'CREDIT') {
                    if (entry.destinationType === 'SMALL_COUNTER') {
                        await tx.smallCounter.updateMany({
                            data: this.getUpdateDataForCurrency(entry.currency.code, -entry.amount)
                        });
                    } else if (entry.destinationType === 'CASH_VAULT') {
                        await tx.cashVault.updateMany({
                            data: this.getUpdateDataForCurrency(entry.currency.code, -entry.amount)
                        });
                    } else if (entry.destinationType === 'BANK_ACCOUNT' && entry.bankAccountId) {
                        await tx.bankAccount.update({
                            where: { id: entry.bankAccountId },
                            data: { balance: { decrement: entry.amount } }
                        });
                    }
                }
            }

            // Soft delete the entry
            await tx.ledgerEntry.update({
                where: { id: data.ledgerEntryId },
                data: {
                    isDeleted: true,
                    deleteReason: data.reason,
                    deletedAt: new Date(),
                    deletedById: data.userId
                }
            });

            return { success: true };
        });
    }

    private getUpdateDataForCurrency(currency: string, amount: number) {
        const supportedCurrencies = ['PKR', 'USD', 'AED', 'AFN'];
        if (!supportedCurrencies.includes(currency)) {
            throw new Error(`Unsupported currency: ${currency}`);
        }

        return {
            [currency.toLowerCase() + 'Balance']: {
                increment: amount
            }
        };
    }
}

export const paymentService = new PaymentService();
