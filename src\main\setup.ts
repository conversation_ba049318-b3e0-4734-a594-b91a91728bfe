import { prisma } from './db';
import { hash } from 'bcryptjs';

export async function initializeApp() {
    // Check if any users exist
    const userCount = await prisma.user.count();

    if (userCount === 0) {
        // Create default admin user
        const defaultPassword = 'admin123'; // This should be changed on first login
        const hashedPassword = await hash(defaultPassword, 10);

        await prisma.user.create({
            data: {
                username: 'admin',
                password: hashedPassword,
                role: 'ADMIN',
                name: 'System Administrator'
            }
        });

        console.log('Default admin user created');
        console.log('Username: admin');
        console.log('Password: admin123');
        console.log('Please change this password after first login');
    }
}