// import { useState } from 'react'
// import {
//   Card,
//   DatePicker,
//   Table,
//   Space,
//   Typography,
//   Button,
//   Input,
//   Tag,
//   Row,
//   Col,
//   Statistic
// } from 'antd'
// import { FaSearch, FaLink, FaBalanceScale } from 'react-icons/fa'
// import { ledgerApi } from '@/renderer/services'
// import { formatCurrency } from '@/renderer/utils'
// import dayjs from 'dayjs'

// const { Title } = Typography
// const { RangePicker } = DatePicker
// const { Search } = Input

// export const AuditTrail = () => {
//   const [loading, setLoading] = useState(false)
//   const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>()
//   const [transactionId, setTransactionId] = useState<string>()
//   const [auditData, setAuditData] = useState<any[]>([])
//   const [verificationData, setVerificationData] = useState<any>(null)
//   const [balanceVerification, setBalanceVerification] = useState<any>(null)

//   const handleDateRangeChange = async (dates: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
//     if (!dates) return
//     setDateRange(dates)
//     await loadAuditTrail()
//   }

//   const loadAuditTrail = async () => {
//     if (!dateRange) return
//     setLoading(true)
//     try {
//       const response = await ledgerApi.getAuditTrail({
//         startDate: dateRange[0].toDate(),
//         endDate: dateRange[1].toDate(),
//         transactionId
//       })
//       setAuditData(response)
//     } catch (error) {
//       console.error('Failed to load audit trail:', error)
//     } finally {
//       setLoading(false)
//     }
//   }

//   const verifyTransactionChain = async () => {
//     if (!transactionId) return
//     setLoading(true)
//     try {
//       const response = await ledgerApi.verifyTransactionChain(transactionId)
//       setVerificationData(response)
//     } catch (error) {
//       console.error('Failed to verify transaction chain:', error)
//     } finally {
//       setLoading(false)
//     }
//   }

//   const verifyBalances = async () => {
//     setLoading(true)
//     try {
//       const response = await ledgerApi.getBalanceVerification()
//       setBalanceVerification(response)
//     } catch (error) {
//       console.error('Failed to verify balances:', error)
//     } finally {
//       setLoading(false)
//     }
//   }

//   const columns = [
//     {
//       title: 'Date & Time',
//       dataIndex: 'createdAt',
//       key: 'createdAt',
//       render: (date: string) => dayjs(date).format('DD/MM/YYYY HH:mm:ss')
//     },
//     {
//       title: 'User',
//       dataIndex: 'createdBy',
//       key: 'createdBy',
//       render: (user: any) => user.name
//     },
//     {
//       title: 'Transaction ID',
//       dataIndex: 'transactionId',
//       key: 'transactionId',
//       render: (id: string) => <Tag color="blue">{id}</Tag>
//     },
//     {
//       title: 'Description',
//       dataIndex: 'description',
//       key: 'description'
//     },
//     {
//       title: 'Amount',
//       dataIndex: 'amount',
//       key: 'amount',
//       render: (amount: number, record: any) => formatCurrency(amount, record.currency)
//     },
//     {
//       title: 'Type',
//       dataIndex: 'type',
//       key: 'type',
//       render: (type: string) => <Tag color={type === 'CREDIT' ? 'green' : 'red'}>{type}</Tag>
//     }
//   ]

//   return (
//     <Card>
//       <Space direction="vertical" size="large" style={{ width: '100%' }}>
//         <Space wrap align="center" style={{ justifyContent: 'space-between', width: '100%' }}>
//           <Title level={4} style={{ margin: 0 }}>
//             Audit Trail & Verification
//           </Title>
//           <Space wrap>
//             <RangePicker
//               onChange={(dates) => handleDateRangeChange(dates as [dayjs.Dayjs, dayjs.Dayjs])}
//               style={{ width: 300 }}
//             />
//             <Search
//               placeholder="Transaction ID"
//               onSearch={(value) => {
//                 setTransactionId(value)
//                 loadAuditTrail()
//               }}
//               style={{ width: 200 }}
//               enterButton={<FaSearch />}
//             />
//           </Space>
//         </Space>

//         <Space wrap>
//           <Button
//             icon={<FaLink />}
//             onClick={verifyTransactionChain}
//             disabled={!transactionId}
//             loading={loading}
//           >
//             Verify Transaction Chain
//           </Button>
//           <Button icon={<FaBalanceScale />} onClick={verifyBalances} loading={loading}>
//             Verify All Balances
//           </Button>
//         </Space>

//         {verificationData && (
//           <Card size="small" title="Transaction Chain Verification">
//             <Space direction="vertical" style={{ width: '100%' }}>
//               <Tag color={verificationData.isValid ? 'green' : 'red'}>
//                 {verificationData.isValid ? 'Valid' : 'Invalid'}
//               </Tag>
//               {verificationData.errors?.map((error: string, index: number) => (
//                 <div key={index} style={{ color: '#f5222d' }}>
//                   {error}
//                 </div>
//               ))}
//             </Space>
//           </Card>
//         )}

//         {balanceVerification && (
//           <Row gutter={[16, 16]}>
//             {Object.entries(balanceVerification).map(([currency, data]: [string, any]) => (
//               <Col key={currency} xs={24} sm={12} md={8}>
//                 <Card size="small" title={`${currency} Balance Verification`}>
//                   <Space direction="vertical" style={{ width: '100%' }}>
//                     <Statistic
//                       title="Calculated Balance"
//                       value={data.calculatedBalance}
//                       precision={2}
//                       valueStyle={{ color: data.isValid ? '#52c41a' : '#f5222d' }}
//                     />
//                     <Statistic title="Actual Balance" value={data.actualBalance} precision={2} />
//                     <Tag color={data.isValid ? 'green' : 'red'}>
//                       {data.isValid ? 'Balanced' : 'Discrepancy Found'}
//                     </Tag>
//                     {data.discrepancy && (
//                       <div style={{ color: '#f5222d' }}>
//                         Discrepancy: {formatCurrency(data.discrepancy, currency)}
//                       </div>
//                     )}
//                   </Space>
//                 </Card>
//               </Col>
//             ))}
//           </Row>
//         )}

//         <Table
//           columns={columns}
//           dataSource={auditData}
//           loading={loading}
//           rowKey="id"
//           pagination={{
//             total: auditData.length,
//             pageSize: 10,
//             showSizeChanger: true,
//             showTotal: (total) => `Total ${total} entries`
//           }}
//         />
//       </Space>
//     </Card>
//   )
// }
