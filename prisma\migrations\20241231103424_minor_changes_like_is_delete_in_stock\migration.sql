/*
  Warnings:

  - You are about to drop the column `isPaid` on the `Sale` table. All the data in the column will be lost.

*/
-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "TransactionCategory" ADD VALUE 'CURRENCY_DEPOSIT';
ALTER TYPE "TransactionCategory" ADD VALUE 'CURRENCY_WITHDRAWAL';
ALTER TYPE "TransactionCategory" ADD VALUE 'OPENING_BALANCE';

-- AlterTable
ALTER TABLE "Payment" ADD COLUMN     "accountId" TEXT;

-- AlterTable
ALTER TABLE "Sale" DROP COLUMN "isPaid",
ADD COLUMN     "deleteReason" TEXT,
ADD COLUMN     "deletedAt" TIMESTAMP(3),
ADD COLUMN     "isDeleted" BOOLEAN NOT NULL DEFAULT false;

-- CreateIndex
CREATE INDEX "LedgerEntry_sourceType_isDeleted_idx" ON "LedgerEntry"("sourceType", "isDeleted");

-- CreateIndex
CREATE INDEX "LedgerEntry_destinationType_isDeleted_idx" ON "LedgerEntry"("destinationType", "isDeleted");

-- CreateIndex
CREATE INDEX "LedgerEntry_transactionType_isDeleted_idx" ON "LedgerEntry"("transactionType", "isDeleted");

-- AddForeignKey
ALTER TABLE "Payment" ADD CONSTRAINT "Payment_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account"("id") ON DELETE SET NULL ON UPDATE CASCADE;
