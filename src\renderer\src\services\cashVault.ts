import { http } from './http';
import { Channels } from '@/common/constants';
import { InitializeCashVaultData, CashVaultBalances, GetCashVaultStatementParams } from '@/common/types';

export const initializeCashVault = async (data: InitializeCashVaultData) => {
    return await http.post(Channels.INITIALIZE_CASH_VAULT, { body: data });
};

export const getBalances = async () => {
    return await http.get(Channels.GET_CASH_VAULT_BALANCES);
};

export const getBalanceByCurrency = async (currency: string) => {
    return await http.get(Channels.GET_CASH_VAULT_BALANCE_BY_CURRENCY, {
        params: { currency }
    });
};

export const adjustBalance = async (currency: string, adjustment: number, reason: string, userId: string) => {
    return await http.post(Channels.ADJUST_CASH_VAULT_BALANCE, {
        body: { currency, adjustment, reason, userId }
    });
};

export const getAllTransactions = async (page: number = 1, pageSize: number = 20, startDate?: Date, endDate?: Date, sortOrder: 'asc' | 'desc' = 'asc') => {
    return await http.get(Channels.GET_ALL_CASH_VAULT_TRANSACTIONS, {
        query: { page, pageSize, startDate, endDate, sortOrder }
    });
};

export const reconcileBalance = async (currency: string) => {
    return await http.get(Channels.RECONCILE_CASH_VAULT_BALANCE, {
        params: { currency }
    });
};

export const generateCashVaultStatement = async (
    currencyCode: string,
    params: GetCashVaultStatementParams
) => {
    const { startDate, endDate, ...rest } = params;

    return await http.get(Channels.GENERATE_CASH_VAULT_STATEMENT, {
        params: { currencyCode },
        query: {
            ...rest,
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString()
        }
    });
};
