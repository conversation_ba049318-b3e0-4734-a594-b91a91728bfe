export interface CreateBankAccountData {
    accountNumber: string;
    bankName: string;
    branchCode?: string;
    openingBalance?: number;
    userId: string;
}

export interface BankAccount {
    id: string;
    accountNumber: string;
    bankName: string;
    branchCode?: string;
    balance: number;
    isActive: boolean;
}

export interface BankTransaction {
    id: string;
    date: Date;
    amount: number;
    type: 'CREDIT' | 'DEBIT';
    description: string;
    createdBy: {
        name: string;
    };
}

export interface GetTransactionHistoryParams {
    page?: number;
    pageSize?: number;
    sortOrder?: 'asc' | 'desc'; // asc = oldest first, desc = newest first
    startDate?: Date;
    endDate?: Date;
}

export interface PaginatedTransactions {
    transactions: BankTransaction[];
    pagination: {
        total: number;
        pages: number;
        currentPage: number;
        pageSize: number;
    };
}

export interface BankStatementEntry {
    id: string;
    date: Date;
    description: string;
    credit: number | null;
    debit: number | null;
    runningBalance: number;
    createdBy: {
        name: string;
    };
}

export interface BankStatementSummary {
    totalCredits: number;
    totalDebits: number;
    net: number;
}

export interface GetBankStatementParams {
    startDate: Date;
    endDate: Date;
    page?: number;
    pageSize?: number;
}

export interface BankStatement {
    account: BankAccount;
    startDate: Date;
    endDate: Date;
    entries: BankStatementEntry[];
    openingBalance: number;
    closingBalance: number;
    summary: BankStatementSummary;
    pagination: {
        total: number;
        pages: number;
        currentPage: number;
        pageSize: number;
    };
}

export interface ReconciliationResult {
    isReconciled: boolean;
    currentBalance: number;
    calculatedBalance: number;
    difference: number;
}

export interface TransactionHistoryResponse {
    transactions: BankTransaction[];
    pagination: {
        total: number;
        pages: number;
        currentPage: number;
        pageSize: number;
    };
}