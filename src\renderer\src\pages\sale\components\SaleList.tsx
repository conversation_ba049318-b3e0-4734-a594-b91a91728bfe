import {
  Table,
  Button,
  Popconfirm,
  message,
  Tag,
  Card,
  Row,
  Col,
  DatePicker,
  Input,
  Tooltip,
  Select,
  Space,
  Typography
} from 'antd'
import { FiTrash2, FiEye } from 'react-icons/fi'
import { useState, useEffect } from 'react'
import { useApi } from '@/renderer/hooks'
import { saleApi } from '@/renderer/services'
import { SaleDetailsModal } from './SaleDetailsModal'
import { formatDate, formatCurrency } from '@/renderer/utils'
import dayjs from 'dayjs'
import { GetSalesParams, GetSalesResponse, SaleWithRelations } from '@/common'
import { title } from 'process'
import { useTheme } from '@/renderer/contexts'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'

const { RangePicker } = DatePicker

interface SaleListProps {
  searchQuery: string
  refreshTrigger: number
}

export const SaleList = ({ searchQuery, refreshTrigger }: SaleListProps) => {
  const [selectedSale, setSelectedSale] = useState<string | null>(null)
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [dateRange, setDateRange] = useState<[Date, Date] | null>(null)
  const [deleteReason, setDeleteReason] = useState('')
  const [isDeleting, setIsDeleting] = useState(false)
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc') // Default to oldest first
  const [tableSize, setTableSize] = useState<'small' | 'middle' | 'large'>('small')

  const { isDarkMode } = useTheme()

  const user = useSelector((state: IRootState) => state.user.data)

  const {
    data,
    isLoading,
    request: fetchSales
  } = useApi<GetSalesResponse, [GetSalesParams]>(saleApi.getSales)

  useEffect(() => {
    fetchSales({
      page,
      pageSize,
      sortOrder,
      ...(dateRange && {
        startDate: dateRange[0],
        endDate: dateRange[1]
      })
    })
  }, [page, pageSize, dateRange, refreshTrigger, sortOrder])

  const handleDelete = async (id: string) => {
    setIsDeleting(true)
    const response = await saleApi.deleteSale({
      id,
      reason: deleteReason,
      userId: user?.id || ''
    })
    setIsDeleting(false)

    console.log('delete sale response', response)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      setDeleteReason('')
      return
    }

    setDeleteReason('')

    message.success('Sale deleted successfully')
    fetchSales({
      page,
      pageSize,
      sortOrder,
      ...(dateRange && {
        startDate: dateRange[0],
        endDate: dateRange[1]
      })
    })
  }

  const getItemDetails = (sale: SaleWithRelations) => {
    const item = sale.car || sale.carPart || sale.electronic || sale.scrap
    return {
      id: item?.id || '',
      name:
        sale.car?.modelNumber ||
        sale.carPart?.name ||
        sale.electronic?.name ||
        sale.scrap?.description ||
        'Unknown'
    }
  }

  const getItemTypeTooltip = (record: SaleWithRelations) => {
    switch (record.itemType) {
      case 'CAR':
        if (record.car) {
          return (
            <div>
              <p>
                <strong>Name:</strong> {record.car.name}
              </p>
              <p>
                <strong>Chassis:</strong> {record.car.chassisNumber}
              </p>
              <p>
                <strong>Model:</strong> {record.car.modelNumber}
              </p>
              <p>
                <strong>Color:</strong> {record.car.color}
              </p>
            </div>
          )
        }
        break
      case 'CARPART':
        if (record.carPart) {
          return (
            <div>
              <p>
                <strong>Name:</strong> {record.carPart.name}
              </p>
              <p>
                <strong>Total Quantity:</strong> {record.carPart.quantity}
              </p>
              <p>
                <strong>Sold in this sale:</strong> {record.quantity}
              </p>
            </div>
          )
        }
        break
      case 'ELECTRONIC':
        if (record.electronic) {
          return (
            <div>
              <p>
                <strong>Name:</strong> {record.electronic.name}
              </p>
              <p>
                <strong>Total Quantity:</strong> {record.electronic.quantity}
              </p>
              <p>
                <strong>Sold in this sale:</strong> {record.quantity}
              </p>
            </div>
          )
        }
        break
      case 'SCRAP':
        if (record.scrap) {
          return (
            <div>
              <p>
                <strong>Description:</strong> {record.scrap.description}
              </p>
              <p>
                <strong>Total Quantity:</strong> {record.scrap.quantity}
              </p>
              <p>
                <strong>Sold in this sale:</strong> {record.quantity}
              </p>
            </div>
          )
        }
        break
    }
    return 'No details available'
  }

  const columns = [
    {
      title: 'Sr. No',
      key: 'serialNumber',
      width: 70,
      render: (_: any, __: any, index: number) => {
        // Calculate serial number based on current page and page size
        return (page - 1) * pageSize + index + 1
      }
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      render: (date: string) => formatDate(date)
    },
    {
      title: 'Item Type',
      dataIndex: 'itemType',
      key: 'itemType',
      width: 120,
      render: (_: string, record: SaleWithRelations) => (
        <Tooltip title={getItemTypeTooltip(record)} placement="right">
          <Tag color="blue">{record.itemType}</Tag>
        </Tooltip>
      )
    },
    {
      title: 'Item Details',
      key: 'item',
      render: (_: any, record: SaleWithRelations) => {
        const item = getItemDetails(record)
        return (
          <div>
            <div className="font-medium">{item.name}</div>
          </div>
        )
      }
    },
    {
      title: 'Customer',
      key: 'customer',
      render: (_: any, record: SaleWithRelations) => (
        <div>
          {record.account ? (
            <>
              <div className="font-medium">{record.account.name}</div>
              <div className="text-xs text-gray-500">Account Sale</div>
            </>
          ) : (
            <Tag color="green">Walk-in Sale</Tag>
          )}
        </div>
      )
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity: number) => quantity
    },
    {
      title: 'Amount',
      dataIndex: 'totalAmount',
      key: 'amount',
      width: 150,
      align: 'right' as const,
      render: (amount: number) => (
        <span className="font-medium text-green-600">{formatCurrency(amount, 'PKR')}</span>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 100,
      render: (_: any, record: SaleWithRelations) => (
        <div className="flex gap-2">
          <Button type="text" icon={<FiEye />} onClick={() => setSelectedSale(record.id)} />
          <Popconfirm
            title="Delete Sale"
            description={
              <div className="space-y-2">
                <p>Are you sure you want to delete this sale?</p>
                <Input.TextArea
                  placeholder="Enter reason for deletion"
                  value={deleteReason}
                  onChange={(e) => setDeleteReason(e.target.value)}
                  rows={3}
                />
              </div>
            }
            onConfirm={() => handleDelete(record.id)}
            okButtonProps={{ disabled: !deleteReason }}
          >
            <Button type="text" danger icon={<FiTrash2 />} loading={isDeleting} />
          </Popconfirm>
        </div>
      )
    }
  ]

  return (
    <div className="space-y-6">
      <Row gutter={16}>
        <Col span={24}>
          <Card className={`shadow-inner ${isDarkMode ? 'bg-black' : 'bg-slate-50'}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Sales</p>
                <p className="text-2xl font-semibold text-blue-600">
                  {data?.pagination.total || 0}
                </p>
              </div>
              <Space>
                <Select
                  value={sortOrder}
                  onChange={(value: 'asc' | 'desc') => setSortOrder(value)}
                  style={{ width: 150 }}
                >
                  <Select.Option value="asc">Oldest First</Select.Option>
                  <Select.Option value="desc">Newest First</Select.Option>
                </Select>
                <RangePicker
                  onChange={(dates) => {
                    if (dates) {
                      setDateRange([dates[0]!.toDate(), dates[1]!.toDate()])
                    } else {
                      setDateRange(null)
                    }
                  }}
                />
                <Space>
                  <Typography.Text>Table Size:</Typography.Text>
                  <Select
                    value={tableSize}
                    onChange={setTableSize}
                    options={[
                      { label: 'Small', value: 'small' },
                      { label: 'Middle', value: 'middle' },
                      { label: 'Large', value: 'large' }
                    ]}
                    style={{ width: 100 }}
                  />
                </Space>
              </Space>
            </div>
          </Card>
        </Col>
      </Row>

      <Table
        columns={columns}
        dataSource={data?.sales}
        loading={isLoading}
        rowKey="id"
        virtual
        sticky
        size={tableSize}
        pagination={{
          current: page,
          position: ['topRight'],
          pageSize,
          total: data?.pagination.total,
          onChange: (page, pageSize) => {
            setPage(page)
            setPageSize(pageSize)
          },
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '50', '100'],
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
        }}
        className="ant-table-compact"
      />

      <SaleDetailsModal saleId={selectedSale} onClose={() => setSelectedSale(null)} />
    </div>
  )
}
