import { useEffect, useState } from 'react'
import {
  Table,
  Card,
  DatePicker,
  Select,
  Button,
  Space,
  Tag,
  Tooltip,
  Modal,
  Input,
  App,
  Switch,
  Typography
} from 'antd'
import { DeleteOutlined, SortAscendingOutlined, SortDescendingOutlined } from '@ant-design/icons'
import type { GetPaymentsResponse, PaymentWithRelations } from '@/common/types'
import { paymentApi } from '@/renderer/services'
import { formatDate } from '@/renderer/utils'
import { useAccountContext } from '@/renderer/contexts'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'

const { RangePicker } = DatePicker

interface PaymentListProps {
  refreshTrigger: number
}

export const PaymentList = ({ refreshTrigger }: PaymentListProps) => {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<GetPaymentsResponse>()
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    paymentType: undefined as 'PAID' | 'RECEIVED' | undefined,
    startDate: undefined as Date | undefined,
    endDate: undefined as Date | undefined,
    accountId: undefined as string | undefined,
    includeDeleted: false,
    orderBy: 'desc' as 'asc' | 'desc'
  })
  const [deleteModalVisible, setDeleteModalVisible] = useState(false)
  const [selectedPayment, setSelectedPayment] = useState<PaymentWithRelations | null>(null)
  const [deleteReason, setDeleteReason] = useState('')
  const [tableSize, setTableSize] = useState<'small' | 'middle' | 'large'>('small')

  const { message } = App.useApp()
  const { accounts } = useAccountContext()

  const user = useSelector((state: IRootState) => state.user.data)

  const fetchPayments = async () => {
    setLoading(true)
    const response = await paymentApi.getPayments(filters)
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    setData(response.data.data)
  }

  console.log(data)

  useEffect(() => {
    fetchPayments()
  }, [filters, refreshTrigger])

  const handleDelete = async () => {
    if (!selectedPayment || !deleteReason) return

    const response = await paymentApi.deletePayment({
      id: selectedPayment.id,
      reason: deleteReason,
      userId: user?.id || ''
    })

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Payment deleted successfully')

    setDeleteModalVisible(false)
    setDeleteReason('')
    setSelectedPayment(null)
    fetchPayments()
  }

  const columns = [
    {
      title: 'S.No',
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      width: 60,
      render: (_: any, __: any, index: number) => {
        // Calculate serial number based on current page and page size
        return (filters.page - 1) * filters.limit + index + 1
      }
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => formatDate(date)
    },
    {
      title: 'Account',
      dataIndex: ['account', 'name'],
      key: 'account'
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number, record: PaymentWithRelations) => (
        <span className={record.paymentType === 'PAID' ? 'text-red-500' : 'text-green-500'}>
          {record.paymentType === 'PAID' ? '-' : '+'}
          {amount.toLocaleString()} {record.currency.code || 'PKR'}
        </span>
      )
    },
    {
      title: 'Type',
      dataIndex: 'paymentType',
      key: 'paymentType',
      render: (type: string) => <Tag color={type === 'PAID' ? 'volcano' : 'green'}>{type}</Tag>
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      render: (description: string) => description || '-'
    },
    {
      title: 'Status',
      key: 'status',
      render: (record: PaymentWithRelations) => (
        <Tag color={record.isDeleted ? 'red' : 'green'}>
          {record.isDeleted ? 'Deleted' : 'Active'}
        </Tag>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: PaymentWithRelations) => (
        <Space>
          <Tooltip title="Delete Payment">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => {
                setSelectedPayment(record)
                setDeleteModalVisible(true)
              }}
              disabled={record.isDeleted}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  const handleTableChange = (pagination: any) => {
    setFilters((prev) => ({
      ...prev,
      page: pagination.current,
      limit: pagination.pageSize
    }))
  }

  return (
    <Card className="mt-2">
      <Space className="mb-2 flex w-full flex-wrap items-center justify-between">
        <Space className="flex flex-wrap items-center gap-4" wrap>
          <RangePicker
            onChange={(dates) => {
              if (dates) {
                setFilters((prev) => ({
                  ...prev,
                  startDate: dates[0]?.toDate(),
                  endDate: dates[1]?.toDate()
                }))
              } else {
                setFilters((prev) => ({
                  ...prev,
                  startDate: undefined,
                  endDate: undefined
                }))
              }
            }}
            className="w-64"
          />
          <Select
            placeholder="Payment Type"
            allowClear
            className="w-40"
            onChange={(value) => setFilters((prev) => ({ ...prev, paymentType: value }))}
            options={[
              { label: 'Paid', value: 'PAID' },
              { label: 'Received', value: 'RECEIVED' }
            ]}
          />
          <Select
            placeholder="Select Account"
            allowClear
            className="w-64"
            onChange={(value) => setFilters((prev) => ({ ...prev, accountId: value }))}
            options={accounts}
            showSearch
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
          <Select
            value={filters.orderBy}
            className="w-40"
            onChange={(value) => setFilters((prev) => ({ ...prev, orderBy: value }))}
            options={[
              {
                label: (
                  <div className="flex items-center">
                    <SortDescendingOutlined className="mr-2" /> Newest First
                  </div>
                ),
                value: 'desc'
              },
              {
                label: (
                  <div className="flex items-center">
                    <SortAscendingOutlined className="mr-2" /> Oldest First
                  </div>
                ),
                value: 'asc'
              }
            ]}
          />
          <div className="flex items-center gap-2">
            <Switch
              checked={filters.includeDeleted}
              onChange={(checked) => setFilters((prev) => ({ ...prev, includeDeleted: checked }))}
            />
            <span>Show Deleted</span>
          </div>
        </Space>

        <Space>
          <Typography.Text>Table Size:</Typography.Text>
          <Select
            value={tableSize}
            onChange={setTableSize}
            options={[
              { label: 'Small', value: 'small' },
              { label: 'Middle', value: 'middle' },
              { label: 'Large', value: 'large' }
            ]}
            style={{ width: 100 }}
          />
        </Space>
      </Space>

      <Table
        columns={columns}
        dataSource={data?.payments}
        loading={loading}
        rowKey="id"
        onChange={handleTableChange}
        sticky
        virtual
        size={tableSize}
        pagination={{
          position: ['topRight'],
          showQuickJumper: true,
          showPrevNextJumpers: true,
          current: filters.page,
          pageSize: filters.limit,
          total: data?.pagination.total,
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '50', '100', '500', '1000'],
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
        }}
      />

      <Modal
        title="Delete Payment"
        open={deleteModalVisible}
        onOk={handleDelete}
        onCancel={() => {
          setDeleteModalVisible(false)
          setDeleteReason('')
          setSelectedPayment(null)
        }}
        okButtonProps={{ danger: true }}
      >
        <p>Are you sure you want to delete this payment?</p>
        <Input.TextArea
          placeholder="Enter reason for deletion"
          value={deleteReason}
          onChange={(e) => setDeleteReason(e.target.value)}
          className="mt-4"
          rows={3}
        />
      </Modal>
    </Card>
  )
}
