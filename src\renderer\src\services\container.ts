import { http } from "./http";
import { Channels } from "@/common/constants";
import { CreateContainerData, GetContainerByStockIdParams, GetContainersParams, GetContainerStockParams } from "@/common/types";

// interface CreateCarData {
//     chassisNumber: string;
//     modelNumber: string;
//     name: string;
//     color: string;
// }

// interface CreateCarPartData {
//     name: string;
//     quantity: number;
// }

// interface CreateElectronicData {
//     name: string;
//     quantity: number;
// }

// interface CreateScrapData {
//     description: string;
//     quantity: number;
// }

// interface CreateContainerParams {
//     containerNumber: string;
//     openedAt: Date;
//     driverExpense: number;
//     taxes: number;
//     containerCost: number;
//     fieldRent: number;
//     partnerId?: string;
//     cars?: CreateCarData[];
//     carParts?: CreateCarPartData[];
//     electronics?: CreateElectronicData[];
//     scraps?: CreateScrapData[];
//     createdById: string;
// }

// interface GetContainersParams {
//     page?: number;
//     limit?: number;
//     includeDeleted?: boolean;
//     partnerId?: string;
//     search?: string;
// }

// interface GetContainerStockParams {
//     page?: number;
//     limit?: number;
//     itemType?: 'CAR' | 'CAR_PART' | 'ELECTRONIC' | 'SCRAP';
// }

export const createContainer = async (params: CreateContainerData) => {
    return await http.post(Channels.CREATE_CONTAINER, { body: params });
};

export const deleteContainer = async (id: string, deleteReason: string, deletedById: string) => {
    return await http.delete(Channels.DELETE_CONTAINER, {
        params: { id },
        body: { deleteReason, deletedById }
    });
};

export const getContainers = async (params: GetContainersParams) => {
    return await http.get(Channels.GET_CONTAINERS, { query: params });
};

export const getContainerStock = async (containerId: string, params: GetContainerStockParams) => {
    return await http.get(Channels.GET_CONTAINER_STOCK, {
        params: { containerId },
        query: params
    });
};

export const getContainerById = async (id: string) => {
    return await http.get(Channels.GET_CONTAINER, { params: { id } });
};

export const getContainerSummary = async (id: string) => {
    return await http.get(Channels.GET_CONTAINER_SUMMARY, { params: { id } });
};

export const checkCarWithSameChassisNumberExists = async (chassisNumber: string) => {
    return await http.post(Channels.CHECK_CAR_WITH_SAME_CHASSIS_NUMBER_EXISTS, { body: { chassisNumber } });
};

export const getContainersForPDF = async (startDate?: Date, endDate?: Date, partnerId?: string) => {
    return await http.get(Channels.GET_CONTAINERS_FOR_PDF, { query: { startDate, endDate, partnerId } });
};
