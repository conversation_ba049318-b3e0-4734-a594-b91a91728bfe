import { useState } from 'react'
import { Layout, Input, But<PERSON>, Card, Tabs } from 'antd'
import { FiPlus } from 'react-icons/fi'
import { AccountList } from './components/AccountList'
import { AddAccount } from './components/AddAccount'
import { TransitionWrapper } from '@/renderer/components'

type AccountType = 'CUSTOMER' | 'TENANT' | 'BOTH'

const { Content } = Layout
const { Search } = Input
const { TabPane } = Tabs

const Accounts = () => {
  const [search, setSearch] = useState('')
  const [isAddMode, setIsAddMode] = useState(false)
  const [activeTab, setActiveTab] = useState<'CUSTOMER' | 'TENANT' | 'BOTH'>('CUSTOMER')
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  return (
    <Layout className="relative h-full">
      <TransitionWrapper isVisible={!isAddMode} direction="left">
        <Content className="p-6">
          <Card>
            <div className="mb-6 flex items-center justify-between">
              <Search
                placeholder="Search accounts..."
                allowClear
                onChange={(e) => setSearch(e.target.value)}
                className="max-w-md"
              />
              <Button
                type="primary"
                icon={<FiPlus />}
                onClick={() => setIsAddMode(true)}
                className="bg-blue-500 hover:!bg-blue-600"
              >
                Add Account
              </Button>
            </div>

            <Tabs activeKey={activeTab} onChange={(key) => setActiveTab(key as AccountType)}>
              <TabPane tab="Customers" key="CUSTOMER">
                <AccountList type="CUSTOMER" searchQuery={search} refreshTrigger={refreshTrigger} />
              </TabPane>
              <TabPane tab="Tenants" key="TENANT">
                <AccountList type="TENANT" searchQuery={search} refreshTrigger={refreshTrigger} />
              </TabPane>
            </Tabs>
          </Card>
        </Content>
      </TransitionWrapper>

      <TransitionWrapper isVisible={isAddMode} direction="right">
        <AddAccount onClose={() => setIsAddMode(false)} setRefreshTrigger={setRefreshTrigger} />
      </TransitionWrapper>
    </Layout>
  )
}

export default Accounts
