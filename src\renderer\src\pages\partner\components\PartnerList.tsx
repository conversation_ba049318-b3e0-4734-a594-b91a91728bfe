import { <PERSON>, But<PERSON>, Popconfirm, message, Tag } from 'antd'
import { FiTrash2, FiEye } from 'react-icons/fi'
import { useEffect, useState } from 'react'
import { useApi } from '@/renderer/hooks'
import { partnerApi } from '@/renderer/services'
import { PartnersDetailModal } from './'
import { formatDate } from '@/renderer/utils'
import { GetPartnersParams, GetPartnersResponse } from '@/common/types'

interface PartnerListProps {
  searchQuery: string
  refreshTrigger: number
}

const PartnerList = ({ searchQuery, refreshTrigger }: PartnerListProps) => {
  const [selectedPartner, setSelectedPartner] = useState<string | null>(null)
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  const {
    data,
    isLoading,
    request: fetchPartners
  } = useApi<GetPartnersResponse, [GetPartnersParams]>(partnerApi.getPartners)

  useEffect(() => {
    fetchPartners({ page, limit: pageSize, search: searchQuery })
  }, [page, pageSize, searchQuery, refreshTrigger])

  console.log(data)

  const { request: deletePartner } = useApi(partnerApi.deletePartner)

  const handleDelete = async (id: string) => {
    try {
      await deletePartner(id, 'User requested deletion')
      message.success('Partner deleted successfully')
      fetchPartners({ page, limit: pageSize, search: searchQuery })
    } catch (error: any) {
      message.error(error.message)
    }
  }

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Containers',
      dataIndex: '_count',
      key: 'containers',
      render: (count: { containers: number }) => <Tag color="blue">{count.containers}</Tag>
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => formatDate(date)
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record: any) => (
        <div className="flex gap-2">
          <Button type="text" icon={<FiEye />} onClick={() => setSelectedPartner(record.id)} />
          <Popconfirm
            title="Delete Partner"
            description="Are you sure you want to delete this partner?"
            onConfirm={() => handleDelete(record.id)}
          >
            <Button
              type="text"
              danger
              icon={<FiTrash2 />}
              disabled={record._count.containers > 0}
            />
          </Popconfirm>
        </div>
      )
    }
  ]

  return (
    <>
      <Table
        columns={columns}
        dataSource={data?.partners}
        loading={isLoading}
        rowKey="id"
        pagination={{
          current: page,
          pageSize,
          total: data?.total,
          position: ['topRight'],
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '50', '100'],
          showQuickJumper: true,
          onChange: (page, pageSize) => {
            setPage(page)
            setPageSize(pageSize)
          }
        }}
      />

      <PartnersDetailModal partnerId={selectedPartner} onClose={() => setSelectedPartner(null)} />
    </>
  )
}

export default PartnerList
