import { configureStore, combineReducers } from '@reduxjs/toolkit';
import storage from 'redux-persist/es/storage';
import {
  persistStore,
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from 'redux-persist';
import { userReducer } from './slices';

const rootReducer = combineReducers({
  user: userReducer,
});

const persistedReducer = persistReducer(
  {
    key: 'root',
    version: 1,
    storage,
  },
  rootReducer,
);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
});

export type IRootState = ReturnType<typeof store.getState>;
export const persistor = persistStore(store);

// Listen for app closing event
// TODO: uncomment before release

// Listen for app closing event
window.electron?.ipcRenderer.on('app-closing', () => {
  // Clear the persisted state
  persistor.purge();
  // Dispatch logout action
  store.dispatch({ type: 'user/logout' });
});

export * from "./slices";
