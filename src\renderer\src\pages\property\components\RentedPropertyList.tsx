import { useEffect, useState } from 'react'
import {
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Tooltip,
  App,
  Modal,
  InputNumber,
  Form,
  Dropdown,
  MenuProps
} from 'antd'
import { rentedPropertyApi } from '@/renderer/services'
import type { GetRentedPropertiesParams, RentedProperty } from '@/common/types'
import { formatCurrency, formatDate } from '@/renderer/utils'
import { useApi } from '@/renderer/hooks'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { FiTrash2, FiMoreVertical, FiEdit3, FiCalendar } from 'react-icons/fi'
import { FaMoneyBillWave, FaMoneyBillAlt } from 'react-icons/fa'
import { useBankContext } from '@/renderer/contexts'

interface RentedPropertyListProps {
  refreshTrigger: number
}

export const RentedPropertyList = ({ refreshTrigger }: RentedPropertyListProps) => {
  const [filters, setFilters] = useState<Partial<GetRentedPropertiesParams>>({
    page: 1,
    limit: 20,
    isActive: true
  })

  const [selectedRental, setSelectedRental] = useState<RentedProperty | null>(null)
  const [isTerminateModalOpen, setIsTerminateModalOpen] = useState(false)
  const [terminateReason, setTerminateReason] = useState('')
  const [isAdjustRentModalOpen, setIsAdjustRentModalOpen] = useState(false)
  const [newRentAmount, setNewRentAmount] = useState<number>()
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false)
  const [isAdjustmentModalOpen, setIsAdjustmentModalOpen] = useState(false)
  const [paymentForm] = Form.useForm()
  const [adjustmentForm] = Form.useForm()

  const { banks: bankAccounts } = useBankContext()

  const { message, modal } = App.useApp()
  const user = useSelector((state: IRootState) => state.user.data)

  const {
    data: response,
    isLoading,
    request: fetchRentedProperties
  } = useApi<any, [Partial<GetRentedPropertiesParams>]>(rentedPropertyApi.getRentedProperties)

  useEffect(() => {
    fetchRentedProperties(filters)
  }, [filters, refreshTrigger])

  const handleFiltersChange = (key: keyof GetRentedPropertiesParams, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value }))
  }

  const handleTerminateRental = async () => {
    if (!selectedRental || !terminateReason) return

    const response = await rentedPropertyApi.terminateRental(selectedRental.id, terminateReason)
    if (!response.error.error && !response.data.error) {
      message.success('Rental terminated successfully')
      setIsTerminateModalOpen(false)
      setTerminateReason('')
      setSelectedRental(null)
      fetchRentedProperties(filters)
    } else {
      message.error(response.error?.message || response.data.error?.message)
    }
  }

  const handleEndRental = async (rental: RentedProperty) => {
    modal.confirm({
      title: 'End Rental',
      content:
        'Are you sure you want to end this rental? This will mark the property as available.',
      onOk: async () => {
        const response = await rentedPropertyApi.endRental(rental.id)
        if (!response.error.error && !response.data.error) {
          message.success('Rental ended successfully')
          fetchRentedProperties(filters)
        } else {
          message.error(response.error?.message || response.data.error?.message)
        }
      }
    })
  }

  const handleAdjustRent = async () => {
    if (!selectedRental || !newRentAmount) return

    const response = await rentedPropertyApi.adjustRent(selectedRental.id, newRentAmount)
    console.log(response)
    if (!response.error.error && !response.data.error) {
      message.success('Rent adjusted successfully')
      setIsAdjustRentModalOpen(false)
      setNewRentAmount(undefined)
      setSelectedRental(null)
      fetchRentedProperties(filters)
    } else {
      message.error(response.error?.message || response.data.error?.message)
    }
  }

  const handleAdjustRentChange = (value: number | null) => {
    setNewRentAmount(value || undefined)
  }

  const handleRecordPayment = async (values: {
    amount: number
    destinationType: 'CASH_VAULT' | 'BANK_ACCOUNT'
    bankAccountId?: string
  }) => {
    if (!selectedRental || !user?.id) return

    const response = await rentedPropertyApi.recordRentPayment({
      rentedPropertyId: selectedRental.id,
      amount: values.amount,
      userId: user.id,
      destinationType: values.destinationType,
      bankAccountId: values.bankAccountId
    })

    if (!response.error.error && !response.data.error) {
      message.success('Payment recorded successfully')
      setIsPaymentModalOpen(false)
      paymentForm.resetFields()
      setSelectedRental(null)
      fetchRentedProperties(filters)
    } else {
      message.error(response.error?.message || response.data?.error?.message)
    }
  }

  const handleAdjustPayment = async (values: {
    amount: number
    type: 'CREDIT' | 'DEBIT'
    destinationType: 'CASH_VAULT' | 'BANK_ACCOUNT'
    bankAccountId?: string
    reason: string
  }) => {
    if (!selectedRental || !user?.id) return

    const response = await rentedPropertyApi.adjustRentPayment({
      rentedPropertyId: selectedRental.id,
      amount: values.amount,
      type: values.type,
      destinationType: values.destinationType,
      bankAccountId: values.bankAccountId,
      reason: values.reason,
      userId: user.id
    })

    console.log(response)

    if (!response.error.error && !response.data?.error) {
      message.success('Payment adjustment recorded successfully')
      setIsAdjustmentModalOpen(false)
      adjustmentForm.resetFields()
      setSelectedRental(null)
      fetchRentedProperties(filters)
    } else {
      message.error(response.error?.message || response.data?.error?.message)
    }
  }

  const getMenuItems = (record: RentedProperty): MenuProps['items'] => [
    {
      key: 'recordPayment',
      icon: <FaMoneyBillWave />,
      label: 'Record Payment',
      onClick: () => {
        setSelectedRental(record)
        setIsPaymentModalOpen(true)
      }
    },
    {
      key: 'adjustPayment',
      icon: <FaMoneyBillAlt />,
      label: 'Adjust Payment',
      onClick: () => {
        setSelectedRental(record)
        setIsAdjustmentModalOpen(true)
      }
    },
    {
      key: 'endRental',
      icon: <FiCalendar />,
      label: 'End Rental',
      onClick: () => handleEndRental(record)
    },
    {
      key: 'adjustRent',
      icon: <FiEdit3 />,
      label: 'Adjust Rent',
      onClick: () => {
        setSelectedRental(record)
        setNewRentAmount(record.monthlyRent)
        setIsAdjustRentModalOpen(true)
      }
    },
    {
      key: 'terminate',
      icon: <FiTrash2 />,
      label: 'Terminate',
      danger: true,
      onClick: () => {
        setSelectedRental(record)
        setIsTerminateModalOpen(true)
      }
    }
  ]

  const columns = [
    {
      title: 'Property',
      key: 'property',
      render: (record: RentedProperty) => (
        <div>
          <div className="font-medium">{record.property?.name}</div>
          <div className="text-sm text-gray-500">{record.property?.address}</div>
        </div>
      )
    },
    {
      title: 'Tenant',
      key: 'tenant',
      render: (record: RentedProperty) => (
        <Tooltip title={record.tenant?.phoneNumber}>{record.tenant?.name}</Tooltip>
      )
    },
    {
      title: 'Monthly Rent',
      dataIndex: 'monthlyRent',
      key: 'monthlyRent',
      render: (amount: number) => formatCurrency(amount, 'PKR')
    },
    {
      title: 'Start Date',
      dataIndex: 'startDate',
      key: 'startDate',
      render: (date: string) => formatDate(date)
    },
    {
      title: 'End Date',
      dataIndex: 'endDate',
      key: 'endDate',
      render: (date: string | null) => (date ? formatDate(date) : 'No End Date')
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 80,
      render: (record: RentedProperty) => (
        <Dropdown
          menu={{ items: getMenuItems(record) }}
          trigger={['click']}
          placement="bottomRight"
        >
          <Button type="text" icon={<FiMoreVertical />} />
        </Dropdown>
      )
    }
  ]

  return (
    <div className="flex flex-col gap-4">
      <div className="flex gap-4">
        <Input.Search
          placeholder="Search properties or tenants"
          allowClear
          className="max-w-xs"
          value={filters.search}
          onChange={(e) => handleFiltersChange('search', e.target.value)}
        />

        <Select
          placeholder="Status"
          allowClear
          className="min-w-[150px]"
          value={filters.isActive}
          onChange={(value) => handleFiltersChange('isActive', value)}
          options={[
            { value: true, label: 'Active' },
            { value: false, label: 'Inactive' }
          ]}
        />
      </div>

      <Table
        columns={columns}
        dataSource={response?.rentedProperties}
        rowKey="id"
        loading={isLoading}
        pagination={{
          current: filters.page,
          pageSize: filters.limit,
          total: response?.pagination.total,
          onChange: (page, pageSize) => {
            setFilters((prev) => ({ ...prev, page, limit: pageSize }))
          }
        }}
      />

      <Modal
        title="Terminate Rental"
        open={isTerminateModalOpen}
        onOk={handleTerminateRental}
        onCancel={() => {
          setIsTerminateModalOpen(false)
          setTerminateReason('')
          setSelectedRental(null)
        }}
      >
        <Input.TextArea
          placeholder="Enter reason for termination"
          value={terminateReason}
          onChange={(e) => setTerminateReason(e.target.value)}
          rows={4}
        />
      </Modal>

      <Modal
        title="Adjust Monthly Rent"
        open={isAdjustRentModalOpen}
        onOk={handleAdjustRent}
        onCancel={() => {
          setIsAdjustRentModalOpen(false)
          setNewRentAmount(undefined)
          setSelectedRental(null)
        }}
      >
        <InputNumber
          className="w-full"
          placeholder="Enter new monthly rent"
          value={newRentAmount}
          onChange={handleAdjustRentChange}
          formatter={(value) => `PKR ${value || ''}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={(value) => Number(value!.replace(/PKR\s?|(,*)/g, ''))}
        />
      </Modal>

      <Modal
        title={`Record Payment - ${selectedRental?.property?.name}`}
        open={isPaymentModalOpen}
        onCancel={() => {
          setIsPaymentModalOpen(false)
          paymentForm.resetFields()
          setSelectedRental(null)
        }}
        footer={null}
      >
        <Form form={paymentForm} onFinish={handleRecordPayment} layout="vertical" className="mt-4">
          <Form.Item
            name="amount"
            label="Payment Amount"
            rules={[{ required: true, message: 'Please enter the payment amount' }]}
          >
            <InputNumber
              className="w-full"
              placeholder="Enter payment amount"
              formatter={(value) => `PKR ${value || ''}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={(value) => (value ? Number(value.replace(/PKR\s?|(,*)/g, '')) : 0)}
              min={Number(1)}
            />
          </Form.Item>

          <Form.Item
            name="destinationType"
            label="Receive Payment In"
            rules={[{ required: true, message: 'Please select where the payment is received' }]}
          >
            <Select
              options={[
                // { label: 'Small Counter', value: 'SMALL_COUNTER' },
                { label: 'Cash Vault', value: 'CASH_VAULT' },
                { label: 'Bank Account', value: 'BANK_ACCOUNT' }
              ]}
              onChange={(value) => {
                if (value !== 'BANK_ACCOUNT') {
                  paymentForm.setFieldValue('bankAccountId', undefined)
                }
              }}
            />
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.destinationType !== currentValues.destinationType
            }
          >
            {({ getFieldValue }) =>
              getFieldValue('destinationType') === 'BANK_ACCOUNT' ? (
                <Form.Item
                  name="bankAccountId"
                  label="Bank Account"
                  rules={[{ required: true, message: 'Please select a bank account' }]}
                >
                  <Select
                    options={bankAccounts}
                    showSearch
                    allowClear
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                  />
                </Form.Item>
              ) : null
            }
          </Form.Item>

          <div className="flex justify-end gap-2">
            <Button
              onClick={() => {
                setIsPaymentModalOpen(false)
                paymentForm.resetFields()
                setSelectedRental(null)
              }}
            >
              Cancel
            </Button>
            <Button type="primary" htmlType="submit">
              Record Payment
            </Button>
          </div>
        </Form>
      </Modal>

      <Modal
        title="Adjust Payment Entry"
        open={isAdjustmentModalOpen}
        onCancel={() => {
          setIsAdjustmentModalOpen(false)
          adjustmentForm.resetFields()
          setSelectedRental(null)
        }}
        footer={null}
      >
        <Form
          form={adjustmentForm}
          onFinish={handleAdjustPayment}
          layout="vertical"
          className="mt-4"
        >
          <Form.Item
            name="amount"
            label="Adjustment Amount"
            rules={[{ required: true, message: 'Please enter the adjustment amount' }]}
          >
            <InputNumber
              className="w-full"
              placeholder="Enter adjustment amount"
              formatter={(value) => `PKR ${value || ''}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={(value) => Number(value!.replace(/PKR\s?|(,*)/g, ''))}
              min={Number(1)}
            />
          </Form.Item>

          <Form.Item
            name="type"
            label="Adjustment Type"
            rules={[{ required: true, message: 'Please select the adjustment type' }]}
          >
            <Select
              options={[
                { label: 'Credit (Increase Balance)', value: 'CREDIT' },
                { label: 'Debit (Decrease Balance)', value: 'DEBIT' }
              ]}
            />
          </Form.Item>

          <Form.Item
            name="destinationType"
            label="Original Payment Location"
            rules={[{ required: true, message: 'Please select where the payment was received' }]}
          >
            <Select
              options={[
                // { label: 'Small Counter', value: 'SMALL_COUNTER' },
                { label: 'Cash Vault', value: 'CASH_VAULT' },
                { label: 'Bank Account', value: 'BANK_ACCOUNT' }
              ]}
              onChange={(value) => {
                if (value !== 'BANK_ACCOUNT') {
                  adjustmentForm.setFieldValue('bankAccountId', undefined)
                }
              }}
            />
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.destinationType !== currentValues.destinationType
            }
          >
            {({ getFieldValue }) =>
              getFieldValue('destinationType') === 'BANK_ACCOUNT' ? (
                <Form.Item
                  name="bankAccountId"
                  label="Bank Account"
                  rules={[{ required: true, message: 'Please select a bank account' }]}
                >
                  <Select
                    options={bankAccounts}
                    showSearch
                    allowClear
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                  />
                </Form.Item>
              ) : null
            }
          </Form.Item>

          <Form.Item
            name="reason"
            label="Adjustment Reason"
            rules={[{ required: true, message: 'Please provide the reason for adjustment' }]}
          >
            <Input.TextArea rows={3} placeholder="Explain why this adjustment is needed" />
          </Form.Item>

          <div className="flex justify-end gap-2">
            <Button
              onClick={() => {
                setIsAdjustmentModalOpen(false)
                adjustmentForm.resetFields()
                setSelectedRental(null)
              }}
            >
              Cancel
            </Button>
            <Button type="primary" htmlType="submit">
              Make Adjustment
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  )
}
