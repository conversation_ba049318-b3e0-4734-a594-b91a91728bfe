import { Modal, Tabs, <PERSON>, Card, DatePicker, Button, Empty, Spin, Space } from 'antd'
import { useEffect, useState } from 'react'
import { useApi } from '@/renderer/hooks'
import { accountsApi } from '@/renderer/services'
import { formatDate, formatCurrency } from '@/renderer/utils'
import dayjs from 'dayjs'
import type {
  GetAccountBalanceSummaryResponse,
  GetAccountStatementResponse,
  GetAccountStatisticsResponse
} from '@/common/types'
import { title } from 'process'

const { RangePicker } = DatePicker
const { TabPane } = Tabs

interface AccountDetailsModalProps {
  accountId: string | null
  onClose: () => void
}

export const AccountDetailsModal = ({ accountId, onClose }: AccountDetailsModalProps) => {
  const [dateRange, setDateRange] = useState<[Date, Date]>([
    dayjs().subtract(30, 'days').toDate(),
    dayjs().toDate()
  ])

  const { data: accountData, request: fetchAccount } = useApi<
    GetAccountBalanceSummaryResponse,
    [string]
  >(accountsApi.getBalanceSummary)

  // TODO: fix the types and typescript errors for these hooks.

  // const { data: statement, request: fetchStatement } = useApi<
  //   GetAccountStatementResponse,
  //   [string, Date, Date]
  // >(accountsApi.getAccountStatement)

  const { data: statistics, request: fetchStatistics } = useApi<
    GetAccountStatisticsResponse,
    [string]
  >(accountsApi.getAccountStatistics)

  useEffect(() => {
    if (accountId) {
      fetchAccount(accountId)
      fetchStatistics(accountId)
      // fetchStatement(accountId, dateRange[0], dateRange[1])
    }
  }, [accountId, dateRange])

  // const statementColumns = [
  //   {
  //     title: 'S.No',
  //     key: 'serialNumber',
  //     render: (_: any, __: any, index: number) => index + 1
  //   },
  //   {
  //     title: 'Date',
  //     dataIndex: 'date',
  //     key: 'date',
  //     render: (date: string) => formatDate(date)
  //   },
  //   {
  //     title: 'Description',
  //     dataIndex: 'description',
  //     key: 'description'
  //   },
  //   {
  //     title: 'Type',
  //     dataIndex: 'type',
  //     key: 'type',
  //     render: (type: string) => (
  //       <span className={type === 'CREDIT' ? 'text-green-600' : 'text-red-600'}>{type}</span>
  //     )
  //   },
  //   {
  //     title: 'Amount',
  //     dataIndex: 'amount',
  //     key: 'amount',
  //     render: (amount: number, record: any) => formatCurrency(amount, record.currency.code)
  //   }
  // ]

  if (!accountData) {
    return (
      <Modal open={!!accountId} onCancel={onClose} width={1200} title="Loading..." footer={null}>
        <Space className="h-full w-full items-center justify-center">
          <Spin />
        </Space>
      </Modal>
    )
  }

  return (
    <Modal
      open={!!accountId}
      onCancel={onClose}
      width={1200}
      title={`Account Details - ${accountData?.account.name}`}
      footer={null}
    >
      <div className="space-y-6">
        <div className="grid grid-cols-3 gap-4">
          {accountData.account.balances.map((balance: any) => (
            <Card key={balance.currency.code} className="text-center">
              <p className="text-sm text-gray-600">{balance.currency.code} Balance</p>
              <p
                className={`text-xl font-semibold ${
                  balance.balance >= 0 ? 'text-green-600' : 'text-red-600'
                }`}
              >
                {formatCurrency(balance.balance, balance.currency.code)}
              </p>
            </Card>
          ))}
        </div>

        <Tabs defaultActiveKey="statistics">
          <TabPane tab="Statistics" key="statistics">
            <div className="grid grid-cols-3 gap-4">
              <Card>
                <p className="text-sm text-gray-600">Total Sales</p>
                <p className="text-xl font-semibold">{statistics?.totalSales}</p>
              </Card>
              <Card>
                <p className="text-sm text-gray-600">Total Properties</p>
                <p className="text-xl font-semibold">{statistics?.totalProperties}</p>
              </Card>
              <Card>
                <p className="text-sm text-gray-600">Total Transactions</p>
                <p className="text-xl font-semibold">{accountData.totalTransactions}</p>
              </Card>
            </div>
          </TabPane>

          {/* not using this statement for now purpose not clear */}

          {/* <TabPane tab="Statement" key="statement">
            <div className="mb-4">
              <RangePicker
                value={[dayjs(dateRange[0]), dayjs(dateRange[1])]}
                onChange={(dates) => {
                  if (dates) {
                    setDateRange([dates[0]!.toDate(), dates[1]!.toDate()])
                  }
                }}
              />
            </div>
            {statement?.entries.length ? (
              <Table
                virtual
                sticky
                columns={statementColumns}
                dataSource={statement.entries}
                rowKey="id"
                pagination={false}
                scroll={{ y: 400 }}
              />
            ) : (
              <Empty description="No transactions found" />
            )}
          </TabPane> */}
        </Tabs>
      </div>
    </Modal>
  )
}
