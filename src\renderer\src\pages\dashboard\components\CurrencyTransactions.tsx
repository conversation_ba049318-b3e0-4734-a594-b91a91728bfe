import { useEffect, useState } from 'react'
import { Typography, Space, Button, Alert, Skeleton, Row, Col, Select, Table } from 'antd'
import { useApi } from '@/renderer/hooks'
import { dashboardApi } from '@/renderer/services'
import { CurrencyTransactionSummary, TimeRange } from '@/common/types'
import { FaExchangeAlt, FaSync } from 'react-icons/fa'
import { formatCurrency } from '@/renderer/utils'
import { useTheme } from '@/renderer/contexts'

const { Title } = Typography

const TIME_RANGES = [
  { value: '1D', label: 'Today' },
  { value: '7D', label: 'Last 7 Days' },
  { value: '14D', label: 'Last 14 Days' },
  { value: '30D', label: 'Last 30 Days' }
]

const CurrencyTransactions = () => {
  const { isDarkMode } = useTheme()
  const [timeRange, setTimeRange] = useState<TimeRange>('1D')

  const {
    data,
    isLoading,
    error,
    request: fetchCurrencyTransactions
  } = useApi<CurrencyTransactionSummary[], [TimeRange]>(dashboardApi.getCurrencyTransactions)

  useEffect(() => {
    fetchCurrencyTransactions(timeRange)
  }, [timeRange])

  const columns = [
    {
      title: 'Currency',
      dataIndex: 'currency',
      key: 'currency'
    },
    {
      title: 'Credits',
      dataIndex: 'credits',
      key: 'credits',
      align: 'right' as const,
      render: (value: number, record: any) => formatCurrency(value, record.currency)
    },
    {
      title: 'Debits',
      dataIndex: 'debits',
      key: 'debits',
      align: 'right' as const,
      render: (value: number, record: any) => formatCurrency(value, record.currency)
    },
    {
      title: 'Net',
      dataIndex: 'net',
      key: 'net',
      align: 'right' as const,
      render: (value: number, record: any) => formatCurrency(value, record.currency)
    },
    {
      title: 'Transactions',
      dataIndex: 'transactionCount',
      key: 'transactionCount',
      align: 'right' as const
    }
  ]

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <FaExchangeAlt className="text-2xl text-indigo-600" />
            <Title level={5} className="!mb-0">
              Currency Transactions
            </Title>
          </Space>
          <Space>
            <Select value={timeRange} disabled style={{ width: 120 }} />
            <Button icon={<FaSync />} loading />
          </Space>
        </Space>
        <Skeleton active paragraph={{ rows: 4 }} />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <FaExchangeAlt className="text-2xl text-indigo-600" />
            <Title level={5} className="!mb-0">
              Currency Transactions
            </Title>
          </Space>
          <Space>
            <Select value={timeRange} disabled style={{ width: 120 }} />
            <Button icon={<FaSync />} onClick={() => fetchCurrencyTransactions(timeRange)} />
          </Space>
        </Space>
        <Alert
          message="Error"
          description="Failed to load currency transactions"
          type="error"
          showIcon
        />
      </div>
    )
  }

  if (!data) return null

  return (
    <div className="flex flex-col gap-4">
      <Space className="flex justify-between">
        <Space>
          <FaExchangeAlt className="text-2xl text-indigo-600" />
          <Title level={5} className="!mb-0">
            Currency Transactions
          </Title>
        </Space>
        <Space>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            options={TIME_RANGES}
            style={{ width: 120 }}
          />
          <Button icon={<FaSync />} onClick={() => fetchCurrencyTransactions(timeRange)} />
        </Space>
      </Space>

      <div className={`rounded-lg p-4 ${isDarkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Table
              dataSource={data}
              columns={columns}
              pagination={false}
              size="small"
              className={isDarkMode ? 'dark-table' : ''}
            />
          </Col>
        </Row>
      </div>
    </div>
  )
}

export default CurrencyTransactions
