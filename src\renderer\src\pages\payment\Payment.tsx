import { lazy, useState } from 'react'
import { <PERSON><PERSON>, Card, Layout, Tabs } from 'antd'
import {
  PaymentList,
  CreatePayment,
  PaymentSummary,
  OutstandingPayments,
  PaymentStatistics,
  TransferFunds,
  AccountsTransfer,
  TransferList
} from './components'
import { TransitionWrapper } from '@/renderer/components'
import { MdPayments } from 'react-icons/md'

const { Content } = Layout

const Payment = () => {
  const [isCreatePaymentOpen, setIsCreatePaymentOpen] = useState(false)
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const [activeTab, setActiveTab] = useState('1')

  const items = [
    {
      key: '1',
      label: 'Payment List',
      children: <PaymentList refreshTrigger={refreshTrigger} />
    },
    {
      key: '2',
      label: 'Transfer List',
      children: <TransferList refreshTrigger={refreshTrigger} />
    },

    // {
    //   key: '3',
    //   label: 'Payment Summary',
    //   children: <PaymentSummary />
    // },
    // {
    //   key: '4',
    //   label: 'Outstanding Payments',
    //   children: <OutstandingPayments />
    // },
    {
      key: '5',
      label: 'Statistics',
      children: <PaymentStatistics />
    },
    {
      key: '6',
      label: 'Transfer Funds',
      children: <TransferFunds />
    },
    {
      key: '7',
      label: 'Accounts Transfer',
      children: <AccountsTransfer onSuccess={() => setRefreshTrigger((prev) => prev + 1)} />
    }
  ]

  return (
    <div className="flex h-full flex-col gap-4 p-6">
      <div className="relative flex-1">
        <TransitionWrapper isVisible={isCreatePaymentOpen} direction="right">
          <CreatePayment
            onClose={() => setIsCreatePaymentOpen(false)}
            setRefreshTrigger={setRefreshTrigger}
          />
        </TransitionWrapper>

        <TransitionWrapper isVisible={!isCreatePaymentOpen} direction="left">
          <Layout className="h-full">
            <Card>
              <div className="flex items-center justify-between">
                <div>
                  {/* <h1 className="text-2xl font-semibold">Payment Management</h1>
                  <p>Manage payments, transfers, and view payment statistics</p> */}
                </div>

                <Button type="primary" onClick={() => setIsCreatePaymentOpen(true)}>
                  <MdPayments />
                  Create Payment
                </Button>
              </div>

              <Tabs
                activeKey={activeTab}
                onChange={setActiveTab}
                items={items}
                className="rounded-lg shadow-sm"
                tabBarStyle={{
                  marginBottom: 0,
                  paddingLeft: 16,
                  paddingRight: 16,
                  borderBottom: '1px solid #f0f0f0'
                }}
              />
            </Card>
          </Layout>
        </TransitionWrapper>
      </div>
    </div>
  )
}

export default Payment
