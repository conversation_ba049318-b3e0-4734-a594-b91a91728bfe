import { GetAccountLedgerParams, GetAccountStatementParams, GetBalanceSheetParams, GetDailyLedgerParams, GetProfitLossStatementParams, GetTransactionsByCategoryParams, GetTransactionsByLocationParams, ReconcileBalanceParams, LedgerEntry, PaginatedLedgerResponse } from '@/common/types'
import { prisma } from '../db'
import { Prisma, TransactionType, TransactionLocation, TransactionCategory } from '@prisma/client'




class LedgerService {

    async getPaginatedLedgerEntries(
        accountId: string,
        params: {
            page?: number
            limit?: number
            currencyId: string
            startDate?: Date
            endDate?: Date
        }
    ): Promise<PaginatedLedgerResponse> {

        const page = params.page || 1
        let limit = params.limit || 50

        // Get currency ID if a currency code was provided
        let currencyId = params.currencyId
        if (currencyId.length <= 3) {
            const currency = await prisma.currency.findFirst({
                where: { code: currencyId }
            })
            if (!currency) {
                throw new Error(`Currency ${currencyId} not found`)
            }
            currencyId = currency.id
        }

        // Build the date filter if either startDate or endDate is provided
        let dateFilter = {};
        if (params.startDate || params.endDate) {
            // Handle timezone issues by setting time to start/end of day
            let startDateWithTime = params.startDate ? new Date(params.startDate) : undefined;
            let endDateWithTime = params.endDate ? new Date(params.endDate) : undefined;

            // If we have a start date, set it to the beginning of the day (00:00:00)
            if (startDateWithTime) {
                startDateWithTime.setHours(0, 0, 0, 0);
            }

            // If we have an end date, set it to the end of the day (23:59:59.999)
            if (endDateWithTime) {
                endDateWithTime.setHours(23, 59, 59, 999);
            }

            dateFilter = {
                date: {
                    ...(startDateWithTime && { gte: startDateWithTime }),
                    ...(endDateWithTime && { lte: endDateWithTime })
                }
            };
        }

        const where = {
            accountId,
            isDeleted: false,
            currencyId,
            ...dateFilter
        }

        // Get total count for pagination
        const total = await prisma.ledgerEntry.count({ where })

        const skip = (page - 1) * limit

        // Get entries for current page - OLDEST FIRST (ASC)
        const entries = await prisma.ledgerEntry.findMany({
            where,
            orderBy: { date: 'asc' }, // Oldest first
            include: {
                currency: true,
                createdBy: {
                    select: {
                        id: true,
                        name: true
                    }
                }
            },
            skip,
            take: limit
        })

        // SIMPLIFIED APPROACH:
        // 1. Always calculate the starting balance by summing up all entries before the current page
        // 2. Don't worry about sequential navigation or date filters affecting the balance calculation

        // Calculate the initial running balance using aggregation
        let runningBalance = 0

        // If we have entries and we're not on the first page, calculate the balance for all entries before the first entry of this page
        if (entries.length > 0 && page > 1) {
            // Get sum of all credits and debits BEFORE the first entry of this page
            const [creditsBefore, debitsBefore] = await Promise.all([
                prisma.ledgerEntry.aggregate({
                    where: {
                        accountId,
                        currencyId,
                        isDeleted: false,
                        date: { lt: entries[0].date }
                    },
                    _sum: { amount: true }
                }),
                prisma.ledgerEntry.aggregate({
                    where: {
                        accountId,
                        currencyId,
                        isDeleted: false,
                        date: { lt: entries[0].date }
                    },
                    _sum: { amount: true }
                })
            ]);

            // Calculate the initial balance by summing credits and subtracting debits
            const creditSum = creditsBefore._sum.amount || 0;
            const debitSum = debitsBefore._sum.amount || 0;

            // Fix: We need to filter by transaction type as well
            const [creditsBeforeFiltered, debitsBeforeFiltered] = await Promise.all([
                prisma.ledgerEntry.aggregate({
                    where: {
                        accountId,
                        currencyId,
                        isDeleted: false,
                        date: { lt: entries[0].date },
                        type: TransactionType.CREDIT
                    },
                    _sum: { amount: true }
                }),
                prisma.ledgerEntry.aggregate({
                    where: {
                        accountId,
                        currencyId,
                        isDeleted: false,
                        date: { lt: entries[0].date },
                        type: TransactionType.DEBIT
                    },
                    _sum: { amount: true }
                })
            ]);

            // Fix: For account ledger, CREDIT increases balance and DEBIT decreases balance
            // Make sure we correctly handle the sign of the amount for DEBIT entries
            runningBalance = (creditsBeforeFiltered._sum.amount || 0) - Math.abs(debitsBeforeFiltered._sum.amount || 0);
        }

        // Calculate running balances for this page's entries
        let entriesWithBalance = entries.map(entry => {
            // For ASC order (oldest first), add transaction effects to running balance
            // CREDIT increases the balance, DEBIT decreases it
            if (entry.type === TransactionType.CREDIT) {
                runningBalance += entry.amount
            } else {
                // Fix: Ensure we use the absolute value of DEBIT amounts to correctly subtract
                runningBalance -= Math.abs(entry.amount)
            }

            return { ...entry, balance: runningBalance }
        })

        return {
            entries: entriesWithBalance,
            pagination: {
                total,
                page,
                limit,
                hasMore: total > (skip + limit)
            }
        }
    }


    // Account Balance Management

    async getAccountStatement(accountId: string, params: GetAccountStatementParams) {
        const { startDate, endDate, currencyId } = params

        // Calculate opening balance by considering credits and debits separately
        const [credits, debits] = await Promise.all([
            prisma.ledgerEntry.aggregate({
                where: {
                    accountId,
                    currencyId,
                    date: { lt: startDate },
                    isDeleted: false,
                    type: TransactionType.CREDIT
                },
                _sum: {
                    amount: true
                }
            }),
            prisma.ledgerEntry.aggregate({
                where: {
                    accountId,
                    currencyId,
                    date: { lt: startDate },
                    isDeleted: false,
                    type: TransactionType.DEBIT
                },
                _sum: {
                    amount: true
                }
            })
        ])

        // Ensure we use absolute values for DEBIT amounts
        const openingBalance = (credits._sum.amount || 0) - Math.abs(debits._sum.amount || 0)

        // Get all transactions within date range
        const transactions = await prisma.ledgerEntry.findMany({
            where: {
                accountId,
                currencyId,
                date: {
                    gte: startDate,
                    lte: endDate
                },
                isDeleted: false
            },
            include: {
                currency: true,
                createdBy: {
                    select: {
                        id: true,
                        name: true
                    }
                }
            },
            orderBy: { date: 'asc' }
        })

        // Calculate running balance and closing balance
        let runningBalance = openingBalance
        const transactionsWithBalance = transactions.map(tx => {
            // Use absolute value for DEBIT to ensure proper subtraction
            runningBalance += tx.type === 'CREDIT' ? tx.amount : -Math.abs(tx.amount)
            return {
                ...tx,
                runningBalance
            }
        })

        return {
            openingBalance,
            closingBalance: runningBalance,
            transactions: transactionsWithBalance,
            period: {
                startDate,
                endDate
            }
        }
    }

    // Cash Management

    async reconcileBalance(params: ReconcileBalanceParams) {
        const { location, currencyId, bankAccountId } = params

        if (location === TransactionLocation.BANK_ACCOUNT && !bankAccountId) {
            throw new Error('Bank account ID is required for bank reconciliation')
        }

        // Calculate credits and debits separately using database aggregation
        const [credits, debits] = await Promise.all([
            prisma.ledgerEntry.aggregate({
                where: {
                    currencyId,
                    isDeleted: false,
                    type: TransactionType.CREDIT,
                    OR: [
                        { sourceType: location },
                        { destinationType: location }
                    ],
                    ...(bankAccountId && { bankAccountId })
                },
                _sum: { amount: true }
            }),
            prisma.ledgerEntry.aggregate({
                where: {
                    currencyId,
                    isDeleted: false,
                    type: TransactionType.DEBIT,
                    OR: [
                        { sourceType: location },
                        { destinationType: location }
                    ],
                    ...(bankAccountId && { bankAccountId })
                },
                _sum: { amount: true }
            })
        ])

        // Calculate the net balance from ledger
        const calculatedBalance = (credits._sum.amount ?? 0) - (debits._sum.amount ?? 0)

        // Get current balance from the location
        let currentBalance: number
        switch (location) {
            case TransactionLocation.SMALL_COUNTER:
                const counter = await prisma.smallCounter.findFirst()
                if (!counter) {
                    throw new Error('Small counter not found')
                }
                currentBalance = await this.getBalanceForCurrency(counter, currencyId)
                break

            case TransactionLocation.CASH_VAULT:
                const vault = await prisma.cashVault.findFirst()
                if (!vault) {
                    throw new Error('Cash vault not found')
                }
                currentBalance = await this.getBalanceForCurrency(vault, currencyId)
                break

            case TransactionLocation.BANK_ACCOUNT:
                if (!bankAccountId) {
                    throw new Error('Bank account ID is required')
                }
                const bankAccount = await prisma.bankAccount.findUnique({
                    where: { id: bankAccountId }
                })
                if (!bankAccount) {
                    throw new Error('Bank account not found')
                }
                currentBalance = bankAccount.balance
                break

            default:
                throw new Error(`Unsupported location: ${location}`)
        }

        const isReconciled = calculatedBalance === currentBalance

        return {
            isReconciled,
            currentBalance,
            calculatedBalance,
            difference: currentBalance - calculatedBalance
        }
    }

    private async getBalanceForCurrency(
        location: { pkrBalance: number; usdBalance: number; aedBalance: number; afnBalance: number },
        currencyId: string
    ) {
        const currency = await prisma.currency.findUnique({
            where: { id: currencyId }
        })

        if (!currency) {
            throw new Error('Currency not found')
        }

        const balanceField = `${currency.code.toLowerCase()}Balance` as keyof typeof location
        return location[balanceField]
    }


    // Reports & Analytics

    async getDailyLedger(params: GetDailyLedgerParams) {
        const { date } = params
        const startOfDay = new Date(date)
        startOfDay.setHours(0, 0, 0, 0)
        const endOfDay = new Date(date)
        endOfDay.setHours(23, 59, 59, 999)

        const entries = await prisma.ledgerEntry.findMany({
            where: {
                date: {
                    gte: startOfDay,
                    lte: endOfDay
                },
                isDeleted: false
            },
            include: {
                account: true,
                currency: true,
                bankAccount: true,
                createdBy: {
                    select: {
                        id: true,
                        name: true
                    }
                },
                sale: true,
                payment: true,
                expense: true,
                exchange: true,
                exchangeTo: true
            },
            orderBy: { date: 'asc' }
        })

        // Calculate totals by currency
        const totals = entries.reduce((acc, entry) => {
            const currencyCode = entry.currency.code
            if (!acc[currencyCode]) {
                acc[currencyCode] = {
                    credits: 0,
                    debits: 0,
                    net: 0
                }
            }

            const amount = entry.amount
            if (entry.type === 'CREDIT') {
                acc[currencyCode].credits += amount
                acc[currencyCode].net += amount
            } else {
                acc[currencyCode].debits += amount
                acc[currencyCode].net -= amount
            }

            return acc
        }, {} as Record<string, { credits: number; debits: number; net: number }>)

        return {
            date,
            entries,
            totals
        }
    }

    async getTransactionsByCategory(params: GetTransactionsByCategoryParams) {
        const { startDate, endDate, categories } = params

        const entries = await prisma.ledgerEntry.findMany({
            where: {
                date: {
                    gte: startDate,
                    lte: endDate
                },
                isDeleted: false,
                ...(categories && { transactionType: { in: categories } })
            },
            include: {
                currency: true
            },
            orderBy: { date: 'asc' }
        })

        // Group by category and currency
        const summary = entries.reduce((acc, entry) => {
            const category = entry.transactionType
            const currencyCode = entry.currency.code

            if (!acc[category]) {
                acc[category] = {}
            }
            if (!acc[category][currencyCode]) {
                acc[category][currencyCode] = {
                    count: 0,
                    total: 0,
                    entries: []
                }
            }

            acc[category][currencyCode].count++
            acc[category][currencyCode].total += entry.type === 'CREDIT' ? entry.amount : -entry.amount
            acc[category][currencyCode].entries.push(entry)

            return acc
        }, {} as Record<TransactionCategory, Record<string, { count: number; total: number; entries: typeof entries }>>)

        return {
            startDate,
            endDate,
            summary
        }
    }

    async getTransactionsByLocation(params: GetTransactionsByLocationParams) {
        const { startDate, endDate, locations } = params

        const entries = await prisma.ledgerEntry.findMany({
            where: {
                date: {
                    gte: startDate,
                    lte: endDate
                },
                isDeleted: false,
                OR: [
                    ...(locations ? [{ sourceType: { in: locations } }] : []),
                    ...(locations ? [{ destinationType: { in: locations } }] : [])
                ]
            },
            include: {
                currency: true
            },
            orderBy: { date: 'asc' }
        })

        // Group by location and currency
        const summary = entries.reduce((acc, entry) => {
            const processLocation = (location: TransactionLocation, isSource: boolean) => {
                if (!acc[location]) {
                    acc[location] = {}
                }

                const currencyCode = entry.currency.code
                if (!acc[location][currencyCode]) {
                    acc[location][currencyCode] = {
                        inflow: 0,
                        outflow: 0,
                        net: 0,
                        entries: []
                    }
                }

                const amount = entry.amount
                if (isSource) {
                    acc[location][currencyCode].outflow += amount
                    acc[location][currencyCode].net -= amount
                } else {
                    acc[location][currencyCode].inflow += amount
                    acc[location][currencyCode].net += amount
                }
                acc[location][currencyCode].entries.push(entry)
            }

            processLocation(entry.sourceType, true)
            processLocation(entry.destinationType, false)

            return acc
        }, {} as Record<TransactionLocation, Record<string, {
            inflow: number
            outflow: number
            net: number
            entries: typeof entries
        }>>)

        return {
            startDate,
            endDate,
            summary
        }
    }

    async getBalanceSheet(params: GetBalanceSheetParams) {
        const { date } = params
        // Assets
        const [
            smallCounter,
            cashVault,
            bankAccounts,
            _accountBalances, // Unused but kept for structure
            accountsReceivable,
            inventory
        ] = await Promise.all([
            prisma.smallCounter.findFirst(),
            prisma.cashVault.findFirst(),
            prisma.bankAccount.findMany({
                where: { isActive: true }
            }),
            prisma.accountBalance.findMany({
                include: { currency: true }
            }),
            prisma.account.findMany({
                where: {
                    isDeleted: false,
                    balances: {
                        some: {
                            balance: { gt: 0 }
                        }
                    }
                },
                include: {
                    balances: {
                        include: { currency: true }
                    }
                }
            }),
            Promise.all([
                prisma.car.count({
                    where: { status: 'AVAILABLE' }
                }),
                prisma.carPart.count({
                    where: { status: 'AVAILABLE' }
                }),
                prisma.electronic.count({
                    where: { status: 'AVAILABLE' }
                }),
                prisma.scrap.count({
                    where: { status: 'AVAILABLE' }
                })
            ])
        ])

        // Group cash and bank balances by currency
        const cashBalances = {} as Record<string, {
            smallCounter: number
            cashVault: number
            bankAccounts: number
            total: number
        }>

        // Add small counter balances
        if (smallCounter) {
            Object.entries(smallCounter).forEach(([key, value]) => {
                if (key.endsWith('Balance')) {
                    const currency = key.replace('Balance', '').toUpperCase()
                    if (!cashBalances[currency]) {
                        cashBalances[currency] = {
                            smallCounter: 0,
                            cashVault: 0,
                            bankAccounts: 0,
                            total: 0
                        }
                    }
                    cashBalances[currency].smallCounter = value as number
                    cashBalances[currency].total += value as number
                }
            })
        }

        // Add cash vault balances
        if (cashVault) {
            Object.entries(cashVault).forEach(([key, value]) => {
                if (key.endsWith('Balance')) {
                    const currency = key.replace('Balance', '').toUpperCase()
                    if (!cashBalances[currency]) {
                        cashBalances[currency] = {
                            smallCounter: 0,
                            cashVault: 0,
                            bankAccounts: 0,
                            total: 0
                        }
                    }
                    cashBalances[currency].cashVault = value as number
                    cashBalances[currency].total += value as number
                }
            })
        }

        // Add bank account balances (PKR only)
        const bankBalance = bankAccounts.reduce((sum, account) => sum + account.balance, 0)
        if (!cashBalances['PKR']) {
            cashBalances['PKR'] = {
                smallCounter: 0,
                cashVault: 0,
                bankAccounts: 0,
                total: 0
            }
        }
        cashBalances['PKR'].bankAccounts = bankBalance
        cashBalances['PKR'].total += bankBalance

        // Group accounts receivable by currency
        const receivables = accountsReceivable.reduce((acc, account) => {
            account.balances.forEach(balance => {
                const currency = balance.currency.code
                if (!acc[currency]) {
                    acc[currency] = 0
                }
                acc[currency] += balance.balance
            })
            return acc
        }, {} as Record<string, number>)

        // Inventory count
        const [carCount, partCount, electronicCount, scrapCount] = inventory

        return {
            date,
            assets: {
                cash: cashBalances,
                receivables,
                inventory: {
                    cars: carCount,
                    parts: partCount,
                    electronics: electronicCount,
                    scrap: scrapCount,
                    total: carCount + partCount + electronicCount + scrapCount
                }
            }
        }
    }

    async getProfitLossStatement(params: GetProfitLossStatementParams) {
        const { startDate, endDate } = params

        // Get all transactions within the period
        const entries = await prisma.ledgerEntry.findMany({
            where: {
                date: {
                    gte: startDate,
                    lte: endDate
                },
                isDeleted: false,
                transactionType: {
                    in: [
                        TransactionCategory.SALE,
                        TransactionCategory.PAYMENT,
                        TransactionCategory.EXPENSE,
                        TransactionCategory.RENT
                    ]
                }
            },
            include: {
                currency: true,
                sale: true,
                payment: true,
                expense: true
            }
        })

        // Group transactions by category and currency
        const summary = entries.reduce((acc, entry) => {
            const currencyCode = entry.currency.code
            if (!acc[currencyCode]) {
                acc[currencyCode] = {
                    revenue: {
                        sales: 0,
                        rent: 0,
                        total: 0
                    },
                    expenses: {
                        operational: 0,
                        other: 0,
                        total: 0
                    },
                    profit: 0
                }
            }

            const amount = entry.amount
            switch (entry.transactionType) {
                case TransactionCategory.SALE:
                    acc[currencyCode].revenue.sales += amount
                    acc[currencyCode].revenue.total += amount
                    break
                case TransactionCategory.RENT:
                    acc[currencyCode].revenue.rent += amount
                    acc[currencyCode].revenue.total += amount
                    break
                case TransactionCategory.EXPENSE:
                    if (entry.expense?.category === 'OPERATIONAL') {
                        acc[currencyCode].expenses.operational += amount
                    } else {
                        acc[currencyCode].expenses.other += amount
                    }
                    acc[currencyCode].expenses.total += amount
                    break
            }

            acc[currencyCode].profit =
                acc[currencyCode].revenue.total -
                acc[currencyCode].expenses.total

            return acc
        }, {} as Record<string, {
            revenue: {
                sales: number
                rent: number
                total: number
            }
            expenses: {
                operational: number
                other: number
                total: number
            }
            profit: number
        }>)

        return {
            period: {
                startDate,
                endDate
            },
            summary
        }
    }


    async getAccountLedgerEntriesForPDF(accountId: string, currencyCode: string) {
        // Get currency ID if a currency code was provided

        let currencyId: string | undefined
        if (currencyCode.length <= 3) {
            const currency = await prisma.currency.findFirst({
                where: { code: currencyCode }
            })
            if (!currency) {
                throw new Error(`Currency ${currencyCode} not found`)
            }
            currencyId = currency.id
        }

        // Get all entries for the account in chronological order (oldest first)
        const entries = await prisma.ledgerEntry.findMany({
            where: {
                accountId,
                currencyId,
                isDeleted: false
            },
            orderBy: { date: 'asc' },
            include: {
                currency: true,
                createdBy: {
                    select: {
                        id: true,
                        name: true
                    }
                },
                sale: true,
                payment: true,
                expense: true,
                exchange: true,
                exchangeTo: true
            }
        })

        // Calculate running balance and totals
        let runningBalance = 0
        let totalCredits = 0
        let totalDebits = 0

        const entriesWithBalance = entries.map(entry => {
            // Update running balance based on transaction type
            if (entry.type === 'CREDIT') {
                runningBalance += entry.amount
                totalCredits += entry.amount
            } else {
                // Use absolute value for DEBIT entries to properly subtract
                runningBalance -= Math.abs(entry.amount)
                totalDebits += Math.abs(entry.amount)
            }

            return {
                ...entry,
                runningBalance
            }
        })

        // Get account details
        const account = await prisma.account.findUnique({
            where: { id: accountId },
            include: {
                balances: {
                    where: { currencyId },
                    include: { currency: true }
                }
            }
        })

        if (!account) {
            throw new Error('Account not found')
        }

        // Prepare summary
        const summary = {
            accountName: account.name,
            accountPhone: account.phoneNumber,
            accountAddress: account.address,
            currency: account.balances[0]?.currency.code || currencyId,
            totalCredits,
            totalDebits,
            netBalance: totalCredits - totalDebits,
            totalEntries: entries.length,
            dateRange: entries.length > 0 ? {
                from: entries[0].date,
                to: entries[entries.length - 1].date
            } : null
        }

        return {
            summary,
            entries: entriesWithBalance
        }
    }


}

export const ledgerService = new LedgerService();
