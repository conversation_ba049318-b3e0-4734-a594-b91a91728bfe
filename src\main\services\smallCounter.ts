import { prisma } from '../db';
import { InitializeSmallCounterData, SmallCounterBalances, CurrencyBalance } from '@/common/types';

class SmallCounterService {

    private async ensureSmallCounterExists() {
        const smallCounter = await prisma.smallCounter.findFirst();
        if (!smallCounter) {
            throw new Error('Small counter not initialized');
        }
        return smallCounter;
    }

    async initializeSmallCounter(data: InitializeSmallCounterData) {
        const smallCounter = await prisma.smallCounter.findFirst()
        if (smallCounter) {
            throw new Error('Small counter already initialized');
        }

        return await prisma.$transaction(async (tx) => {
            // Create the small counter first
            const counter = await tx.smallCounter.create({
                data: {
                    pkrBalance: data.pkrBalance ?? 0,
                    usdBalance: data.usdBalance ?? 0,
                    aedBalance: data.aedBalance ?? 0,
                    afnBalance: data.afnBalance ?? 0
                }
            });

            // Create ledger entries for each non-zero balance
            const currencies = [
                { code: 'PKR', balance: data.pkrBalance },
                { code: 'USD', balance: data.usdBalance },
                { code: 'AED', balance: data.aedBalance },
                { code: 'AFN', balance: data.afnBalance }
            ];

            for (const { code, balance } of currencies) {
                if (balance && balance > 0) {
                    await tx.ledgerEntry.create({
                        data: {
                            amount: balance,
                            type: 'CREDIT',
                            description: `Opening balance for Small Counter - ${code}`,
                            sourceType: 'OTHER',
                            destinationType: 'SMALL_COUNTER',
                            transactionType: 'OPENING_BALANCE',
                            currency: { connect: { code } },
                            createdBy: { connect: { id: data.userId } }
                        }
                    });
                }
            }

            return counter;
        });
    }

    async getSmallCounterBalances(): Promise<SmallCounterBalances> {
        const counter = await this.ensureSmallCounterExists();
        return counter;
    }

    async getBalanceByCurrency(currency: string): Promise<CurrencyBalance> {
        const counter = await this.ensureSmallCounterExists();

        let balance: number;
        switch (currency) {
            case 'PKR':
                balance = counter.pkrBalance;
                break;
            case 'USD':
                balance = counter.usdBalance;
                break;
            case 'AED':
                balance = counter.aedBalance;
                break;
            case 'AFN':
                balance = counter.afnBalance;
                break;
            default:
                throw new Error(`Unsupported currency: ${currency}`);
        }

        return { currency, balance };
    }


    async adjustBalance(currency: string, adjustment: number, reason: string, userId: string) {
        return await prisma.$transaction(async (tx) => {
            await this.ensureSmallCounterExists();

            // Create ledger entry for audit trail
            await tx.ledgerEntry.create({
                data: {
                    amount: Math.abs(adjustment),
                    type: adjustment > 0 ? 'CREDIT' : 'DEBIT',
                    description: `Manual balance adjustment: ${reason}`,
                    sourceType: adjustment > 0 ? 'OTHER' : 'SMALL_COUNTER',
                    destinationType: adjustment > 0 ? 'SMALL_COUNTER' : 'OTHER',
                    transactionType: 'OTHER',
                    currency: { connect: { code: currency } },
                    createdBy: { connect: { id: userId } }
                }
            });

            // Update small counter balance
            const updateData = this.getUpdateDataForCurrency(currency, adjustment);
            return await tx.smallCounter.updateMany({ data: updateData });
        });
    }

    async getBalanceHistory(currency: string, startDate: Date, endDate: Date) {
        const entries = await prisma.ledgerEntry.findMany({
            where: {
                currencyId: currency,
                date: { gte: startDate, lte: endDate },
                OR: [
                    { sourceType: 'SMALL_COUNTER' },
                    { destinationType: 'SMALL_COUNTER' }
                ],
                isDeleted: false
            },
            include: {
                createdBy: { select: { name: true } }
            },
            orderBy: { date: 'desc' }
        });

        return entries.map(entry => ({
            date: entry.date,
            amount: entry.destinationType === 'SMALL_COUNTER' ? entry.amount : -entry.amount,
            description: entry.description,
            type: entry.type,
            createdBy: entry.createdBy.name
        }));
    }

    async transferToVault(currency: string, amount: number, userId: string) {
        return await prisma.$transaction(async (tx) => {
            // Validate balance
            const currentBalance = await this.getBalanceByCurrency(currency);
            if (currentBalance.balance < amount) {
                throw new Error(`Insufficient ${currency} balance`);
            }

            // Create ledger entry
            await tx.ledgerEntry.create({
                data: {
                    amount,
                    type: 'DEBIT',
                    description: `Transfer to vault`,
                    sourceType: 'SMALL_COUNTER',
                    destinationType: 'CASH_VAULT',
                    transactionType: 'TRANSFER',
                    currency: { connect: { code: currency } },
                    createdBy: { connect: { id: userId } }
                }
            });

            // Update small counter balance
            await tx.smallCounter.updateMany({
                data: this.getUpdateDataForCurrency(currency, -amount)
            });

            // Update vault balance
            await tx.cashVault.updateMany({
                data: this.getUpdateDataForCurrency(currency, amount)
            });
        });
    }

    private getUpdateDataForCurrency(currency: string, adjustment: number) {
        const supportedCurrencies = ['PKR', 'USD', 'AED', 'AFN'];
        if (!supportedCurrencies.includes(currency)) {
            throw new Error(`Unsupported currency: ${currency}`);
        }

        return {
            [currency.toLowerCase() + 'Balance']: {
                increment: adjustment
            }
        };
    }


    async reconcileBalance(currency: string) {
        const currencyId = await prisma.currency.findFirst({
            where: { code: currency },
            select: { id: true }
        });

        if (!currencyId) {
            throw new Error(`Currency ${currency} not found`);
        }

        // Calculate credits and debits separately using database aggregation
        const [credits, debits] = await Promise.all([
            prisma.ledgerEntry.aggregate({
                where: {
                    currencyId: currencyId.id,
                    isDeleted: false,
                    type: 'CREDIT',
                    OR: [
                        { sourceType: 'SMALL_COUNTER' },
                        { destinationType: 'SMALL_COUNTER' }
                    ]
                },
                _sum: { amount: true }
            }),
            prisma.ledgerEntry.aggregate({
                where: {
                    currencyId: currencyId.id,
                    isDeleted: false,
                    type: 'DEBIT',
                    OR: [
                        { sourceType: 'SMALL_COUNTER' },
                        { destinationType: 'SMALL_COUNTER' }
                    ]
                },
                _sum: { amount: true }
            })
        ]);

        // Calculate the net balance
        const calculatedBalance = (credits._sum.amount ?? 0) - (debits._sum.amount ?? 0);

        // Get current balance from the counter
        const currentBalance = await this.getBalanceByCurrency(currency);
        const isReconciled = calculatedBalance === currentBalance.balance;

        return {
            isReconciled,
            currentBalance: currentBalance.balance,
            calculatedBalance,
            difference: currentBalance.balance - calculatedBalance
        };
    }

    async getDailyTransactions(date: Date) {
        const startOfDay = new Date(date);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(date);
        endOfDay.setHours(23, 59, 59, 999);

        return await prisma.ledgerEntry.findMany({
            where: {
                date: { gte: startOfDay, lte: endOfDay },
                OR: [
                    { sourceType: 'SMALL_COUNTER' },
                    { destinationType: 'SMALL_COUNTER' }
                ],
                isDeleted: false
            },
            include: {
                currency: true,
                createdBy: { select: { name: true } }
            },
            orderBy: { date: 'desc' }
        });
    }

    async getBalanceAtDate(currency: string, date: Date) {
        const entries = await prisma.ledgerEntry.findMany({
            where: {
                currencyId: currency,
                date: { lte: date },
                OR: [
                    { sourceType: 'SMALL_COUNTER' },
                    { destinationType: 'SMALL_COUNTER' }
                ],
                isDeleted: false
            }
        });

        return entries.reduce((balance, entry) => {
            return balance + (
                entry.destinationType === 'SMALL_COUNTER' ? entry.amount : -entry.amount
            );
        }, 0);
    }

    async getAllTransactions(page: number = 1, pageSize: number = 20, startDate?: Date, endDate?: Date) {
        const skip = (page - 1) * pageSize;

        // Handle date filtering
        let dateFilter = {}
        if (startDate && endDate) {
            if (startDate.toDateString() === endDate.toDateString()) {
                // Same day - filter for entire day
                const start = new Date(startDate)
                start.setHours(0, 0, 0, 0)
                const end = new Date(startDate)
                end.setHours(23, 59, 59, 999)
                dateFilter = {
                    date: {
                        gte: start,
                        lte: end
                    }
                }
            } else {
                // Date range
                dateFilter = {
                    date: {
                        gte: startDate,
                        lte: endDate
                    }
                }
            }
        }

        const [transactions, total] = await Promise.all([
            prisma.ledgerEntry.findMany({
                where: {
                    OR: [
                        { sourceType: 'SMALL_COUNTER' },
                        { destinationType: 'SMALL_COUNTER' }
                    ],
                    isDeleted: false,
                    ...dateFilter
                },
                include: {
                    currency: true,
                    createdBy: { select: { name: true } },
                    account: { select: { name: true } }
                },
                orderBy: { date: 'desc' },
                skip,
                take: pageSize
            }),
            prisma.ledgerEntry.count({
                where: {
                    OR: [
                        { sourceType: 'SMALL_COUNTER' },
                        { destinationType: 'SMALL_COUNTER' }
                    ],
                    isDeleted: false,
                    ...dateFilter
                }
            })
        ]);

        return {
            transactions,
            pagination: {
                total,
                pages: Math.ceil(total / pageSize),
                currentPage: page,
                pageSize
            }
        };
    }

}

export const smallCounterService = new SmallCounterService();