import { prisma } from '../db';
import { CreateAccountData, UpdateAccountData, GetAccountsParams } from '@/common/types';
import { Prisma } from '@prisma/client';

class AccountService {
    async createAccount(data: CreateAccountData) {
        const currencies = await prisma.currency.findMany();

        return await prisma.$transaction(async (tx) => {
            // Create account
            const account = await tx.account.create({
                data: {
                    name: data.name,
                    phoneNumber: data.phoneNumber,
                    address: data.address,
                    type: data.type
                }
            });

            // Initialize balances and create ledger entries
            for (const currency of currencies) {
                const openingBalance = data.openingBalances?.[currency.code as keyof typeof data.openingBalances] ?? 0;

                await tx.accountBalance.create({
                    data: {
                        accountId: account.id,
                        currencyId: currency.id,
                        balance: openingBalance
                    }
                });

                if (openingBalance !== 0) {
                    await tx.ledgerEntry.create({
                        data: {
                            amount: openingBalance,
                            type: openingBalance > 0 ? 'CREDIT' : 'DEBIT',
                            description: 'Opening Balance',
                            sourceType: 'OTHER',
                            destinationType: 'ACCOUNT',
                            transactionType: 'OPENING_BALANCE',
                            currency: { connect: { id: currency.id } },
                            account: { connect: { id: account.id } },
                            createdBy: { connect: { id: data.userId } }
                        }
                    });
                }
            }

            return account;
        });
    }

    async deleteAccount(id: string) {
        return await prisma.$transaction(async (tx) => {
            // Check for active non-opening-balance transactions
            const hasActiveTransactions = await tx.ledgerEntry.findFirst({
                where: {
                    accountId: id,
                    NOT: { transactionType: 'OPENING_BALANCE' },
                    isDeleted: false
                }
            });

            if (hasActiveTransactions) {
                throw new Error('Cannot delete account with existing transactions');
            }

            // Check for active sales or properties
            const [hasSales, hasProperties] = await Promise.all([
                tx.sale.findFirst({ where: { accountId: id, isDeleted: false } }),
                tx.rentedProperty.findFirst({ where: { tenantId: id, isDeleted: false } })
            ]);

            if (hasSales || hasProperties) {
                throw new Error('Cannot delete account with existing sales or properties');
            }

            // Delete all ledger entries (including opening balances)
            await tx.ledgerEntry.deleteMany({
                where: { accountId: id }
            });

            await tx.rentedProperty.deleteMany({
                where: { tenantId: id }
            });

            await tx.sale.deleteMany({
                where: { accountId: id }
            });

            // Delete account balances
            await tx.accountBalance.deleteMany({
                where: { accountId: id }
            });

            // Delete account
            return await tx.account.delete({
                where: { id }
            });
        });
    }

    async updateAccount(id: string, data: UpdateAccountData) {
        return await prisma.account.update({
            where: { id },
            data: {
                phoneNumber: data.phoneNumber,
                address: data.address
            }
        });
    }

    async getAccountById(id: string) {
        return await prisma.account.findUnique({
            where: { id },
            include: {
                balances: {
                    select: {
                        currency: {
                            select: {
                                code: true,
                            }
                        },
                        balance: true
                    }
                }
            }
        });
    }

    async getAccounts({ page, limit, type, search, includeDeleted = false }: GetAccountsParams) {
        const where: Prisma.AccountWhereInput = {
            ...(type && { type }),
            ...(search && {
                OR: [
                    { name: { contains: search, mode: 'insensitive' } },
                    { phoneNumber: { contains: search, mode: 'insensitive' } }
                ]
            }),
            ...(!includeDeleted && { isDeleted: false })
        };

        const [accounts, total] = await Promise.all([
            prisma.account.findMany({
                where,
                include: {
                    balances: {
                        include: {
                            currency: true
                        }
                    }
                },
                orderBy: { createdAt: 'desc' },
                skip: (page - 1) * limit,
                take: limit
            }),
            prisma.account.count({ where })
        ]);

        return {
            accounts,
            total,
            page,
            totalPages: Math.ceil(total / limit)
        };
    }


    async getAccountsForSelect() {
        const accounts = await prisma.account.findMany({
            where: { isDeleted: false },
            select: {
                id: true,
                name: true,
                type: true,
                phoneNumber: true
            },
            orderBy: { name: 'asc' }
        });

        return accounts.map(account => ({
            value: account.id,
            label: `${account.name} (${account.phoneNumber})`,
            type: account.type
        }));
    }

    async getCustomerAccountsForSelect() {
        const accounts = await prisma.account.findMany({
            where: {
                isDeleted: false,
                OR: [
                    { type: 'CUSTOMER' },
                    // { type: 'BOTH' }
                ]
            },
            select: {
                id: true,
                name: true,
                phoneNumber: true
            },
            orderBy: { name: 'asc' }
        });

        return accounts.map(account => ({
            value: account.id,
            label: `${account.name} (${account.phoneNumber})`
        }));
    }

    async getTenantAccountsForSelect() {
        const accounts = await prisma.account.findMany({
            where: {
                isDeleted: false,
                OR: [
                    { type: 'TENANT' },
                    // { type: 'BOTH' }
                ]
            },
            select: {
                id: true,
                name: true,
                phoneNumber: true
            },
            orderBy: { name: 'asc' }
        });

        return accounts.map(account => ({
            value: account.id,
            label: `${account.name} (${account.phoneNumber})`
        }));
    }


    async getAccountStatement(accountId: string, startDate: Date, endDate: Date) {
        const account = await prisma.account.findUnique({
            where: { id: accountId },
            include: {
                balances: {
                    include: { currency: true }
                }
            }
        });

        if (!account) throw new Error('Account not found');

        const ledgerEntries = await prisma.ledgerEntry.findMany({
            where: {
                accountId,
                date: {
                    gte: startDate,
                    lte: endDate
                },
                isDeleted: false
            },
            include: {
                currency: true,
                sale: {
                    select: {
                        id: true,
                        date: true,
                        totalAmount: true
                    }
                },
                payment: true
            },
            orderBy: { date: 'asc' }
        });

        return {
            account,
            entries: ledgerEntries,
            startDate,
            endDate
        };
    }

    async getBalanceSummary(accountId: string) {
        const account = await prisma.account.findUnique({
            where: { id: accountId },
            include: { balances: { include: { currency: true } } }
        });

        if (!account) throw new Error('Account not found');

        // const balances = await prisma.accountBalance.findMany({
        //     where: { accountId },
        //     include: { currency: true }
        // });

        const totalTransactions = await prisma.ledgerEntry.count({
            where: {
                accountId,
                isDeleted: false
            }
        });

        return {
            account,
            totalTransactions
        };
    }

    async getAccountStatistics(accountId: string) {
        const [sales, ledgerEntries, properties] = await Promise.all([
            prisma.sale.count({
                where: { accountId }
            }),
            prisma.ledgerEntry.groupBy({
                by: ['type'],
                where: {
                    accountId,
                    isDeleted: false
                },
                _sum: { amount: true },
                _count: true
            }),
            prisma.rentedProperty.count({
                where: { tenantId: accountId }
            })
        ]);

        const transactions = ledgerEntries.reduce((acc, entry) => {
            acc[entry.type] = {
                count: entry._count,
                total: entry._sum.amount || 0
            };
            return acc;
        }, {} as Record<string, { count: number; total: number }>);

        return {
            totalSales: sales,
            totalProperties: properties,
            transactions
        };
    }


}

export const accountService = new AccountService();