import { IRequest, ResetPasswordData, UpdatePasswordData, CreateUserData, LoginData } from '@/common/types';
import { userService } from '../services';

class UserController {

    async login(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as LoginData;

        if (!data.username || !data.password) {
            throw new Error('Username and password are required');
        }

        return await userService.login(data);
    }

    async createUser(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as CreateUserData;

        if (!data.username || !data.password || !data.role || !data.name) {
            throw new Error('Username, password, role, and name are required');
        }

        if (data.password.length < 8) {
            throw new Error('Password must be at least 8 characters long');
        }

        return await userService.createUser(data);
    }

    async updatePassword(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { userId, currentPassword, newPassword } = req.body ?? {};

        if (!userId || !currentPassword || !newPassword) {
            throw new Error('All fields are required');
        }

        if (newPassword.length < 8) {
            throw new Error('New password must be at least 8 characters long');
        }

        return await userService.updatePassword({ userId, currentPassword, newPassword });
    }

    async resetPassword(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { userId, newPassword, adminId } = req.body as ResetPasswordData;

        if (!userId || !newPassword || !adminId) {
            throw new Error('All fields are required');
        }

        if (newPassword.length < 8) {
            throw new Error('New password must be at least 8 characters long');
        }

        return await userService.resetPassword({ userId, newPassword, adminId });
    }

    async getUsers(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { page = 1, limit = 10, includeInactive } = req.query ?? {};
        return await userService.getUsers({
            page: Number(page),
            limit: Number(limit),
            includeInactive: Boolean(includeInactive)
        });
    }

    async deactivateUser(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { userId } = req.params ?? {};
        const { adminId } = req.body ?? {};

        if (!userId || !adminId) {
            throw new Error('User ID and admin ID are required');
        }

        return await userService.deactivateUser(userId, adminId);
    }
}

export const userController = new UserController();