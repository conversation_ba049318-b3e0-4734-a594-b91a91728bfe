import { DatePicker, Form, Input, InputNumber, Modal, Select } from 'antd'
import type { CreateExpenseData } from '@/common/types'
import { useBankContext } from '@/renderer/contexts'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { handleDatePickerValue } from '@/renderer/utils'

dayjs.extend(utc)
dayjs.extend(timezone)

interface ExpenseFormProps {
  open: boolean
  loading?: boolean
  onCancel: () => void
  onSubmit: (data: CreateExpenseData) => Promise<void>
}

type TransactionLocation =
  // | 'SMALL_COUNTER'
  'CASH_VAULT' | 'BANK_ACCOUNT' | 'ACCOUNT' | 'EXTERNAL' | 'OTHER'

const TransactionLocation = {
  // SMALL_COUNTER: 'SMALL_COUNTER',
  CASH_VAULT: 'CASH_VAULT',
  BANK_ACCOUNT: 'BANK_ACCOUNT',
  ACCOUNT: 'ACCOUNT',
  EXTERNAL: 'EXTERNAL',
  OTHER: 'OTHER'
}

const EXPENSE_CATEGORIES = [
  'Office Supplies',
  'Utilities',
  'Rent',
  'Salaries',
  'Marketing',
  'Travel',
  'Maintenance',
  'Other'
]

export const ExpenseForm = ({ open, loading, onCancel, onSubmit }: ExpenseFormProps) => {
  const [form] = Form.useForm()

  const { banks } = useBankContext()

  const user = useSelector((state: IRootState) => state.user.data)

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      await onSubmit({
        ...values,
        date: handleDatePickerValue(values.date?.toDate()),
        userId: user?.id || ''
      })
      form.resetFields()
    } catch (error) {
      // Form validation error, no need to handle
    }
  }

  return (
    <Modal
      title="Create Expense"
      open={open}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={600}
    >
      <Form form={form} layout="vertical" className="mt-4">
        <Form.Item
          name="amount"
          label="Amount"
          rules={[
            { required: true, message: 'Please enter amount' },
            { type: 'number', min: 0, message: 'Amount must be positive' }
          ]}
        >
          <InputNumber
            className="w-full"
            placeholder="Enter amount"
            formatter={(value) => `PKR ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={(value) => value!.replace(/PKR\s?|(,*)/g, '')}
          />
        </Form.Item>

        <Form.Item
          name="category"
          label="Category"
          rules={[{ required: true, message: 'Please select category' }]}
        >
          <Select placeholder="Select category">
            {EXPENSE_CATEGORIES.map((category) => (
              <Select.Option key={category} value={category}>
                {category}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="date"
          label="Date"
          rules={[{ required: true, message: 'Please select date' }]}
        >
          <DatePicker format="DD/MM/YYYY" />
        </Form.Item>

        <Form.Item
          name="description"
          label="Description"
          rules={[{ required: true, message: 'Please enter description' }]}
        >
          <Input.TextArea rows={4} placeholder="Enter expense description" />
        </Form.Item>

        <Form.Item
          name="paymentSource"
          label="Payment Source"
          rules={[{ required: true, message: 'Please select payment source' }]}
        >
          <Select placeholder="Select payment source">
            {/* <Select.Option value={TransactionLocation.SMALL_COUNTER}>Small Counter</Select.Option> */}
            <Select.Option value={TransactionLocation.CASH_VAULT}>Cash Vault</Select.Option>
            <Select.Option value={TransactionLocation.BANK_ACCOUNT}>Bank Account</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item noStyle shouldUpdate={(prev, curr) => prev.paymentSource !== curr.paymentSource}>
          {({ getFieldValue }) =>
            getFieldValue('paymentSource') === TransactionLocation.BANK_ACCOUNT && (
              <Form.Item
                name="bankAccountId"
                label="Bank Account"
                rules={[{ required: true, message: 'Please select bank account' }]}
              >
                <Select placeholder="Select bank account" options={banks} />
              </Form.Item>
            )
          }
        </Form.Item>
      </Form>
    </Modal>
  )
}
