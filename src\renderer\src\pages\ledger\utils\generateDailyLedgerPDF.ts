import { A5_CONFIG, PDF_STYLES, savePDF, printPDF } from '@/renderer/utils/pdfUtils'
import jsPDF from 'jspdf'
import 'jspdf-autotable'
import dayjs from 'dayjs'

interface DailyLedgerData {
    entries: any[]
    totals: Record<string, {
        credits: number
        debits: number
        net: number
    }>
}

export const generateDailyLedgerPDF = async (
    data: DailyLedgerData,
    date: Date
): Promise<jsPDF> => {
    // Create PDF document
    const doc = new jsPDF(A5_CONFIG)
    const pageWidth = doc.internal.pageSize.width

    // Define the header function
    const drawHeader = () => {
        // Add header
        doc.setFontSize(PDF_STYLES.header.fontSize)
        doc.text('Daily Ledger', pageWidth / 2, 10, { align: 'center' })

        // Add date
        doc.setFontSize(PDF_STYLES.header.titleFontSize)
        doc.text(`Date: ${dayjs(date).format('DD/MM/YYYY')}`, 10, 20)

        // Add currency summaries in a 2x2 grid
        const margin = 8
        const boxPadding = 3
        const lineSpacing = 6
        let yPos = 30

        // Calculate dimensions
        const boxWidth = pageWidth - (2 * margin)
        const cellWidth = (boxWidth - boxPadding) / 2
        const cellHeight = 4 * lineSpacing
        const totalHeight = (2 * cellHeight) + (3 * boxPadding)

        // Draw main box
        doc.setDrawColor(0)
        doc.setLineWidth(0.1)
        doc.rect(margin, yPos - boxPadding, boxWidth, totalHeight)

        // Draw internal lines
        // Vertical divider
        doc.line(
            margin + cellWidth + boxPadding,
            yPos - boxPadding,
            margin + cellWidth + boxPadding,
            yPos + totalHeight - boxPadding
        )
        // Horizontal divider
        doc.line(
            margin,
            yPos + cellHeight + boxPadding,
            margin + boxWidth,
            yPos + cellHeight + boxPadding
        )

        // Function to render currency summary in a cell
        const renderCurrencyCell = (currency: string, totals: any, x: number, y: number) => {
            if (!data.totals[currency]) return; // Skip if currency doesn't exist

            // Currency header
            doc.setFontSize(PDF_STYLES.header.titleFontSize)
            doc.setTextColor(0, 0, 0)
            doc.setFont('helvetica', 'bold')
            doc.text(`${currency}`, x, y)
            y += lineSpacing

            // Credits
            doc.setFont('helvetica', 'normal')
            doc.setTextColor(0, 0, 0)
            doc.text('Credits:', x + 5, y)
            doc.setTextColor(0, 128, 0)
            doc.text(
                data.totals[currency].credits.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }),
                x + cellWidth - 5,
                y,
                { align: 'right' }
            )
            y += lineSpacing

            // Debits
            doc.setTextColor(0, 0, 0)
            doc.text('Debits:', x + 5, y)
            doc.setTextColor(255, 0, 0)
            doc.text(
                data.totals[currency].debits.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }),
                x + cellWidth - 5,
                y,
                { align: 'right' }
            )
            y += lineSpacing

            // Net
            doc.setTextColor(0, 0, 0)
            doc.text('Net:', x + 5, y)
            doc.setTextColor(data.totals[currency].net >= 0 ? 0 : 255, data.totals[currency].net >= 0 ? 128 : 0, 0)
            doc.text(
                data.totals[currency].net.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }),
                x + cellWidth - 5,
                y,
                { align: 'right' }
            )
        }

        // Render each currency in its cell
        // Top row: PKR and USD
        renderCurrencyCell('PKR', data.totals['PKR'], margin + boxPadding, yPos + 1)
        renderCurrencyCell('USD', data.totals['USD'], margin + cellWidth + (2 * boxPadding), yPos + 1)

        // Bottom row: AED and AFN
        renderCurrencyCell('AED', data.totals['AED'], margin + boxPadding, yPos + cellHeight + (2 * boxPadding) + 1)
        renderCurrencyCell('AFN', data.totals['AFN'], margin + cellWidth + (2 * boxPadding), yPos + cellHeight + (2 * boxPadding) + 1)

        // Reset text color and font
        doc.setTextColor(0, 0, 0)
        doc.setFont('helvetica', 'normal')

        return yPos + totalHeight + 5; // Return the Y position after the header
    }

    // Draw the header once
    const startY = drawHeader();

    // Add entries table
    // @ts-ignore (jspdf-autotable types are not properly recognized)
    doc.autoTable({
        startY: startY,
        columns: [
            { header: 'S.No', dataKey: 'serialNumber' },
            { header: 'Time', dataKey: 'time' },
            { header: 'Description', dataKey: 'description' },
            { header: 'Category', dataKey: 'category' },
            { header: 'Type', dataKey: 'type' },
            { header: 'Amount', dataKey: 'amount' },
            { header: 'Account', dataKey: 'account' }
        ],
        body: data.entries.map((entry, index) => ({
            serialNumber: index + 1,
            time: dayjs(entry.date).format('HH:mm:ss'),
            description: entry.description,
            category: entry.transactionType.replace(/_/g, ' '),
            type: entry.type,
            amount: entry.amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }),
            account: entry.account?.name || '',
            _type: entry.type,
            _currency: entry.currency.code
        })),
        styles: {
            ...PDF_STYLES.table.styles,
            fontSize: 7,
            fillColor: [255, 255, 255] // Ensure all cells have white background
        },
        headStyles: {
            ...PDF_STYLES.table.headStyles,
            textColor: [255, 255, 255], // Ensure header text is white, not gray
            fillColor: [0, 0, 0] // black background for header
        },
        footStyles: PDF_STYLES.table.footStyles,
        margin: { left: 5, right: 5 },
        columnStyles: {
            serialNumber: { cellWidth: 10, halign: 'center' },
            time: { cellWidth: 15 },
            description: { cellWidth: 'auto' },
            category: { cellWidth: 20 },
            type: { cellWidth: 15 },
            amount: { cellWidth: 30, halign: 'right' },
            account: { cellWidth: 20 }
        },
        didParseCell: function (data) {
            if (data.section === 'body') {
                if (data.column.dataKey === 'type') {
                    data.cell.styles.textColor = data.row.raw._type === 'CREDIT' ? [0, 128, 0] : [255, 0, 0]
                }
                if (data.column.dataKey === 'amount') {
                    data.cell.styles.textColor = data.row.raw._type === 'CREDIT' ? [0, 128, 0] : [255, 0, 0]
                    data.cell.text[0] = `${data.cell.text[0]} ${data.row.raw._currency}`
                }
            }
        }
    })

    // Add footer
    const pageCount = (doc as any).internal.getNumberOfPages()
    for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i)
        doc.setFontSize(PDF_STYLES.footer.fontSize)
        doc.setTextColor(0, 0, 0)
        doc.text(
            `Page ${i} of ${pageCount}`,
            pageWidth / 2,
            doc.internal.pageSize.height - PDF_STYLES.footer.margin,
            { align: 'center' }
        )
    }

    return doc
}

export const handleDailyLedgerPDF = async (
    data: DailyLedgerData,
    date: Date,
    action: 'save' | 'print'
): Promise<void> => {
    try {
        const doc = await generateDailyLedgerPDF(data, date)
        const fileName = `Daily_Ledger_${dayjs(date).format('YYYY-MM-DD')}`

        if (action === 'save') {
            await savePDF(doc, fileName)
        } else {
            await printPDF(doc)
        }
    } catch (error) {
        console.error('Failed to generate PDF:', error)
        throw error
    }
}
