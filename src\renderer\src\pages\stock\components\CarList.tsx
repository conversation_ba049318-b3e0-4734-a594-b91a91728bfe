import { Table, Input, Select, Tag, Card } from 'antd'
import { useState, useEffect } from 'react'
import { useApi } from '@/renderer/hooks'
import { stockApi } from '@/renderer/services'
import { formatCurrency, formatDate } from '@/renderer/utils'
import type { CarWithRelations, GetStockParams, StockResponse } from '@/common/types'
import { SortAscendingOutlined, SortDescendingOutlined } from '@ant-design/icons'

type ItemStatus = 'AVAILABLE' | 'SOLD'

const { Search } = Input

export const CarList = () => {
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [search, setSearch] = useState('')
  const [status, setStatus] = useState<ItemStatus | 'ALL'>('AVAILABLE')
  const [orderBy, setOrderBy] = useState<'asc' | 'desc'>('desc')

  const {
    data,
    isLoading,
    request: fetchCars
  } = useApi<StockResponse<CarWithRelations>, [GetStockParams]>(stockApi.getCars)

  useEffect(() => {
    fetchCars({ page, limit: pageSize, search, status, orderBy })
  }, [page, pageSize, search, status, orderBy])

  const columns = [
    { title: 'Sr. No', key: 'serialNumber', render: (_: any, __: any, index: number) => index + 1 },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Model',
      dataIndex: 'modelNumber',
      key: 'modelNumber'
    },
    {
      title: 'Chassis',
      dataIndex: 'chassisNumber',
      key: 'chassisNumber'
    },
    {
      title: 'Color',
      dataIndex: 'color',
      key: 'color'
    },
    {
      title: 'Container',
      dataIndex: ['container', 'containerNumber'],
      key: 'container'
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'AVAILABLE' ? 'green' : 'blue'}>{status}</Tag>
      )
    },
    {
      title: 'Container Opened',
      dataIndex: ['container', 'openedAt'],
      key: 'openedAt',
      render: (date: string) => formatDate(date)
    },
    {
      title: 'Sale Details',
      key: 'sale',
      render: (_: any, record: CarWithRelations) =>
        record.sale && (
          <div>
            <div>{formatDate(record.sale.date.toString())}</div>
            <div className="text-gray-500">{formatCurrency(record.sale.totalAmount, 'PKR')}</div>
          </div>
        )
    }
  ]

  return (
    <Card className="rounded-lg">
      <div className="mb-4 flex gap-4">
        <Search placeholder="Search cars..." onSearch={setSearch} className="max-w-md" />
        <Select
          value={status}
          onChange={setStatus}
          className="w-32"
          options={[
            { label: 'All', value: 'ALL' },
            { label: 'Available', value: 'AVAILABLE' },
            { label: 'Sold', value: 'SOLD' }
          ]}
        />
        <Select
          value={orderBy}
          onChange={setOrderBy}
          className="w-56"
          options={[
            {
              label: (
                <div className="flex items-center">
                  <SortDescendingOutlined className="mr-2" /> Newest Container First
                </div>
              ),
              value: 'desc'
            },
            {
              label: (
                <div className="flex items-center">
                  <SortAscendingOutlined className="mr-2" /> Oldest Container First
                </div>
              ),
              value: 'asc'
            }
          ]}
        />
      </div>

      <Table
        columns={columns}
        dataSource={data?.items}
        rowKey="id"
        loading={isLoading}
        virtual
        sticky
        size="small"
        pagination={{
          current: page,
          pageSize,
          total: data?.total,
          position: ['topRight'],
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '50', '100'],
          showQuickJumper: true,
          onChange: (page, pageSize) => {
            setPage(page)
            setPageSize(pageSize)
          },
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
        }}
      />
    </Card>
  )
}
