import { Form, Input, InputNumber } from 'antd'
import type { FormInstance } from 'antd'

interface CarPartFormProps {
  form: FormInstance
}

export const CarPartForm = ({ form }: CarPartFormProps) => {
  return (
    <>
      <Form.Item
        name="name"
        label="Part Name"
        rules={[{ required: true, message: 'Please enter part name' }]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        name="quantity"
        label="Quantity"
        rules={[{ required: true, message: 'Please enter quantity' }]}
        initialValue={1}
      >
        <InputNumber min={1} className="w-full" />
      </Form.Item>
    </>
  )
}
