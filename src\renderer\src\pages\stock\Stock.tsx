import { Card, Tabs } from 'antd'
import { StockSummary } from './components/StockSummary'
import { CarList } from './components/CarList'
import { CarPartList } from './components/CarPartList'
import { ElectronicList } from './components/ElectronicList'
import { ScrapList } from './components/ScrapList'

const Stock = () => {
  return (
    <Card className="m-6 flex flex-col">
      <StockSummary className="my-6" />

      <Tabs
        // className="flex-1"

        className="flex-1 rounded-lg shadow-sm"
        tabBarStyle={{
          marginBottom: 0,
          paddingLeft: 16,
          paddingRight: 16,
          borderBottom: '1px solid #f0f0f0'
        }}
        items={[
          {
            key: 'cars',
            label: 'Cars',
            children: <CarList />
          },
          {
            key: 'carParts',
            label: 'Car Parts',
            children: <CarPartList />
          },
          {
            key: 'electronics',
            label: 'Electronics',
            children: <ElectronicList />
          },
          {
            key: 'scraps',
            label: 'Scraps',
            children: <ScrapList />
          }
        ]}
      />
    </Card>
  )
}

export default Stock
