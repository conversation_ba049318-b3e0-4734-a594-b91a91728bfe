import { TransactionType, ManualEntryTarget } from "@prisma/client";

export interface CreateManualEntryData {
    amount: number;
    description?: string;
    transactionDate: Date;
    entryType: TransactionType; // CREDIT or DEBIT
    currencyCode: string; // Currency code (PKR, USD, AED, AFN)

    // Target entity (only one should be set)
    accountId?: string; // For accounts (customers, tenants)
    bankAccountId?: string; // For bank accounts
    targetType?: ManualEntryTarget; // For cash vault

    createdById: string;
}

export enum ManualEntryStatus {
    ACTIVE = 'ACTIVE',
    DELETED = 'DELETED',
    ALL = 'ALL'
}

export enum ManualEntrySortOrder {
    OLDEST_FIRST = 'OLDEST_FIRST',
    NEWEST_FIRST = 'NEWEST_FIRST'
}

export interface GetManualEntriesParams {
    page?: number;
    pageSize?: number;
    startDate?: Date;
    endDate?: Date;
    entryType?: TransactionType;
    targetType?: ManualEntryTarget;
    accountId?: string; // Changed from partyId
    bankAccountId?: string; // Changed from bankId
    currencyCode?: string; // Added currency filter
    status?: ManualEntryStatus;
    sortOrder?: ManualEntrySortOrder;
    search?: string;
}

export interface VoidManualEntryParams {
    id: string;
    deletedById: string;
    deletionReason: string;
}

export interface ManualEntryItem {
    id: string;
    amount: number;
    description?: string;
    transactionDate: Date;
    entryType: TransactionType;

    // Currency information
    currency: {
        id: string;
        code: string;
        name: string;
    };

    // Target information
    accountId?: string;
    account?: {
        id: string;
        name: string;
        type: string;
    };

    bankAccountId?: string;
    bankAccount?: {
        id: string;
        accountNumber: string;
        bankName: string;
    };

    targetType?: ManualEntryTarget;

    // Ledger information
    ledgerEntryId: string;
    ledgerEntry: {
        id: string;
        sourceType: string;
        destinationType: string;
        transactionType: string;
    };

    // Audit trail
    createdAt: Date;
    updatedAt: Date;
    createdBy: {
        id: string;
        name: string;
    };

    // Deletion tracking
    isDeleted: boolean;
    deletedAt?: Date;
    deletedBy?: {
        id: string;
        name: string;
    };
    deletionReason?: string;
}

export interface ManualEntriesResponse {
    entries: ManualEntryItem[];
    pagination: {
        total: number;
        pages: number;
        currentPage: number;
        pageSize: number;
    };
}

export interface CreateManualEntryResult {
    manualEntry: ManualEntryItem;
    message: string;
}

export interface VoidManualEntryResult {
    success: boolean;
    message: string;
}

// Filter options for frontend
export interface ManualEntryFilters {
    dateRange?: {
        startDate: Date;
        endDate: Date;
    };
    entryType?: TransactionType;
    targetType?: ManualEntryTarget;
    accountId?: string; // Changed from partyId
    bankAccountId?: string; // Changed from bankId
    currencyCode?: string; // Added currency filter
    status?: ManualEntryStatus;
    sortOrder?: ManualEntrySortOrder;
    search?: string;
}