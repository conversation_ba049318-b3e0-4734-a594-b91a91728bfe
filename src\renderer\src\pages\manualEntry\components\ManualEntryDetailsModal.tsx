import { Modal, Descriptions, Tag, Typography } from 'antd'
import type { ManualEntryItem } from '@/common/types/manualEntry'
import dayjs from 'dayjs'

const { Text } = Typography

interface ManualEntryDetailsModalProps {
  entry: ManualEntryItem | null
  open: boolean
  onClose: () => void
}

export const ManualEntryDetailsModal = ({ entry, open, onClose }: ManualEntryDetailsModalProps) => {
  if (!entry) return null

  const getTargetInfo = () => {
    if (entry.account) {
      return {
        type: 'Account',
        name: entry.account.name,
        details: `Type: ${entry.account.type}`
      }
    }

    if (entry.bankAccount) {
      return {
        type: 'Bank Account',
        name: entry.bankAccount.bankName,
        details: `Account: ${entry.bankAccount.accountNumber}`
      }
    }

    if (entry.targetType) {
      return {
        type: 'Cash Location',
        name: 'Cash Vault',
        details: ''
      }
    }

    return { type: 'Unknown', name: '-', details: '' }
  }

  const targetInfo = getTargetInfo()

  const getStatusTag = () => {
    if (entry.isDeleted) {
      return <Tag color="red">Deleted</Tag>
    }
    return <Tag color="green">Active</Tag>
  }

  return (
    <Modal title="Manual Entry Details" open={open} onCancel={onClose} footer={null} width={700}>
      <Descriptions bordered column={2} size="small">
        <Descriptions.Item label="Entry ID" span={2}>
          <Text code>{entry.id}</Text>
        </Descriptions.Item>

        <Descriptions.Item label="Amount">
          <span
            className={
              entry.entryType === 'DEBIT'
                ? 'font-semibold text-red-600'
                : 'font-semibold text-green-600'
            }
          >
            {entry.entryType === 'DEBIT' ? '-' : '+'}
            {entry.amount.toLocaleString()} {entry.currency.code}
          </span>
        </Descriptions.Item>

        <Descriptions.Item label="Currency">
          <Tag color="blue">
            {entry.currency.code} - {entry.currency.name}
          </Tag>
        </Descriptions.Item>

        <Descriptions.Item label="Entry Type">
          <Tag color={entry.entryType === 'DEBIT' ? 'red' : 'green'}>{entry.entryType}</Tag>
        </Descriptions.Item>

        <Descriptions.Item label="Transaction Date">
          {dayjs(entry.transactionDate).format('DD/MM/YYYY HH:mm')}
        </Descriptions.Item>

        <Descriptions.Item label="Status">{getStatusTag()}</Descriptions.Item>

        <Descriptions.Item label="Target Type">{targetInfo.type}</Descriptions.Item>

        <Descriptions.Item label="Target Name">{targetInfo.name}</Descriptions.Item>

        {targetInfo.details && (
          <Descriptions.Item label="Target Details" span={2}>
            {targetInfo.details}
          </Descriptions.Item>
        )}

        <Descriptions.Item label="Description" span={2}>
          {entry.description || <Text type="secondary">No description</Text>}
        </Descriptions.Item>

        <Descriptions.Item label="Created By">{entry.createdBy.name}</Descriptions.Item>

        <Descriptions.Item label="Created At">
          {dayjs(entry.createdAt).format('DD/MM/YYYY HH:mm:ss')}
        </Descriptions.Item>

        <Descriptions.Item label="Ledger Entry ID">
          <Text code>{entry.ledgerEntryId}</Text>
        </Descriptions.Item>

        <Descriptions.Item label="Transaction Flow">
          <Tag color="blue">
            {entry.ledgerEntry.sourceType} → {entry.ledgerEntry.destinationType}
          </Tag>
        </Descriptions.Item>

        <Descriptions.Item label="Transaction Type" span={2}>
          <Tag color="purple">{entry.ledgerEntry.transactionType}</Tag>
        </Descriptions.Item>

        {entry.isDeleted && (
          <>
            <Descriptions.Item label="Deleted By">
              {entry.deletedBy?.name || 'Unknown'}
            </Descriptions.Item>

            <Descriptions.Item label="Deleted At">
              {entry.deletedAt ? dayjs(entry.deletedAt).format('DD/MM/YYYY HH:mm:ss') : '-'}
            </Descriptions.Item>

            <Descriptions.Item label="Deletion Reason" span={2}>
              {entry.deletionReason || <Text type="secondary">No reason provided</Text>}
            </Descriptions.Item>
          </>
        )}
      </Descriptions>
    </Modal>
  )
}
