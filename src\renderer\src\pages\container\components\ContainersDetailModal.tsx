import { Modal, Tabs, Table, Tag, Descriptions } from 'antd'
import { useEffect } from 'react'
import { useApi } from '@/renderer/hooks'
import { containerApi } from '@/renderer/services'
import { formatCurrency } from '@/renderer/utils'
import { ContainerSummaryResponse, ContainerByIdResponse } from '@/common/types'
import { title } from 'process'
import { useTheme } from '@/renderer/contexts/ThemeContext'

interface ContainersDetailModalProps {
  containerId: string | null
  onClose: () => void
}

export const ContainersDetailModal = ({ containerId, onClose }: ContainersDetailModalProps) => {
  const { isDarkMode } = useTheme()

  const { data: container, request: fetchContainer } = useApi<ContainerByIdResponse, [string]>(
    containerApi.getContainerById
  )

  const { data: summary, request: fetchSummary } = useApi<ContainerSummaryResponse, [string]>(
    containerApi.getContainerSummary
  )

  useEffect(() => {
    if (containerId) {
      fetchContainer(containerId)
      fetchSummary(containerId)
    }
  }, [containerId])

  const getStatusColor = (status: string) => {
    return status === 'SOLD' ? 'green' : 'blue'
  }

  const carColumns = [
    { title: 'Chassis Number', dataIndex: 'chassisNumber' },
    { title: 'Model', dataIndex: 'modelNumber' },
    { title: 'Name', dataIndex: 'name' },
    { title: 'Color', dataIndex: 'color' },
    {
      title: 'Status',
      dataIndex: 'status',
      render: (status: string) => <Tag color={getStatusColor(status)}>{status}</Tag>
    }
  ]

  const itemColumns = [
    { title: 'Name', dataIndex: 'name' },
    { title: 'Initial Quantity', dataIndex: 'initialQuantity' },
    { title: 'RemainingQuantity', dataIndex: 'quantity' },
    {
      title: 'Status',
      dataIndex: 'status',
      render: (status: string) => <Tag color={getStatusColor(status)}>{status}</Tag>
    }
  ]

  const scrapColumns = [
    { title: 'Description', dataIndex: 'description' },
    { title: 'Initial Quantity', dataIndex: 'initialQuantity' },
    { title: 'Remaining Quantity', dataIndex: 'quantity' },
    {
      title: 'Status',
      dataIndex: 'status',
      render: (status: string) => <Tag color={getStatusColor(status)}>{status}</Tag>
    }
  ]

  const modalContent =
    container && summary ? (
      <>
        <div className="mb-6 grid grid-cols-3 gap-4">
          <div
            className={`shadow-large rounded-lg bg-[length:200%_200%] bg-[10%_10%] p-4 transition-[background-position] duration-500 ease-in-out hover:bg-[90%_90%] ${
              isDarkMode
                ? 'bg-gradient-to-br from-blue-950 via-slate-950 to-blue-900'
                : 'bg-gradient-to-br from-sky-200 via-white to-blue-200'
            }`}
          >
            <p className="text-sm text-gray-400">Total Expenses</p>
            <p className="text-xl font-semibold">{formatCurrency(summary.totalExpenses, 'PKR')}</p>
          </div>
          <div
            className={`shadow-large rounded-lg bg-[length:200%_200%] bg-[10%_10%] p-4 transition-[background-position] duration-500 ease-in-out hover:bg-[90%_90%] ${
              isDarkMode
                ? 'bg-gradient-to-br from-blue-950 via-slate-950 to-blue-900'
                : 'bg-gradient-to-br from-sky-200 via-white to-blue-200'
            }`}
          >
            <p className="text-sm text-gray-400">Total Sales</p>
            <p className="text-xl font-semibold">{formatCurrency(summary.totalSales, 'PKR')}</p>
          </div>
          <div
            className={`shadow-large rounded-lg bg-[length:200%_200%] bg-[10%_10%] p-4 transition-[background-position] duration-500 ease-in-out hover:bg-[90%_90%] ${
              isDarkMode
                ? 'bg-gradient-to-br from-blue-950 via-slate-950 to-blue-900'
                : 'bg-gradient-to-br from-sky-200 via-white to-blue-200'
            }`}
          >
            <p className="text-sm text-gray-400">Profit</p>
            <p className="text-xl font-semibold">{formatCurrency(summary.profit, 'PKR')}</p>
          </div>
          <div
            className={`shadow-large rounded-lg bg-[length:200%_200%] bg-[10%_10%] p-4 transition-[background-position] duration-500 ease-in-out hover:bg-[90%_90%] ${
              isDarkMode
                ? 'bg-gradient-to-br from-blue-950 via-slate-950 to-blue-900'
                : 'bg-gradient-to-br from-sky-200 via-white to-blue-200'
            }`}
          >
            <p className="text-sm text-gray-400">Sales minus Expenses</p>
            <p className="text-xl font-semibold">
              {formatCurrency(summary.salesMinusExpenses, 'PKR')}
            </p>
          </div>
        </div>

        <Tabs
          items={[
            {
              key: 'cars',
              label: `Cars (${summary.itemsSummary.cars.total})`,
              children: (
                <Table
                  columns={carColumns}
                  dataSource={container.cars}
                  rowKey="id"
                  pagination={false}
                />
              )
            },
            {
              key: 'carParts',
              label: `Car Parts (${summary.itemsSummary.carParts.total})`,
              children: (
                <Table
                  columns={itemColumns}
                  dataSource={container.carParts}
                  rowKey="id"
                  pagination={false}
                />
              )
            },
            {
              key: 'electronics',
              label: `Electronics (${summary.itemsSummary.electronics.total})`,
              children: (
                <Table
                  columns={itemColumns}
                  dataSource={container.electronics}
                  rowKey="id"
                  pagination={false}
                />
              )
            },
            {
              key: 'scraps',
              label: `Scraps (${summary.itemsSummary.scraps.total})`,
              children: (
                <Table
                  columns={scrapColumns}
                  dataSource={container.scraps}
                  rowKey="id"
                  pagination={false}
                />
              )
            },
            {
              key: 'containerDetails',
              label: 'Container Details',
              children: (
                <Descriptions bordered column={2}>
                  <Descriptions.Item label="Container Number">
                    {container.containerNumber}
                  </Descriptions.Item>
                  <Descriptions.Item label="Container Cost">
                    {formatCurrency(container.containerCost, 'PKR')}
                  </Descriptions.Item>
                  <Descriptions.Item label="Driver Expense">
                    {formatCurrency(container.driverExpense, 'PKR')}
                  </Descriptions.Item>
                  <Descriptions.Item label="Taxes">
                    {formatCurrency(container.taxes, 'PKR')}
                  </Descriptions.Item>
                  <Descriptions.Item label="Route Expense">
                    {formatCurrency(container.routeExpense, 'PKR')}
                  </Descriptions.Item>
                  <Descriptions.Item label="Field Rent">
                    {formatCurrency(container.fieldRent, 'PKR')}
                  </Descriptions.Item>
                </Descriptions>
              )
            }
          ]}
        />
      </>
    ) : null

  return (
    <Modal
      open={!!containerId}
      onCancel={onClose}
      width={1000}
      title={`Container ${container?.containerNumber}`}
      footer={null}
    >
      {modalContent}
    </Modal>
  )
}
