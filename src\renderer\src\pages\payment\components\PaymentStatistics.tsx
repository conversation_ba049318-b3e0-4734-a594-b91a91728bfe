import { useState, useEffect } from 'react'
import { Card, DatePicker, Table, Row, Col, Statistic, App } from 'antd'
import type { PaymentStatistics as PaymentStatsType } from '@/common/types'
import { paymentApi } from '@/renderer/services'

const { RangePicker } = DatePicker

export const PaymentStatistics = () => {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<PaymentStatsType | null>(null)
  const [dateRange, setDateRange] = useState<[Date?, Date?]>([])

  const { message } = App.useApp()

  const fetchStatistics = async () => {
    if (!dateRange[0] || !dateRange[1]) return

    setLoading(true)
    const response = await paymentApi.getPaymentStatistics(dateRange[0], dateRange[1])
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    setData(response.data.data)
  }

  useEffect(() => {
    if (dateRange[0] && dateRange[1]) {
      fetchStatistics()
    }
  }, [dateRange])

  const currencyColumns = [
    {
      title: 'Currency',
      dataIndex: 'currency',
      key: 'currency'
    },
    {
      title: 'Total Amount',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (value: number) => value.toLocaleString()
    },
    {
      title: 'Paid',
      dataIndex: 'paid',
      key: 'paid',
      render: (value: number) => <span className="text-red-500">{value.toLocaleString()}</span>
    },
    {
      title: 'Received',
      dataIndex: 'received',
      key: 'received',
      render: (value: number) => <span className="text-green-500">{value.toLocaleString()}</span>
    },
    {
      title: 'Average Amount',
      dataIndex: 'averageAmount',
      key: 'averageAmount',
      render: (value: number) => value.toLocaleString(undefined, { maximumFractionDigits: 2 })
    },
    {
      title: 'Max Amount',
      dataIndex: 'maxAmount',
      key: 'maxAmount',
      render: (value: number) => value.toLocaleString()
    },
    {
      title: 'Min Amount',
      dataIndex: 'minAmount',
      key: 'minAmount',
      render: (value: number) => value.toLocaleString()
    },
    {
      title: 'Transactions',
      dataIndex: 'totalTransactions',
      key: 'totalTransactions'
    }
  ]

  const dailyColumns = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date'
    },
    {
      title: 'Total Amount',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (value: number) => value.toLocaleString()
    },
    {
      title: 'Transactions',
      dataIndex: 'count',
      key: 'count'
    }
  ]

  const currencyData = data
    ? Object.entries(data.currencyStats).map(([currency, stats]) => ({
        key: currency,
        currency,
        ...stats
      }))
    : []

  const dailyData = data
    ? Object.entries(data.dailyDistribution).map(([date, stats]) => ({
        key: date,
        date,
        ...stats
      }))
    : []

  return (
    <Card className="mt-4">
      <div className="mb-6">
        <RangePicker
          onChange={(_, dateStrings) => {
            setDateRange([
              dateStrings[0] ? new Date(dateStrings[0]) : undefined,
              dateStrings[1] ? new Date(dateStrings[1]) : undefined
            ])
          }}
          className="w-64"
        />
      </div>

      {data && (
        <>
          <Row gutter={16} className="mb-6">
            <Col span={8}>
              <Card>
                <Statistic title="Total Transactions" value={data.totalTransactions} />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic title="Unique Accounts" value={data.uniqueAccounts} />
              </Card>
            </Col>
          </Row>

          <div className="mb-6">
            <h3 className="mb-4 text-lg font-medium text-gray-800">Currency Statistics</h3>
            <Table
              columns={currencyColumns}
              dataSource={currencyData}
              loading={loading}
              pagination={false}
              scroll={{ x: true }}
            />
          </div>

          <div>
            <h3 className="mb-4 text-lg font-medium text-gray-800">Daily Distribution</h3>
            <Table
              columns={dailyColumns}
              dataSource={dailyData}
              loading={loading}
              pagination={{
                pageSize: 7,
                showTotal: (total) => `Total ${total} days`
              }}
            />
          </div>
        </>
      )}
    </Card>
  )
}
