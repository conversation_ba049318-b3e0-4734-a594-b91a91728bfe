export interface InitializeCashVaultData {
    pkrBalance?: number;
    usdBalance?: number;
    aedBalance?: number;
    afnBalance?: number;
    userId: string;
}

export interface CashVaultBalances {
    pkrBalance: number;
    usdBalance: number;
    aedBalance: number;
    afnBalance: number;
    lastUpdated: Date;
}

export interface CashVaultStatementEntry {
    id: string;
    date: Date;
    description: string;
    credit: number | null;
    debit: number | null;
    runningBalance: number;
    createdBy: {
        name: string;
    };
}

export interface CashVaultStatementSummary {
    totalCredits: number;
    totalDebits: number;
    net: number;
}

export interface GetCashVaultStatementParams {
    startDate: Date;
    endDate: Date;
    page?: number;
    pageSize?: number;
}

export interface CashVaultStatement {
    currencyCode: string;
    startDate: Date;
    endDate: Date;
    entries: CashVaultStatementEntry[];
    openingBalance: number;
    closingBalance: number;
    summary: CashVaultStatementSummary;
    pagination: {
        total: number;
        pages: number;
        currentPage: number;
        pageSize: number;
    };
}