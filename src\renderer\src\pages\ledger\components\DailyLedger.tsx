import { useEffect, useState } from 'react'
import {
  Card,
  DatePicker,
  Table,
  Space,
  Typography,
  Statistic,
  Row,
  Col,
  Tag,
  message,
  Button,
  Tooltip
} from 'antd'
import { FaArrowUp, FaArrowDown, FaFilePdf } from 'react-icons/fa'
import { ledgerApi } from '@/renderer/services'
import { formatCurrency } from '@/renderer/utils'
import { PDFConfirmationModal } from '@/renderer/components'
import { handleDailyLedgerPDF } from '../utils'
import dayjs from 'dayjs'

const { Title } = Typography

export const DailyLedger = () => {
  const [loading, setLoading] = useState(false)
  const [selectedDate, setSelectedDate] = useState<dayjs.Dayjs>(dayjs())
  const [data, setData] = useState<any>({
    entries: [],
    groupedEntries: {},
    totals: {}
  })
  const [isPDFModalOpen, setIsPDFModalOpen] = useState(false)
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)

  const fetchDailyLedger = async () => {
    setLoading(true)
    const response = await ledgerApi.getDailyLedger({ date: selectedDate.toDate() })
    setLoading(false)

    console.log('daily ledger response', response)
    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }
    setData(response.data.data)
  }

  const handleDateChange = async (date: dayjs.Dayjs | null) => {
    if (!date) return
    setSelectedDate(date)
    fetchDailyLedger()
  }

  const handlePDFAction = async (action: 'save' | 'print') => {
    try {
      setIsGeneratingPDF(true)
      await handleDailyLedgerPDF(data, selectedDate.toDate(), action)
      message.success(`PDF ${action === 'save' ? 'saved' : 'opened for printing'} successfully`)
    } catch (error) {
      console.error(`Failed to ${action} PDF:`, error)
      message.error(`Failed to ${action} PDF. Please try again.`)
    } finally {
      setIsGeneratingPDF(false)
      setIsPDFModalOpen(false)
    }
  }

  useEffect(() => {
    fetchDailyLedger()
  }, [selectedDate])

  const getColorOnTransactionType = (transactionType: string) => {
    let color = '#6B7280' // default neutral color (gray-500)
    if (transactionType === 'SALE') color = 'green' // positive income (emerald-500)
    if (transactionType === 'PAYMENT') color = 'magenta' // incoming payment (sky-500)
    if (transactionType === 'EXPENSE') color = 'red' // money going out (rose-600)
    if (transactionType === 'CURRENCY_EXCHANGE') color = 'purple' // currency conversion (violet-500)
    if (transactionType === 'TRANSFER') color = 'gold' // internal movement (amber-500)
    if (transactionType === 'RENT') color = 'yellow' // recurring income (yellow-600)
    if (transactionType === 'DEPOSIT') color = 'geekblue' // money coming in (teal-500)
    if (transactionType === 'CURRENCY_DEPOSIT') color = 'lime' // foreign currency in (teal-600)
    if (transactionType === 'WITHDRAWAL') color = 'red' // money going out (red-500)
    if (transactionType === 'CURRENCY_WITHDRAWAL') color = 'magenta' // foreign currency out (red-600)
    if (transactionType === 'OPENING_BALANCE') color = 'cyan' // initial entry (blue-600)
    if (transactionType === 'OTHER') color = 'gray' // miscellaneous (gray-400)
    return color
  }

  const columns = [
    {
      title: 'S.#',
      key: 'serialNumber',
      width: 70,
      render: (_: any, __: any, index: number) => index + 1
    },
    {
      title: 'Time',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => dayjs(date).format('HH:mm:ss')
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      width: 300,
      render: (description: string) => {
        const maxLength = 70 // You can adjust this number to show more/less characters
        const displayText =
          description.length > maxLength ? `${description.slice(0, maxLength)}...` : description

        return (
          <Tooltip title={description}>
            <span>{displayText}</span>
          </Tooltip>
        )
      }
    },
    {
      title: 'Category',
      dataIndex: 'transactionType',
      key: 'category',
      render: (category: string) => (
        <Tag color={getColorOnTransactionType(category)}>{category.replace(/_/g, ' ')}</Tag>
      )
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => <Tag color={type === 'CREDIT' ? 'green' : 'red'}>{type}</Tag>
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number, record: any) => formatCurrency(amount, record.currency.code)
    },
    {
      title: 'Account',
      dataIndex: ['account', 'name'],
      key: 'account'
    }
  ]

  return (
    <Card>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Space wrap align="center" style={{ justifyContent: 'space-between', width: '100%' }}>
          <Space>
            <DatePicker
              value={selectedDate}
              onChange={handleDateChange}
              style={{ width: 200 }}
              allowClear={false}
            />
            <Button
              icon={<FaFilePdf />}
              onClick={() => setIsPDFModalOpen(true)}
              disabled={!data.entries.length}
            >
              Generate PDF
            </Button>
          </Space>
        </Space>

        {/* Currency Summaries */}
        <Row gutter={[16, 16]}>
          {Object.entries(data.totals).map(([currency, totals]: [string, any]) => (
            <Col key={currency} xs={24} sm={12} md={8} lg={6}>
              <Card size="small">
                <Title level={5} style={{ marginTop: 0 }}>
                  {currency}
                </Title>
                <Space direction="vertical" size="small">
                  <Statistic
                    title="Credits"
                    value={totals.credits}
                    precision={2}
                    prefix={<FaArrowUp style={{ color: '#52c41a' }} />}
                    valueStyle={{ color: '#52c41a' }}
                  />
                  <Statistic
                    title="Debits"
                    value={totals.debits}
                    precision={2}
                    prefix={<FaArrowDown style={{ color: '#f5222d' }} />}
                    valueStyle={{ color: '#f5222d' }}
                  />
                  <Statistic
                    title="Net"
                    value={totals.net}
                    precision={2}
                    valueStyle={{ color: totals.net >= 0 ? '#52c41a' : '#f5222d' }}
                  />
                </Space>
              </Card>
            </Col>
          ))}
        </Row>

        <Table
          columns={columns}
          dataSource={data.entries}
          loading={loading}
          rowKey="id"
          virtual
          sticky
          size="small"
          pagination={{
            position: ['topRight'],
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
          }}
        />

        <PDFConfirmationModal
          isOpen={isPDFModalOpen}
          onClose={() => setIsPDFModalOpen(false)}
          onSave={() => handlePDFAction('save')}
          onPrint={() => handlePDFAction('print')}
          title="Daily Ledger PDF"
        />
      </Space>
    </Card>
  )
}
