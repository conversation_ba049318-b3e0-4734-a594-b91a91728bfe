import { useEffect, useState } from 'react'
import { DatePicker, Form, InputNumber, message, Modal, Select } from 'antd'
import type { CreateRentedPropertyData, Property } from '@/common/types'
import { propertyApi } from '@/renderer/services'
import dayjs from 'dayjs'
import { useAccountContext } from '@/renderer/contexts'

interface RentPropertyFormProps {
  open: boolean
  loading?: boolean
  propertyId?: string
  onCancel: () => void
  onSubmit: (data: CreateRentedPropertyData) => Promise<void>
}

export const RentPropertyForm = ({
  open,
  loading,
  propertyId,
  onCancel,
  onSubmit
}: RentPropertyFormProps) => {
  const [form] = Form.useForm()
  const [property, setProperty] = useState<Property | null>(null)
  const { tenants } = useAccountContext()

  // Fetch property details when propertyId changes
  useEffect(() => {
    if (propertyId) {
      propertyApi.getProperty(propertyId).then((response) => {
        if (!response.error.error && !response.data.error) {
          setProperty(response.data.data)
        } else {
          message.error(response.error.message || response.data.error.message)
        }
      })
    }
  }, [propertyId])

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      await onSubmit({
        ...values,
        propertyId: propertyId!,
        startDate: values.startDate.toDate(),
        endDate: values.endDate?.toDate()
      })
      form.resetFields()
    } catch (error) {
      // Form validation error, no need to handle
    }
  }

  return (
    <Modal
      title={`Rent Property${property ? `: ${property.name}` : ''}`}
      open={open}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={600}
    >
      <Form form={form} layout="vertical" className="mt-4">
        <Form.Item
          name="tenantId"
          label="Tenant"
          rules={[{ required: true, message: 'Please select tenant' }]}
        >
          <Select
            showSearch
            placeholder="Select tenant"
            options={tenants}
            optionFilterProp="label"
          />
        </Form.Item>

        <Form.Item
          name="startDate"
          label="Start Date"
          rules={[{ required: true, message: 'Please select start date' }]}
        >
          <DatePicker className="w-full" />
        </Form.Item>

        <Form.Item name="endDate" label="End Date">
          <DatePicker
            className="w-full"
            disabledDate={(date) =>
              date.isBefore(form.getFieldValue('startDate')?.add(1, 'month') || dayjs(), 'day')
            }
          />
        </Form.Item>

        <Form.Item
          name="monthlyRent"
          label="Monthly Rent"
          rules={[
            { required: true, message: 'Please enter monthly rent' },
            { type: 'number', min: 0, message: 'Rent must be positive' }
          ]}
        >
          <InputNumber
            className="w-full"
            placeholder="Enter monthly rent"
            formatter={(value) => `PKR ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={(value) => value!.replace(/PKR\s?|(,*)/g, '')}
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}
