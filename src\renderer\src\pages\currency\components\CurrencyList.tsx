import { Row, Col, Card, Statistic, Select, DatePicker, Spin } from 'antd'
import { useEffect, useState } from 'react'
import { useApi } from '@/renderer/hooks'
import { currencyApi } from '@/renderer/services'
import type { Currency, CurrencySummaryResponse } from '@/common/types'
import { formatCurrency } from '@/renderer/utils'
import dayjs from 'dayjs'

const { RangePicker } = DatePicker

export const CurrencyList = () => {
  const [selectedCurrency, setSelectedCurrency] = useState<string>('PKR')
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null]>([null, null])

  const {
    data: currencies,
    isLoading: loadingCurrencies,
    request: fetchCurrencies
  } = useApi<Currency[], []>(currencyApi.getAllCurrencies)

  const {
    data: summary,
    isLoading: loadingSummary,
    request: fetchSummary
  } = useApi<CurrencySummaryResponse, [string, string, string]>(currencyApi.getCurrencySummary)

  useEffect(() => {
    fetchCurrencies()
  }, [])

  useEffect(() => {
    if (dateRange && dateRange[0] && dateRange[1]) {
      fetchSummary(
        selectedCurrency,
        dateRange[0].toDate().toISOString(),
        dateRange[1].toDate().toISOString()
      )
    }
  }, [selectedCurrency, dateRange])

  if (loadingCurrencies) return <Spin />

  return (
    <div>
      <div className="mb-6 flex gap-4">
        <Select value={selectedCurrency} onChange={setSelectedCurrency} style={{ width: 200 }}>
          {currencies?.map((currency) => (
            <Select.Option key={currency.code} value={currency.code}>
              {currency.name} ({currency.code})
            </Select.Option>
          ))}
        </Select>

        <RangePicker
          value={dateRange}
          allowClear
          onChange={(dates) => {
            setDateRange(dates as [dayjs.Dayjs | null, dayjs.Dayjs | null])
          }}
        />
      </div>

      <Row gutter={16}>
        <Col span={8}>
          <Card size="small">
            <Statistic
              title="Total Inflow"
              value={summary?.inflow ?? 0}
              loading={loadingSummary}
              precision={2}
              formatter={(value) => formatCurrency(value as number, selectedCurrency)}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card size="small">
            <Statistic
              title="Total Outflow"
              value={summary?.outflow ?? 0}
              loading={loadingSummary}
              precision={2}
              formatter={(value) => formatCurrency(value as number, selectedCurrency)}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card size="small">
            <Statistic
              title="Net Flow"
              value={summary?.netFlow ?? 0}
              loading={loadingSummary}
              precision={2}
              valueStyle={{ color: (summary?.netFlow ?? 0) >= 0 ? 'green' : 'red' }}
              formatter={(value) => formatCurrency(value as number, selectedCurrency)}
            />
          </Card>
        </Col>
      </Row>
    </div>
  )
}
