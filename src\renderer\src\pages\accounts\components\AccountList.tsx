import { Table, Button, Popconfirm, message, Tag, Tooltip } from 'antd'
import { FiTrash2, FiEye } from 'react-icons/fi'
import { useState, useEffect } from 'react'
import { useApi } from '@/renderer/hooks'
import { accountsApi } from '@/renderer/services'
import { AccountDetailsModal } from './AccountDetailsModal'
import { formatDate, formatCurrency } from '@/renderer/utils'
import { GetAccountsParams, GetAccountsResponse } from '@/common/types'
import { useAccountContext } from '@/renderer/contexts'

type AccountType = 'CUSTOMER' | 'TENANT' | 'BOTH'

interface AccountListProps {
  type: AccountType
  searchQuery: string
  refreshTrigger: number
}

export const AccountList = ({ type, searchQuery, refreshTrigger }: AccountListProps) => {
  const [selectedAccount, setSelectedAccount] = useState<string | null>(null)
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  const { refreshAccounts } = useAccountContext()

  const {
    data,
    isLoading,
    request: fetchAccounts,
    error,
    errorMessage
  } = useApi<GetAccountsResponse, [GetAccountsParams]>(accountsApi.getAccounts)

  useEffect(() => {
    fetchAccounts({ page, limit: pageSize, type, search: searchQuery })
  }, [page, pageSize, type, searchQuery])

  // console.log(data)
  // console.log(error)
  // console.log(errorMessage)

  const handleDelete = async (id: string) => {
    const response = await accountsApi.deleteAccount(id)
    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }
    message.success('Account deleted successfully')
    fetchAccounts({ page, limit: pageSize, type, search: searchQuery })
    refreshAccounts()
  }

  useEffect(() => {
    fetchAccounts({ page, limit: pageSize, type, search: searchQuery })
  }, [refreshTrigger])

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Phone',
      dataIndex: 'phoneNumber',
      key: 'phoneNumber'
    },
    {
      title: 'Balances',
      dataIndex: 'balances',
      key: 'balances',
      render: (balances: any[]) => {
        const primaryBalance = balances ? balances[0] : null
        const hasMoreBalances = balances?.length > 1

        return (
          <div className="flex items-center gap-2">
            <Tag color={primaryBalance?.balance >= 0 ? 'green' : 'red'} className="text-xs">
              {formatCurrency(primaryBalance?.balance, primaryBalance?.currency.code)}
            </Tag>
            {hasMoreBalances && (
              <Tooltip
                title={
                  <div className="space-y-1">
                    {balances?.slice(1).map((balance) => (
                      <div key={balance?.currency.code}>
                        <Tag color={balance?.balance >= 0 ? 'green' : 'red'} className="text-xs">
                          {formatCurrency(balance?.balance, balance?.currency.code)}
                        </Tag>
                      </div>
                    ))}
                  </div>
                }
              >
                <Tag className="text-xs">+{balances?.length - 1} more</Tag>
              </Tooltip>
            )}
          </div>
        )
      }
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => formatDate(date)
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record: any) => (
        <div className="flex gap-2">
          <Button type="text" icon={<FiEye />} onClick={() => setSelectedAccount(record.id)} />
          <Popconfirm
            title="Delete Account"
            description="Are you sure you want to delete this account?"
            onConfirm={() => handleDelete(record.id)}
          >
            <Button type="text" danger icon={<FiTrash2 />} />
          </Popconfirm>
        </div>
      )
    }
  ]

  return (
    <>
      <Table
        columns={columns}
        dataSource={data?.accounts}
        loading={isLoading}
        rowKey="id"
        virtual
        sticky
        pagination={{
          current: page,
          pageSize,
          total: data?.total,
          position: ['topRight'],
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '50', '100'],
          showQuickJumper: true,
          onChange: (page, pageSize) => {
            setPage(page)
            setPageSize(pageSize)
          }
        }}
      />

      <AccountDetailsModal accountId={selectedAccount} onClose={() => setSelectedAccount(null)} />
    </>
  )
}
