import { Card, Statistic, Row, Col, Spin } from 'antd'
import { useApi } from '@/renderer/hooks'
import { stockApi } from '@/renderer/services'
import type { GetStockParams, StockSummaryResponse } from '@/common/types'
import { useEffect } from 'react'
import { useTheme } from '@/renderer/contexts'

interface StockSummaryProps {
  className?: string
}

interface StockCardProps {
  title: string
  availableValue: number
  soldValue: number
}

const StockCard = ({ title, availableValue, soldValue }: StockCardProps) => {
  const { isDarkMode } = useTheme()

  return (
    <Col span={6}>
      <Card
        title={title}
        size="small"
        className={`shadow-large bg-[length:200%_200%] bg-[10%_10%] transition-[background-position] duration-500 ease-in-out hover:bg-[90%_90%] ${
          isDarkMode
            ? 'bg-gradient-to-br from-blue-950 via-slate-950 to-blue-900'
            : 'bg-gradient-to-br from-sky-200 via-white to-blue-200'
        }`}
      >
        <Row gutter={[8, 8]}>
          <Col span={12}>
            <Statistic title="Available" value={availableValue} />
          </Col>
          <Col span={12}>
            <Statistic title="Sold" value={soldValue} />
          </Col>
        </Row>
      </Card>
    </Col>
  )
}

export const StockSummary = ({ className }: StockSummaryProps) => {
  const {
    data: summary,
    isLoading,
    request: stockSummaryRequest
  } = useApi<StockSummaryResponse, []>(stockApi.getStockSummary)

  const getCountByStatus = (
    items: Array<{ status: string; _count: { _all: number } }>,
    status: string
  ) => {
    return items?.find((i) => i.status === status)?._count._all || 0
  }

  const getQuantitySummary = (
    items: Array<{
      status: string
      _count: { _all: number }
      _sum: { total: number; remaining: number; sold: number }
    }>,
    status: string
  ) => {
    const item = items?.find((i) => i.status === status)
    return {
      total: item?._sum.total || 0,
      remaining: item?._sum.remaining || 0,
      sold: item?._sum.sold || 0
    }
  }

  useEffect(() => {
    stockSummaryRequest()
  }, [])

  if (isLoading) return <Spin />

  return (
    <div className={className}>
      <Row gutter={16}>
        <StockCard
          title="Cars"
          availableValue={getCountByStatus(summary?.cars || [], 'AVAILABLE')}
          soldValue={getCountByStatus(summary?.cars || [], 'SOLD')}
        />

        <StockCard
          title="Car Parts"
          availableValue={getQuantitySummary(summary?.carParts || [], 'AVAILABLE').remaining}
          soldValue={getQuantitySummary(summary?.carParts || [], 'AVAILABLE').sold}
        />

        <StockCard
          title="Electronics"
          availableValue={getQuantitySummary(summary?.electronics || [], 'AVAILABLE').remaining}
          soldValue={getQuantitySummary(summary?.electronics || [], 'AVAILABLE').sold}
        />

        <StockCard
          title="Scraps"
          availableValue={getQuantitySummary(summary?.scraps || [], 'AVAILABLE').remaining}
          soldValue={getQuantitySummary(summary?.scraps || [], 'AVAILABLE').sold}
        />
      </Row>
    </div>
  )
}
