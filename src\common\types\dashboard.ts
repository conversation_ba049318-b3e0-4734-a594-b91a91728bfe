export type TimeRange = '1D' | '7D' | '14D' | '30D'

// Cash Overview Types
export interface DashboardCurrencyBalance {
    pkr: number
    usd: number
    aed: number
    afn: number
}

export interface CashOverview {
    smallCounter: DashboardCurrencyBalance
    cashVault: DashboardCurrencyBalance
    bankAccounts: number
    totalPKR: number
    totalByLocation: {
        smallCounter: number
        cashVault: number
        bankAccounts: number
    }
}

// Bank Account Types
export interface BankAccountSummary {
    id: string
    accountNumber: string
    bankName: string
    balance: number
}

// Transaction Stats Types
export interface TransactionStats {
    totalTransactions: number
    creditCount: number
    debitCount: number
    volumeByType: Record<string, number>
}

// Account Types
export interface AccountStats {
    customers: number
    tenants: number
    both: number
    total: number
}

// Inventory Types
export interface InventorySummary {
    cars: number
    parts: number
    electronics: number
    scrap: number
    totalItems: number
}

// Currency Transaction Summary
export interface CurrencyTransactionSummary {
    currency: string
    credits: number
    debits: number
    transactionCount: number
    net: number
}

// High Value Transactions
export interface HighValueTransaction {
    id: string
    date: Date
    amount: number
    type: string
    currency: string
    description: string
}

// Property Status
export interface PropertyStatusSummary {
    type: string
    total: number
    rented: number
    available: number
    rentRevenue: number
}

// Transaction Trends
export interface TransactionTrend {
    date: string
    credits: number
    debits: number
    netAmount: number
    transactionCount: number
}

// Currency Exchange Summary
export interface ExchangeSummary {
    fromCurrency: string
    toCurrency: string
    rate: number
    volume: number
    lastUpdated: Date
}

// Location Transfer Summary
export interface LocationTransferSummary {
    location: string
    inflow: Record<string, number>
    outflow: Record<string, number>
    net: Record<string, number>
}

// Sales Summary
export interface ItemSaleSummary {
    quantity: number
    amount: number
}

export interface SalesTrendData {
    date: string
    walkIn: {
        quantity: number
        amount: number
    }
    registered: {
        quantity: number
        amount: number
    }
    total: {
        quantity: number
        amount: number
    }
    byType: {
        cars: {
            quantity: number
            amount: number
        }
        parts: {
            quantity: number
            amount: number
        }
        electronics: {
            quantity: number
            amount: number
        }
        scrap: {
            quantity: number
            amount: number
        }
    }
}

// Container Performance
export interface ContainerPerformance {
    containerNumber: string
    totalCost: number
    soldItems: number
    revenue: number
    profit: number
}

// Sales Category Performance
export interface SalesCategoryPerformance {
    category: string
    quantity: number
    revenue: number
    profitMargin: number
}

// UI Element Hints (for frontend reference)
export enum DashboardUIElement {
    CARD = 'card',
    CARD_GRID = 'card-grid',
    TABLE = 'table',
    LINE_CHART = 'line-chart',
    BAR_CHART = 'bar-chart',
    PIE_CHART = 'pie-chart',
    STATISTIC = 'statistic',
    LIST = 'list',
    PROGRESS = 'progress'
}

// Account Balance Summary
export interface AccountBalanceSummary {
    currency: string
    receivables: number
    payables: number
    net: number
}
