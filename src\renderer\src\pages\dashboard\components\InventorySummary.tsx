import { useEffect } from 'react'
import { Typography, Space, Button, Alert, Skeleton, Row, Col, Statistic, Progress } from 'antd'
import { useApi } from '@/renderer/hooks'
import { dashboardApi } from '@/renderer/services'
import { InventorySummary as InventorySummaryType } from '@/common/types'
import { FaBoxes, FaSync } from 'react-icons/fa'
import { useTheme } from '@/renderer/contexts'

const { Title } = Typography

const InventorySummary = () => {
  const { isDarkMode } = useTheme()

  const {
    data,
    isLoading,
    error,
    request: fetchInventorySummary
  } = useApi<InventorySummaryType, []>(dashboardApi.getInventorySummary)

  useEffect(() => {
    fetchInventorySummary()
  }, [])

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <FaBoxes className="text-2xl text-yellow-600" />
            <Title level={5} className="!mb-0">
              Inventory Summary
            </Title>
          </Space>
          <Button icon={<FaSync />} loading />
        </Space>
        <Skeleton active paragraph={{ rows: 4 }} />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <FaBoxes className="text-2xl text-yellow-600" />
            <Title level={5} className="!mb-0">
              Inventory Summary
            </Title>
          </Space>
          <Button icon={<FaSync />} onClick={fetchInventorySummary} />
        </Space>
        <Alert
          message="Error"
          description="Failed to load inventory summary"
          type="error"
          showIcon
        />
      </div>
    )
  }

  if (!data) return null

  return (
    <div className="flex flex-col gap-4">
      <Space className="flex justify-between">
        <Space>
          <FaBoxes className="text-2xl text-yellow-600" />
          <Title level={5} className="!mb-0">
            Inventory Summary
          </Title>
        </Space>
        <Button icon={<FaSync />} onClick={fetchInventorySummary} />
      </Space>

      <div className={`rounded-lg p-4 ${isDarkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Statistic
              title="Total Items"
              value={data.totalItems}
              formatter={(value) => value?.toLocaleString()}
            />
          </Col>
        </Row>

        <Title level={5} className="mb-2 mt-4">
          Category Breakdown
        </Title>
        <Row gutter={[16, 16]}>
          <Col span={12} sm={6}>
            <div className="mb-2">
              <Title level={5} className="mb-1">
                Cars
              </Title>
              <Statistic value={data.cars} />
              <Progress
                percent={Math.round((data.cars / data.totalItems) * 100)}
                size="small"
                status="active"
              />
            </div>
          </Col>
          <Col span={12} sm={6}>
            <div className="mb-2">
              <Title level={5} className="mb-1">
                Parts
              </Title>
              <Statistic value={data.parts} />
              <Progress
                percent={Math.round((data.parts / data.totalItems) * 100)}
                size="small"
                status="active"
              />
            </div>
          </Col>
          <Col span={12} sm={6}>
            <div className="mb-2">
              <Title level={5} className="mb-1">
                Electronics
              </Title>
              <Statistic value={data.electronics} />
              <Progress
                percent={Math.round((data.electronics / data.totalItems) * 100)}
                size="small"
                status="active"
              />
            </div>
          </Col>
          <Col span={12} sm={6}>
            <div className="mb-2">
              <Title level={5} className="mb-1">
                Scrap
              </Title>
              <Statistic value={data.scrap} />
              <Progress
                percent={Math.round((data.scrap / data.totalItems) * 100)}
                size="small"
                status="active"
              />
            </div>
          </Col>
        </Row>
      </div>
    </div>
  )
}

export default InventorySummary
