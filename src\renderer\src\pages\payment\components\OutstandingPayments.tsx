import { useState, useEffect } from 'react'
import { Card, Table, Button, Tooltip, App } from 'antd'
import { DollarOutlined } from '@ant-design/icons'
import type { OutstandingPayment } from '@/common/types'
import { paymentApi } from '@/renderer/services'
import { formatDate } from '@/renderer/utils'

export const OutstandingPayments = () => {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<OutstandingPayment[]>([])

  const { message } = App.useApp()

  const fetchOutstandingPayments = async () => {
    setLoading(true)
    const response = await paymentApi.getOutstandingPayments()
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    setData(response.data.data)
  }

  useEffect(() => {
    fetchOutstandingPayments()
  }, [])

  const columns = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => formatDate(date)
    },
    {
      title: 'Account',
      dataIndex: 'accountName',
      key: 'accountName'
    },
    {
      title: 'Total Amount',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount: number) => amount.toLocaleString()
    },
    {
      title: 'Amount Paid',
      dataIndex: 'amountPaid',
      key: 'amountPaid',
      render: (amount: number) => <span className="text-green-500">{amount.toLocaleString()}</span>
    },
    {
      title: 'Outstanding Amount',
      dataIndex: 'outstandingAmount',
      key: 'outstandingAmount',
      render: (amount: number) => (
        <span className="font-medium text-red-500">{amount.toLocaleString()}</span>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: OutstandingPayment) => (
        <Tooltip title="Create Payment">
          <Button
            type="primary"
            icon={<DollarOutlined />}
            onClick={() => {
              // TODO: Implement create payment for outstanding amount
              console.log('Create payment for:', record)
            }}
          >
            Pay
          </Button>
        </Tooltip>
      )
    }
  ]

  const totalOutstanding = data.reduce((sum, payment) => sum + payment.outstandingAmount, 0)

  return (
    <Card className="mt-4">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-800">
          Total Outstanding Amount:{' '}
          <span className="text-red-500">{totalOutstanding.toLocaleString()}</span>
        </h3>
      </div>

      <Table
        columns={columns}
        dataSource={data}
        loading={loading}
        rowKey="saleId"
        pagination={{
          pageSize: 10,
          showTotal: (total) => `Total ${total} outstanding payments`
        }}
      />
    </Card>
  )
}
