import jsPD<PERSON> from 'jspdf';
import 'jspdf-autotable';

export const savePDF = async (doc: jsPDF, filename: string): Promise<void> => {
    try {
        // Show save dialog
        const path = await window.electron.ipcRenderer.invoke('show-save-dialog', {
            title: 'Save PDF',
            defaultPath: `${filename}.pdf`,
            filters: [{ name: 'PDF Files', extensions: ['pdf'] }]
        });

        // If user cancels the save dialog
        if (!path) {
            return;
        }

        // Save the PDF
        await window.electron.ipcRenderer.invoke('save-pdf', {
            path,
            data: doc.output('arraybuffer')
        });
    } catch (error) {
        console.error('Error saving PDF:', error);
        throw error;
    }
};

export const printPDF = async (doc: jsPDF): Promise<void> => {
    try {
        const dataUri = doc.output('datauristring');
        await window.electron.ipcRenderer.invoke('print-pdf', dataUri);
    } catch (error) {
        console.error('Error printing PDF:', error);
        throw error;
    }
};

// A5 page configuration for consistent use across all PDF generators
export const A5_CONFIG = {
    format: 'a5' as const,
    unit: 'mm' as const,
    orientation: 'portrait' as const
};

// Common PDF styling configurations
export const PDF_STYLES = {
    header: {
        fontSize: 14,
        titleFontSize: 10,
        margin: 8
    },
    table: {
        styles: {
            fontSize: 8,
            cellPadding: 1,
            lineColor: [0, 0, 0],
            lineWidth: 0.1,
            fillColor: [255, 255, 255], // Ensure white background for all cells
            textColor: [0, 0, 0] // Ensure black text for all cells by default
        },
        headStyles: {
            fillColor: [240, 240, 240], // Light gray background for headers
            textColor: [0, 0, 0], // Black text for headers
            fontStyle: 'bold',
            lineColor: [0, 0, 0],
            lineWidth: 0.1
        },
        footStyles: {
            fillColor: [255, 255, 255], // White background for footers
            textColor: [0, 0, 0], // Black text for footers
            fontStyle: 'bold',
            lineWidth: 0.1,
            lineColor: [0, 0, 0]
        }
    },
    footer: {
        fontSize: 6,
        margin: 5
    }
};