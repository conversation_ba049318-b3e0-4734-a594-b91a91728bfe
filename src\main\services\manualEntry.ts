import { prisma } from '../db';
import {
    CreateManualEntryData,
    GetManualEntriesParams,
    VoidManualEntryParams,
    ManualEntriesResponse,
    CreateManualEntryResult,
    VoidManualEntryResult,
    ManualEntryItem,
    ManualEntryStatus,
    ManualEntrySortOrder
} from '@/common/types/manualEntry';
import { TransactionType, ManualEntryTarget, TransactionLocation, TransactionCategory } from '@prisma/client';

class ManualEntryService {
    async createManualEntry(data: CreateManualEntryData): Promise<CreateManualEntryResult> {
        // Validate that exactly one target is specified
        const targetCount = [data.accountId, data.bankAccountId, data.targetType].filter(x => x !== undefined && x !== null).length;
        if (targetCount !== 1) {
            throw new Error('Exactly one target (account, bank account, or target type) must be specified');
        }

        // Validate amount
        if (data.amount <= 0) {
            throw new Error('Amount must be greater than zero');
        }

        return await prisma.$transaction(async (tx) => {
            // Get currency
            const currency = await tx.currency.findUnique({
                where: { code: data.currencyCode }
            });

            if (!currency) {
                throw new Error(`Currency ${data.currencyCode} not found`);
            }

            let sourceType: TransactionLocation = TransactionLocation.EXTERNAL;
            let destinationType: TransactionLocation = TransactionLocation.EXTERNAL;
            let description = data.description || 'Manual entry';

            // Determine transaction flow and update balances based on target
            if (data.accountId) {
                // Manual entry for account
                const account = await tx.account.findUnique({
                    where: { id: data.accountId },
                    select: { id: true, name: true, type: true }
                });

                if (!account) {
                    throw new Error('Account not found');
                }

                description = `Manual ${data.entryType.toLowerCase()} entry for ${account.name}`;

                // For CREDIT: Money goes TO account (External -> Account)
                // For DEBIT: Money comes FROM account (Account -> External)
                if (data.entryType === TransactionType.CREDIT) {
                    sourceType = TransactionLocation.EXTERNAL;
                    destinationType = TransactionLocation.ACCOUNT;
                } else {
                    sourceType = TransactionLocation.ACCOUNT;
                    destinationType = TransactionLocation.EXTERNAL;
                }

                // Update account balance
                // CREDIT = Increase balance (add money)
                // DEBIT = Decrease balance (subtract money)
                const accountBalance = await tx.accountBalance.findUnique({
                    where: {
                        accountId_currencyId: {
                            accountId: data.accountId,
                            currencyId: currency.id
                        }
                    }
                });

                if (!accountBalance) {
                    // Create new balance record if it doesn't exist
                    await tx.accountBalance.create({
                        data: {
                            accountId: data.accountId,
                            currencyId: currency.id,
                            balance: data.entryType === TransactionType.CREDIT ? data.amount : -data.amount
                        }
                    });
                } else {
                    // Update existing balance
                    const balanceChange = data.entryType === TransactionType.CREDIT ? data.amount : -data.amount;
                    await tx.accountBalance.update({
                        where: {
                            accountId_currencyId: {
                                accountId: data.accountId,
                                currencyId: currency.id
                            }
                        },
                        data: {
                            balance: {
                                increment: balanceChange
                            }
                        }
                    });
                }

            } else if (data.bankAccountId) {
                // Manual entry for bank account
                const bankAccount = await tx.bankAccount.findUnique({
                    where: { id: data.bankAccountId },
                    select: { id: true, bankName: true, accountNumber: true, balance: true, isActive: true }
                });

                if (!bankAccount) {
                    throw new Error('Bank account not found');
                }

                if (!bankAccount.isActive) {
                    throw new Error('Bank account is not active');
                }

                // Bank accounts only support PKR
                if (data.currencyCode !== 'PKR') {
                    throw new Error('Bank accounts only support PKR currency');
                }

                description = `Manual ${data.entryType.toLowerCase()} entry for ${bankAccount.bankName} (${bankAccount.accountNumber})`;

                // For CREDIT: Money goes TO bank (External -> Bank)
                // For DEBIT: Money comes FROM bank (Bank -> External)
                if (data.entryType === TransactionType.CREDIT) {
                    sourceType = TransactionLocation.EXTERNAL;
                    destinationType = TransactionLocation.BANK_ACCOUNT;
                } else {
                    sourceType = TransactionLocation.BANK_ACCOUNT;
                    destinationType = TransactionLocation.EXTERNAL;
                }

                // Update bank balance
                // CREDIT = Increase balance (add money)
                // DEBIT = Decrease balance (subtract money)
                const balanceChange = data.entryType === TransactionType.CREDIT ? data.amount : -data.amount;
                await tx.bankAccount.update({
                    where: { id: data.bankAccountId },
                    data: {
                        balance: {
                            increment: balanceChange
                        }
                    }
                });

            } else if (data.targetType === ManualEntryTarget.CASH_VAULT) {
                // Manual entry for cash vault
                const vault = await tx.cashVault.findFirst();

                if (!vault) {
                    throw new Error('Cash vault not found');
                }

                description = `Manual ${data.entryType.toLowerCase()} entry for Cash Vault (${data.currencyCode})`;

                // For CREDIT: Money goes TO vault (External -> Vault)
                // For DEBIT: Money comes FROM vault (Vault -> External)
                if (data.entryType === TransactionType.CREDIT) {
                    sourceType = TransactionLocation.EXTERNAL;
                    destinationType = TransactionLocation.CASH_VAULT;
                } else {
                    sourceType = TransactionLocation.CASH_VAULT;
                    destinationType = TransactionLocation.EXTERNAL;
                }

                // Update vault balance for the specific currency
                // CREDIT = Increase balance (add money)
                // DEBIT = Decrease balance (subtract money)
                const balanceChange = data.entryType === TransactionType.CREDIT ? data.amount : -data.amount;
                const updateData = this.getVaultUpdateData(data.currencyCode, balanceChange);

                await tx.cashVault.update({
                    where: { id: vault.id },
                    data: updateData
                });
            }

            // Create ledger entry
            const ledgerEntry = await tx.ledgerEntry.create({
                data: {
                    amount: data.amount,
                    type: data.entryType,
                    description,
                    date: data.transactionDate,
                    currencyId: currency.id,
                    sourceType,
                    destinationType,
                    transactionType: TransactionCategory.MANUAL_ENTRY,
                    accountId: data.accountId,
                    bankAccountId: data.bankAccountId,
                    createdById: data.createdById
                }
            });

            // Create manual entry
            const manualEntry = await tx.manualEntry.create({
                data: {
                    amount: data.amount,
                    description: data.description,
                    transactionDate: data.transactionDate,
                    entryType: data.entryType,
                    accountId: data.accountId,
                    bankAccountId: data.bankAccountId,
                    targetType: data.targetType,
                    currencyId: currency.id,
                    ledgerEntryId: ledgerEntry.id,
                    createdById: data.createdById
                },
                include: {
                    account: {
                        select: { id: true, name: true, type: true }
                    },
                    bankAccount: {
                        select: { id: true, bankName: true, accountNumber: true }
                    },
                    currency: {
                        select: { id: true, code: true, name: true }
                    },
                    ledgerEntry: {
                        select: { id: true, sourceType: true, destinationType: true, transactionType: true }
                    },
                    createdBy: {
                        select: { id: true, name: true }
                    }
                }
            });

            return {
                manualEntry: manualEntry as ManualEntryItem,
                message: 'Manual entry created successfully'
            };
        });
    }

    private getVaultUpdateData(currencyCode: string, amount: number) {
        switch (currencyCode) {
            case 'PKR':
                return { pkrBalance: { increment: amount } };
            case 'USD':
                return { usdBalance: { increment: amount } };
            case 'AED':
                return { aedBalance: { increment: amount } };
            case 'AFN':
                return { afnBalance: { increment: amount } };
            default:
                throw new Error(`Unsupported currency: ${currencyCode}`);
        }
    }

    async getManualEntries(params: GetManualEntriesParams): Promise<ManualEntriesResponse> {
        const {
            page = 1,
            pageSize = 20,
            startDate,
            endDate,
            entryType,
            targetType,
            accountId,
            bankAccountId,
            currencyCode,
            status = ManualEntryStatus.ACTIVE,
            sortOrder = ManualEntrySortOrder.OLDEST_FIRST,
            search
        } = params;

        const skip = (page - 1) * pageSize;

        // Build where clause
        let whereClause: any = {};

        // Status filtering
        if (status === ManualEntryStatus.ACTIVE) {
            whereClause.isDeleted = false;
        } else if (status === ManualEntryStatus.DELETED) {
            whereClause.isDeleted = true;
        }
        // For ALL status, don't add isDeleted filter

        // Date filtering
        if (startDate && endDate) {
            if (startDate.toDateString() === endDate.toDateString()) {
                // Same day - filter for entire day
                const start = new Date(startDate);
                start.setHours(0, 0, 0, 0);
                const end = new Date(startDate);
                end.setHours(23, 59, 59, 999);
                whereClause.transactionDate = {
                    gte: start,
                    lte: end
                };
            } else {
                // Date range
                whereClause.transactionDate = {
                    gte: startDate,
                    lte: endDate
                };
            }
        }

        // Other filters
        if (entryType) whereClause.entryType = entryType;
        if (targetType) whereClause.targetType = targetType;
        if (accountId) whereClause.accountId = accountId;
        if (bankAccountId) whereClause.bankAccountId = bankAccountId;
        if (currencyCode) {
            whereClause.currency = { code: currencyCode };
        }

        // Search functionality
        if (search) {
            whereClause.OR = [
                { description: { contains: search, mode: 'insensitive' } },
                { account: { name: { contains: search, mode: 'insensitive' } } },
                { bankAccount: { bankName: { contains: search, mode: 'insensitive' } } },
                { bankAccount: { accountNumber: { contains: search, mode: 'insensitive' } } }
            ];
        }

        // Determine sort order
        const orderBy = sortOrder === ManualEntrySortOrder.NEWEST_FIRST
            ? { transactionDate: 'desc' as const }
            : { transactionDate: 'asc' as const };

        const [entries, total] = await Promise.all([
            prisma.manualEntry.findMany({
                where: whereClause,
                include: {
                    account: {
                        select: { id: true, name: true, type: true }
                    },
                    bankAccount: {
                        select: { id: true, bankName: true, accountNumber: true }
                    },
                    currency: {
                        select: { id: true, code: true, name: true }
                    },
                    ledgerEntry: {
                        select: { id: true, sourceType: true, destinationType: true, transactionType: true }
                    },
                    createdBy: {
                        select: { id: true, name: true }
                    },
                    deletedBy: {
                        select: { id: true, name: true }
                    }
                },
                orderBy,
                skip,
                take: pageSize
            }),
            prisma.manualEntry.count({
                where: whereClause
            })
        ]);

        return {
            entries: entries as ManualEntryItem[],
            pagination: {
                total,
                pages: Math.ceil(total / pageSize),
                currentPage: page,
                pageSize
            }
        };
    }

    async getManualEntryById(id: string): Promise<ManualEntryItem | null> {
        const entry = await prisma.manualEntry.findUnique({
            where: { id },
            include: {
                account: {
                    select: { id: true, name: true, type: true }
                },
                bankAccount: {
                    select: { id: true, bankName: true, accountNumber: true }
                },
                currency: {
                    select: { id: true, code: true, name: true }
                },
                ledgerEntry: {
                    select: { id: true, sourceType: true, destinationType: true, transactionType: true }
                },
                createdBy: {
                    select: { id: true, name: true }
                },
                deletedBy: {
                    select: { id: true, name: true }
                }
            }
        });

        return entry as ManualEntryItem | null;
    }

    async voidManualEntry(params: VoidManualEntryParams): Promise<VoidManualEntryResult> {
        const { id, deletedById, deletionReason } = params;

        return await prisma.$transaction(async (tx) => {
            // Get the manual entry with its related data
            const manualEntry = await tx.manualEntry.findUnique({
                where: { id },
                include: {
                    ledgerEntry: true,
                    account: true,
                    bankAccount: true,
                    currency: true
                }
            });

            if (!manualEntry) {
                throw new Error('Manual entry not found');
            }

            if (manualEntry.isDeleted) {
                throw new Error('Manual entry is already voided');
            }

            // Reverse the balance changes
            if (manualEntry.accountId && manualEntry.account) {
                // Reverse account balance (opposite of the original logic)
                // If original was CREDIT (+), reverse with DEBIT (-)
                // If original was DEBIT (-), reverse with CREDIT (+)
                const reverseAmount = manualEntry.entryType === TransactionType.CREDIT
                    ? -manualEntry.amount
                    : manualEntry.amount;

                await tx.accountBalance.update({
                    where: {
                        accountId_currencyId: {
                            accountId: manualEntry.accountId,
                            currencyId: manualEntry.currencyId
                        }
                    },
                    data: {
                        balance: {
                            increment: reverseAmount
                        }
                    }
                });

            } else if (manualEntry.bankAccountId && manualEntry.bankAccount) {
                // Reverse bank balance (opposite of the original logic)
                const reverseAmount = manualEntry.entryType === TransactionType.CREDIT
                    ? -manualEntry.amount
                    : manualEntry.amount;

                await tx.bankAccount.update({
                    where: { id: manualEntry.bankAccountId },
                    data: {
                        balance: {
                            increment: reverseAmount
                        }
                    }
                });

            } else if (manualEntry.targetType === ManualEntryTarget.CASH_VAULT) {
                // Reverse cash vault balance (opposite of the original logic)
                const vault = await tx.cashVault.findFirst();
                if (vault) {
                    const reverseAmount = manualEntry.entryType === TransactionType.CREDIT
                        ? -manualEntry.amount
                        : manualEntry.amount;

                    const updateData = this.getVaultUpdateData(manualEntry.currency.code, reverseAmount);

                    await tx.cashVault.update({
                        where: { id: vault.id },
                        data: updateData
                    });
                }
            }

            // Mark ledger entry as deleted
            await tx.ledgerEntry.update({
                where: { id: manualEntry.ledgerEntryId },
                data: {
                    isDeleted: true,
                    deletedAt: new Date(),
                    deletedById
                }
            });

            // Mark manual entry as deleted
            await tx.manualEntry.update({
                where: { id },
                data: {
                    isDeleted: true,
                    deletedAt: new Date(),
                    deletedById,
                    deletionReason
                }
            });

            return {
                success: true,
                message: 'Manual entry voided successfully'
            };
        });
    }


}

export const manualEntryService = new ManualEntryService();