import { useState } from 'react'
import { Modal, Form, Input, Alert, App, Typography, Descriptions, Tag } from 'antd'
import { manualEntryApi } from '@/renderer/services'
import type { ManualEntryItem } from '@/common/types/manualEntry'
import dayjs from 'dayjs'

const { TextArea } = Input
const { Text } = Typography

interface VoidManualEntryModalProps {
  entry: ManualEntryItem | null
  open: boolean
  onClose: () => void
  onSuccess: () => void
  currentUserId: string
}

export const VoidManualEntryModal = ({
  entry,
  open,
  onClose,
  onSuccess,
  currentUserId
}: VoidManualEntryModalProps) => {
  const [form] = Form.useForm()
  const { message } = App.useApp()
  const [loading, setLoading] = useState(false)

  if (!entry) return null

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true)

      await manualEntryApi.voidManualEntry({
        id: entry.id,
        deletedById: currentUserId,
        deletionReason: values.deletionReason
      })

      form.resetFields()
      onSuccess()
    } catch (error: any) {
      message.error(error.message || 'Failed to void manual entry')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    form.resetFields()
    onClose()
  }

  const getTargetDisplay = () => {
    if (entry.account) {
      return `${entry.account.name} (${entry.account.type})`
    }

    if (entry.bankAccount) {
      return `${entry.bankAccount.bankName} (${entry.bankAccount.accountNumber})`
    }

    if (entry.targetType) {
      return entry.targetType === 'CASH_VAULT' ? 'Cash Vault' : 'Small Counter'
    }

    return 'Unknown'
  }

  return (
    <Modal
      title="Void Manual Entry"
      open={open}
      onCancel={handleClose}
      onOk={() => form.submit()}
      okText="Void Entry"
      okButtonProps={{
        danger: true,
        loading
      }}
      cancelButtonProps={{ disabled: loading }}
      width={600}
    >
      <Alert
        message="Warning"
        description="Voiding this manual entry will reverse the balance changes and mark it as deleted. This action cannot be undone."
        type="warning"
        showIcon
        className="mb-4"
      />

      <div className="mb-4">
        <Text strong>Entry Details:</Text>
        <Descriptions size="small" column={1} className="mt-2">
          <Descriptions.Item label="Amount">
            <span
              className={
                entry.entryType === 'DEBIT'
                  ? 'font-semibold text-red-600'
                  : 'font-semibold text-green-600'
              }
            >
              {entry.entryType === 'DEBIT' ? '-' : '+'}
              {entry.amount.toLocaleString()}
            </span>
          </Descriptions.Item>

          <Descriptions.Item label="Type">
            <Tag color={entry.entryType === 'DEBIT' ? 'red' : 'green'}>{entry.entryType}</Tag>
          </Descriptions.Item>

          <Descriptions.Item label="Target">{getTargetDisplay()}</Descriptions.Item>

          <Descriptions.Item label="Date">
            {dayjs(entry.transactionDate).format('DD/MM/YYYY')}
          </Descriptions.Item>

          <Descriptions.Item label="Description">
            {entry.description || <Text type="secondary">No description</Text>}
          </Descriptions.Item>
        </Descriptions>
      </div>

      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Form.Item
          name="deletionReason"
          label="Reason for Voiding"
          rules={[
            { required: true, message: 'Please provide a reason for voiding this entry' },
            { min: 10, message: 'Reason must be at least 10 characters long' }
          ]}
        >
          <TextArea
            rows={4}
            placeholder="Please provide a detailed reason for voiding this manual entry..."
            maxLength={500}
            showCount
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}
