// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "windows"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id        String   @id @default(cuid())
  name      String
  username  String   @unique
  password  String // Hashed password
  isActive  Boolean  @default(true)
  role      UserRole
  createdAt DateTime @default(now())

  // Audit trail
  createdEntries LedgerEntry[] @relation("CreatedBy")
  deletedEntries LedgerEntry[] @relation("DeletedBy")
}

enum UserRole {
  ADMIN
  NORMAL
}

model Partner {
  id         String      @id @default(cuid())
  name       String
  containers Container[]
  createdAt  DateTime    @default(now())

  isDeleted    Boolean   @default(false)
  deleteReason String?
  deletedAt    DateTime?
}

model Container {
  id              String   @id @default(cuid())
  containerNumber String
  openedAt        DateTime @default(now())
  driverExpense   Float
  taxes           Float
  containerCost   Float
  routeExpense    Float
  fieldRent       Float

  isDeleted    Boolean   @default(false)
  deleteReason String?
  deletedAt    DateTime?

  // Optional relation to Partner (if container belongs to someone else)
  partner   Partner? @relation(fields: [partnerId], references: [id])
  partnerId String?

  // Items in container
  cars        Car[]
  carParts    CarPart[]
  electronics Electronic[]
  scraps      Scrap[]

  @@index([isDeleted, openedAt]) // For filtering and sorting
  @@index([partnerId, isDeleted]) // For partner filtering
  @@index([containerNumber]) // For search
}

model Car {
  id            String     @id @default(cuid())
  chassisNumber String     @unique
  modelNumber   String
  name          String
  color         String
  status        ItemStatus @default(AVAILABLE)

  isDeleted    Boolean   @default(false)
  deleteReason String?
  deletedAt    DateTime?

  container   Container @relation(fields: [containerId], references: [id])
  containerId String

  sale Sale? // One-to-one relation with Sale
}

model CarPart {
  id              String     @id @default(cuid())
  name            String
  initialQuantity Int        @default(1) // Initial quantity when added
  quantity        Int        @default(1) // Current/Remaining quantity
  status          ItemStatus @default(AVAILABLE)

  isDeleted    Boolean   @default(false)
  deleteReason String?
  deletedAt    DateTime?

  container   Container @relation(fields: [containerId], references: [id])
  containerId String

  sale Sale[] // Changed to one-to-many
}

model Electronic {
  id              String     @id @default(cuid())
  name            String
  initialQuantity Int        @default(1) // Initial quantity when added
  quantity        Int        @default(1) // Current/Remaining quantity
  status          ItemStatus @default(AVAILABLE)

  isDeleted    Boolean   @default(false)
  deleteReason String?
  deletedAt    DateTime?

  container   Container @relation(fields: [containerId], references: [id])
  containerId String

  sale Sale[] // Changed to one-to-many
}

model Scrap {
  id              String     @id @default(cuid())
  description     String
  initialQuantity Float // Initial quantity/weight when added
  quantity        Float // Current/Remaining quantity/weight
  status          ItemStatus @default(AVAILABLE)

  isDeleted    Boolean   @default(false)
  deleteReason String?
  deletedAt    DateTime?

  container   Container @relation(fields: [containerId], references: [id])
  containerId String

  sale Sale[] // Changed to one-to-many
}

enum ItemStatus {
  AVAILABLE
  SOLD
}

model Account {
  id          String      @id @default(cuid())
  name        String
  phoneNumber String
  address     String
  type        AccountType
  createdAt   DateTime    @default(now())

  isDeleted    Boolean   @default(false)
  deleteReason String?
  deletedAt    DateTime?

  // Relations
  balances      AccountBalance[]
  sales         Sale[]
  ledgerEntries LedgerEntry[]
  properties    RentedProperty[]
  Payment       Payment[]
  Property      Property[]
}

enum AccountType {
  CUSTOMER
  TENANT
  BOTH
}

// Multi-currency balance for accounts
model AccountBalance {
  id      String @id @default(cuid())
  balance Float  @default(0)

  account    Account  @relation(fields: [accountId], references: [id])
  accountId  String
  currency   Currency @relation(fields: [currencyId], references: [id])
  currencyId String

  @@unique([accountId, currencyId])
}

model Currency {
  id   String @id @default(cuid())
  code String @unique // PKR, USD, AED, AFN
  name String

  isDeleted    Boolean   @default(false)
  deleteReason String?
  deletedAt    DateTime?

  accountBalances AccountBalance[]
  ledgerEntries   LedgerEntry[] // Currency of the transaction
  payments        Payment[] // Added relation
}

// Currency Exchange Record
model CurrencyExchange {
  id   String   @id @default(cuid())
  date DateTime @default(now())

  // From Currency Entry
  fromEntry    LedgerEntry? @relation("FromEntry")
  fromAmount   Float
  fromCurrency String // Currency code

  // To Currency Entry
  toEntry    LedgerEntry? @relation("ToEntry")
  toAmount   Float
  toCurrency String // Currency code

  // Exchange rate at the time (actual rate used in calculation)
  exchangeRate Float

  // Display rate (for UI, can be reverse of exchange rate)
  displayRate Float

  // Description/Notes
  description String?
}

model Sale {
  id       String   @id @default(cuid())
  date     DateTime @default(now())
  itemType ItemType

  // One of these will be filled based on itemType
  carId        String?     @unique
  car          Car?        @relation(fields: [carId], references: [id])
  carPartId    String?
  carPart      CarPart?    @relation(fields: [carPartId], references: [id])
  electronicId String?
  electronic   Electronic? @relation(fields: [electronicId], references: [id])
  scrapId      String?
  scrap        Scrap?      @relation(fields: [scrapId], references: [id])

  // Add quantity field for non-car items
  quantity Float @default(1)

  totalAmount Float

  isDeleted    Boolean   @default(false)
  deleteReason String?
  deletedAt    DateTime?

  // Customer relation (null for walk-in customers)
  account   Account? @relation(fields: [accountId], references: [id])
  accountId String?

  // Location of the payment and bank id if it is a bank payment for walk in sales
  paymentLocation String?
  bankAccount     BankAccount? @relation(fields: [bankId], references: [id])
  bankId          String?

  // Related payments
  payments    Payment[]
  LedgerEntry LedgerEntry[]

  // @@index([containerId, isDeleted]) // For faster sales lookup
}

enum ItemType {
  CAR
  CARPART
  ELECTRONIC
  SCRAP
}

model Payment {
  id          String      @id @default(cuid())
  amount      Float
  date        DateTime    @default(now())
  paymentType PaymentType
  description String?

  // Relations for walk-in sales
  sale   Sale?   @relation(fields: [saleId], references: [id])
  saleId String?

  account   Account? @relation(fields: [accountId], references: [id])
  accountId String?

  // Direct currency relation
  currency   Currency @relation(fields: [currencyId], references: [id])
  currencyId String

  // Ledger entry for this payment
  ledgerEntry LedgerEntry?

  // Soft delete
  isDeleted    Boolean   @default(false)
  deleteReason String?
  deletedAt    DateTime?
}

enum PaymentType {
  PAID
  RECEIVED
}

model Expense {
  id          String   @id @default(cuid())
  amount      Float
  category    String
  description String
  date        DateTime @default(now())

  // Soft delete
  isDeleted    Boolean   @default(false)
  deleteReason String?
  deletedAt    DateTime?

  // This expense will create a ledger entry
  ledgerEntry LedgerEntry?
}

// Small Counter (Single Row Table)
model SmallCounter {
  id          String   @id @default(cuid())
  pkrBalance  Float    @default(0)
  usdBalance  Float    @default(0)
  aedBalance  Float    @default(0)
  afnBalance  Float    @default(0)
  lastUpdated DateTime @default(now()) @updatedAt
}

// Main Vault (Single Row Table)
model CashVault {
  id          String   @id @default(cuid())
  pkrBalance  Float    @default(0)
  usdBalance  Float    @default(0)
  aedBalance  Float    @default(0)
  afnBalance  Float    @default(0)
  lastUpdated DateTime @default(now()) @updatedAt
}

// Bank Accounts
model BankAccount {
  id            String  @id @default(cuid())
  accountNumber String  @unique
  bankName      String
  branchCode    String?
  balance       Float   @default(0) // Only PKR
  isActive      Boolean @default(true)

  // Relations
  ledgerEntries LedgerEntry[]
  Sale          Sale[]
}

// Updated LedgerEntry to include cash management
model LedgerEntry {
  id          String          @id @default(cuid())
  date        DateTime        @default(now())
  amount      Float
  type        TransactionType
  description String

  // Relations
  account       Account?     @relation(fields: [accountId], references: [id])
  accountId     String?
  currency      Currency     @relation(fields: [currencyId], references: [id])
  currencyId    String
  bankAccount   BankAccount? @relation(fields: [bankAccountId], references: [id])
  bankAccountId String?

  // Location tracking (where the money came from or went to)
  sourceType      TransactionLocation
  destinationType TransactionLocation

  // Transaction type categorization
  transactionType TransactionCategory

  // Optional relations based on transaction type
  sale           Sale?             @relation(fields: [saleId], references: [id])
  saleId         String?
  payment        Payment?          @relation(fields: [paymentId], references: [id])
  paymentId      String?           @unique
  expense        Expense?          @relation(fields: [expenseId], references: [id])
  expenseId      String?           @unique
  exchange       CurrencyExchange? @relation("FromEntry", fields: [exchangeFromId], references: [id])
  exchangeFromId String?           @unique
  exchangeTo     CurrencyExchange? @relation("ToEntry", fields: [exchangeToId], references: [id])
  exchangeToId   String?           @unique

  // Soft delete
  isDeleted    Boolean   @default(false)
  deleteReason String?
  deletedAt    DateTime?

  // Audit trail
  createdBy   User    @relation("CreatedBy", fields: [createdById], references: [id])
  createdById String
  deletedBy   User?   @relation("DeletedBy", fields: [deletedById], references: [id])
  deletedById String?

  @@index([date, isDeleted])
  @@index([accountId, isDeleted])
  @@index([bankAccountId, isDeleted])
  @@index([sourceType, isDeleted])
  @@index([destinationType, isDeleted])
  @@index([transactionType, isDeleted])
}

enum TransactionLocation {
  SMALL_COUNTER
  CASH_VAULT
  BANK_ACCOUNT
  ACCOUNT
  EXTERNAL
  OTHER
}

enum TransactionType {
  CREDIT
  DEBIT
}

enum TransactionCategory {
  SALE
  PAYMENT
  EXPENSE
  CURRENCY_EXCHANGE
  TRANSFER
  RENT
  DEPOSIT
  CURRENCY_DEPOSIT
  WITHDRAWAL
  CURRENCY_WITHDRAWAL
  OPENING_BALANCE
  OTHER
}

model Property {
  id          String       @id @default(cuid())
  name        String
  address     String
  type        PropertyType
  description String?

  // Rental status fields
  isRented        Boolean  @default(false)
  currentRent     Float? // Will be null when not rented
  currentTenant   Account? @relation(fields: [currentTenantId], references: [id])
  currentTenantId String? // FK to Account

  isDeleted    Boolean   @default(false)
  deleteReason String?
  deletedAt    DateTime?

  // Rental history
  rentals RentedProperty[]
}

enum PropertyType {
  SHOP
  BUILDING
  LAND
  YARD
  OTHER
}

model RentedProperty {
  id          String    @id @default(cuid())
  startDate   DateTime
  endDate     DateTime?
  monthlyRent Float

  // Relations
  property   Property @relation(fields: [propertyId], references: [id])
  propertyId String
  tenant     Account  @relation(fields: [tenantId], references: [id])
  tenantId   String

  isDeleted    Boolean   @default(false)
  deleteReason String?
  deletedAt    DateTime?
}
