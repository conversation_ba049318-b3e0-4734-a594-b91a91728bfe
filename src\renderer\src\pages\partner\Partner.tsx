import { useState } from 'react'
import { Layout, Input, But<PERSON>, Card } from 'antd'
import { FiPlus } from 'react-icons/fi'
import { PartnerList, AddPartners } from './components'
import { TransitionWrapper } from '@/renderer/components'
import { useTheme } from '@/renderer/contexts'

const { Content } = Layout
const { Search } = Input

const Partner = () => {
  const [search, setSearch] = useState('')
  const [isAddMode, setIsAddMode] = useState(false)
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const { isDarkMode } = useTheme()

  return (
    <Layout className="relative h-full">
      <TransitionWrapper isVisible={!isAddMode} direction="left">
        <Card className="m-6">
          <div
            className={`mb-6 flex items-center justify-between rounded-lg p-6 shadow-inner ${isDarkMode ? 'bg-black' : 'bg-slate-50'}`}
          >
            <Search
              placeholder="Search partners..."
              allowClear
              onChange={(e) => setSearch(e.target.value)}
              className="max-w-md"
            />
            <Button
              type="primary"
              icon={<FiPlus />}
              onClick={() => setIsAddMode(true)}
              className="bg-blue-500 hover:!bg-blue-600"
            >
              Add Partner
            </Button>
          </div>
          <PartnerList refreshTrigger={refreshTrigger} searchQuery={search} />
        </Card>
      </TransitionWrapper>

      <TransitionWrapper isVisible={isAddMode} direction="right">
        <AddPartners onClose={() => setIsAddMode(false)} setRefreshTrigger={setRefreshTrigger} />
      </TransitionWrapper>
    </Layout>
  )
}

export default Partner
