import { useEffect, useState } from 'react'
import { Typography, Space, Button, Alert, Skeleton, Row, Col, Statistic, Select } from 'antd'
import { useApi } from '@/renderer/hooks'
import { dashboardApi } from '@/renderer/services'
import { TransactionStats as TransactionStatsType, TimeRange } from '@/common/types'
import { FaExchangeAlt, FaSync, FaArrowUp, FaArrowDown } from 'react-icons/fa'
import { useTheme } from '@/renderer/contexts'

const { Title } = Typography

const TIME_RANGES = [
  { value: '1D', label: 'Today' },
  { value: '7D', label: 'Last 7 Days' },
  { value: '14D', label: 'Last 14 Days' },
  { value: '30D', label: 'Last 30 Days' }
]

const TransactionStats = () => {
  const { isDarkMode } = useTheme()
  const [timeRange, setTimeRange] = useState<TimeRange>('1D')

  const {
    data,
    isLoading,
    error,
    request: fetchTransactionStats
  } = useApi<TransactionStatsType, [TimeRange]>(dashboardApi.getTransactionStats)

  useEffect(() => {
    fetchTransactionStats(timeRange)
  }, [timeRange])

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <FaExchangeAlt className="text-2xl text-purple-600" />
            <Title level={5} className="!mb-0">
              Transaction Statistics
            </Title>
          </Space>
          <Space>
            <Select value={timeRange} disabled style={{ width: 120 }} />
            <Button icon={<FaSync />} loading />
          </Space>
        </Space>
        <Skeleton active paragraph={{ rows: 4 }} />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <FaExchangeAlt className="text-2xl text-purple-600" />
            <Title level={5} className="!mb-0">
              Transaction Statistics
            </Title>
          </Space>
          <Space>
            <Select value={timeRange} disabled style={{ width: 120 }} />
            <Button icon={<FaSync />} onClick={() => fetchTransactionStats(timeRange)} />
          </Space>
        </Space>
        <Alert
          message="Error"
          description="Failed to load transaction statistics"
          type="error"
          showIcon
        />
      </div>
    )
  }

  if (!data) return null

  return (
    <div className="flex flex-col gap-4">
      <Space className="flex justify-between">
        <Space>
          <FaExchangeAlt className="text-2xl text-purple-600" />
          <Title level={5} className="!mb-0">
            Transaction Statistics
          </Title>
        </Space>
        <Space>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            options={TIME_RANGES}
            style={{ width: 120 }}
          />
          <Button icon={<FaSync />} onClick={() => fetchTransactionStats(timeRange)} />
        </Space>
      </Space>

      <div className={`rounded-lg p-4 ${isDarkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Statistic title="Total Transactions" value={data.totalTransactions} />
          </Col>
          <Col span={12}>
            <Statistic
              title="Credits"
              value={data.creditCount}
              prefix={<FaArrowUp className="text-green-500" />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="Debits"
              value={data.debitCount}
              prefix={<FaArrowDown className="text-red-500" />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Col>
        </Row>

        <Title level={5} className="mb-2 mt-4">
          Volume by Type
        </Title>
        <Row gutter={[16, 16]}>
          {Object.entries(data.volumeByType).map(([type, count]) => (
            <Col span={12} key={type}>
              <Statistic title={type.replace(/_/g, ' ')} value={count} className="capitalize" />
            </Col>
          ))}
        </Row>
      </div>
    </div>
  )
}

export default TransactionStats
