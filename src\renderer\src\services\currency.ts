import { http } from "./http";
import { Channels } from "@/common/constants";

export const getAllCurrencies = () => {
    return http.get(`${Channels.GET_ALL_CURRENCIES}`);
}

export const getCurrencyTransactions = (currencyCode: string, startDate: string, endDate: string, page: number, pageSize: number) => {
    const params = {
        currencyCode,
        startDate,
        endDate,
        page,
        pageSize
    }
    return http.get(`${Channels.GET_CURRENCY_TRANSACTIONS}`, { params });
}

export const getCurrencySummary = (currencyCode: string, startDate: string, endDate: string) => {
    const params = {
        currencyCode,
        startDate,
        endDate
    }
    return http.get(`${Channels.GET_CURRENCY_SUMMARY}`, { params });
}

export const getCurrencyBalances = (currencyCode: string) => {
    const params = {
        currencyCode
    }
    return http.get(`${Channels.GET_CURRENCY_BALANCES}`, { params });
}
