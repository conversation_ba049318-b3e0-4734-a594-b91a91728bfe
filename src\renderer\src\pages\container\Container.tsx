import { useState } from 'react'
import { Layout, Input, Button, DatePicker, Select, Space, Card, message } from 'antd'
import { FiPlus, FiFileText } from 'react-icons/fi'
import { ContainerList } from './components/ContainerList'
import { AddContainer } from './components/AddContainer'
import { TransitionWrapper } from '@/renderer/components'
import PDFConfirmationModal from '@/renderer/components/PDFConfirmationModal'
import { Dayjs } from 'dayjs'
import { usePartnerContext, useTheme } from '@/renderer/contexts'
import { handleContainerListPDF } from './utils/generateContainerListPDF'
import { SortAscendingOutlined, SortDescendingOutlined } from '@ant-design/icons'

const { Content } = Layout
const { Search } = Input

const Container = () => {
  const [search, setSearch] = useState('')
  const [isAddMode, setIsAddMode] = useState(false)
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const [startDate, setStartDate] = useState<Dayjs | null>(null)
  const [endDate, setEndDate] = useState<Dayjs | null>(null)
  const [partnerId, setPartnerId] = useState<string | undefined>(undefined)
  const [isPdfModalOpen, setIsPdfModalOpen] = useState(false)
  const [orderBy, setOrderBy] = useState<'asc' | 'desc'>('asc')

  const { partners } = usePartnerContext()

  const { isDarkMode } = useTheme()

  const handleGeneratePDF = async (action: 'save' | 'print') => {
    try {
      await handleContainerListPDF(startDate?.toDate(), endDate?.toDate(), partnerId, action)
      setIsPdfModalOpen(false)
    } catch (error) {
      console.error('Failed to generate PDF:', error)
      message.error('Failed to generate PDF')
    }
  }

  return (
    <Layout className="relative h-full">
      <TransitionWrapper isVisible={!isAddMode} direction="left">
        <Card className="m-6">
          <div
            className={`mb-6 flex items-center justify-between rounded-lg p-6 shadow-inner ${isDarkMode ? 'bg-black' : 'bg-slate-50'}`}
          >
            <Space>
              <Search
                placeholder="Search containers..."
                allowClear
                onChange={(e) => setSearch(e.target.value)}
                className="max-w-md"
              />
              <DatePicker.RangePicker
                onChange={(value) => {
                  if (value) {
                    setStartDate(value[0])
                    setEndDate(value[1])
                  } else {
                    setStartDate(null)
                    setEndDate(null)
                  }
                }}
              />
              <Select
                style={{ width: 200 }}
                options={partners}
                allowClear
                onChange={(value) => setPartnerId(value)}
                placeholder="Select partner"
              />
              <Select
                value={orderBy}
                onChange={setOrderBy}
                className="w-56"
                options={[
                  {
                    label: (
                      <div className="flex items-center">
                        <SortDescendingOutlined className="mr-2" /> Newest First
                      </div>
                    ),
                    value: 'desc'
                  },
                  {
                    label: (
                      <div className="flex items-center">
                        <SortAscendingOutlined className="mr-2" /> Oldest First
                      </div>
                    ),
                    value: 'asc'
                  }
                ]}
              />
            </Space>

            <Space>
              <Button
                icon={<FiFileText />}
                onClick={() => setIsPdfModalOpen(true)}
                className="mr-2"
              >
                Generate PDF
              </Button>
              <Button
                type="primary"
                icon={<FiPlus />}
                onClick={() => setIsAddMode(true)}
                className="bg-blue-500 hover:!bg-blue-600"
              >
                Add Container
              </Button>
            </Space>
          </div>
          <ContainerList
            searchQuery={search}
            refreshTrigger={refreshTrigger}
            startDate={startDate}
            endDate={endDate}
            partnerId={partnerId}
            orderBy={orderBy}
          />
        </Card>
      </TransitionWrapper>

      <TransitionWrapper isVisible={isAddMode} direction="right">
        <AddContainer onClose={() => setIsAddMode(false)} setRefreshTrigger={setRefreshTrigger} />
      </TransitionWrapper>

      <PDFConfirmationModal
        isOpen={isPdfModalOpen}
        onClose={() => setIsPdfModalOpen(false)}
        onSave={() => handleGeneratePDF('save')}
        onPrint={() => handleGeneratePDF('print')}
        title="Container List PDF"
      />
    </Layout>
  )
}

export default Container
