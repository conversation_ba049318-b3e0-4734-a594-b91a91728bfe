import { A5_CONFIG, PDF_STYLES, savePDF, printPDF } from '@/renderer/utils/pdfUtils'
import { ledgerApi } from '@/renderer/services'
import jsPDF from 'jspdf'
import 'jspdf-autotable'
import dayjs from 'dayjs'

export const generateAccountLedgerPDF = async (
    accountId: string,
    currencyCode: string,
    accountName: string
): Promise<jsPDF> => {

    const response = await ledgerApi.getAccountLedgerEntriesForPDF(accountId, currencyCode)

    if (response.error.error || response.data.error) {
        throw new Error(response.error.message || response.data.error.message)
    }

    const { summary, entries } = response.data.data

    // Create PDF document
    const doc = new jsPDF(A5_CONFIG)
    const pageWidth = doc.internal.pageSize.width

    // Define header content function - will only be called for the first page
    const drawHeader = () => {
        // Add header
        doc.setFontSize(PDF_STYLES.header.fontSize)
        doc.text('Account Statement', pageWidth / 2, 10, { align: 'center' })

        // Add account details
        doc.setFontSize(PDF_STYLES.header.titleFontSize)
        doc.text([
            `Account: ${summary.accountName}`,
            `Phone: ${summary.accountPhone}`,
            `Address: ${summary.accountAddress}`,
            `Currency: ${summary.currency}`,
            `Period: ${dayjs(summary.dateRange?.from).format('DD/MM/YYYY')} - ${dayjs(summary.dateRange?.to).format('DD/MM/YYYY')}`,
        ], 10, 20)

        // Add summary with colors
        let xPos = pageWidth - 10
        let yPos = 20

        // Draw summary text with colors
        doc.setTextColor(0, 0, 0) // Black for labels
        doc.text('Total Credits:', xPos, yPos, { align: 'right' })
        yPos += 5
        doc.setTextColor(0, 128, 0) // Green for credits
        doc.text(summary.totalCredits.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }), xPos, yPos, { align: 'right' })
        yPos += 5
        doc.setTextColor(0, 0, 0) // Black for labels
        doc.text('Total Debits:', xPos, yPos, { align: 'right' })
        yPos += 5
        doc.setTextColor(255, 0, 0) // Red for debits
        doc.text(summary.totalDebits.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }), xPos, yPos, { align: 'right' })
        yPos += 5
        doc.setTextColor(0, 0, 0) // Black for labels
        doc.text('Net Balance:', xPos, yPos, { align: 'right' })
        yPos += 5
        doc.setTextColor(summary.netBalance >= 0 ? 0 : 255, summary.netBalance >= 0 ? 128 : 0, 0)
        doc.text(summary.netBalance.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }), xPos, yPos, { align: 'right' })

        // Reset text color
        doc.setTextColor(0, 0, 0)
    }

    // Draw the header once before the table
    drawHeader()

    // Add entries table with separate credit/debit columns
    // @ts-ignore (jspdf-autotable types are not properly recognized)
    doc.autoTable({
        startY: 50,
        columns: [
            { header: 'S.No', dataKey: 'serialNumber' },
            { header: 'Date', dataKey: 'date' },
            { header: 'Description', dataKey: 'description' },
            { header: 'Credit', dataKey: 'credit' },
            { header: 'Debit', dataKey: 'debit' },
            { header: 'Balance', dataKey: 'balance' }
        ],
        body: entries.map((entry, index) => ({
            serialNumber: index + 1,
            date: dayjs(entry.date).format('DD/MM/YYYY'),
            description: entry.description,
            credit: entry.type === 'CREDIT' ? entry.amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }) : '',
            debit: entry.type === 'DEBIT' ? entry.amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }) : '',
            balance: entry.runningBalance.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }),
            _balance: entry.runningBalance // Hidden field for color calculation
        })),
        styles: {
            ...PDF_STYLES.table.styles,
            fontSize: 7, // Smaller font size for A5
            fillColor: [255, 255, 255] // Ensure all cells have white background
        },
        headStyles: {
            ...PDF_STYLES.table.headStyles,
            textColor: [255, 255, 255], // Ensure header text is white, not gray
            fillColor: [0, 0, 0] // black background for header
        },
        footStyles: PDF_STYLES.table.footStyles,
        margin: { left: 5, right: 5 },
        columnStyles: {
            serialNumber: { cellWidth: 10, halign: 'center' },
            date: { cellWidth: 18 },
            description: { cellWidth: 'auto' },
            credit: {
                cellWidth: 20,
                halign: 'right'
            },
            debit: {
                cellWidth: 20,
                halign: 'right'
            },
            balance: {
                cellWidth: 22,
                halign: 'right'
            }
        },
        didParseCell: function (data) {
            // Only style non-header cells
            if (data.section === 'body' || data.section === 'foot') {
                if (data.column.dataKey === 'credit' && data.cell.raw) {
                    data.cell.styles.textColor = [0, 128, 0] // Green for credits
                }
                if (data.column.dataKey === 'debit' && data.cell.raw) {
                    data.cell.styles.textColor = [255, 0, 0] // Red for debits
                }
                if (data.column.dataKey === 'balance' && data.cell.raw) {
                    const balanceValue = parseFloat(data.cell.raw.replace(/,/g, ''))
                    data.cell.styles.textColor = balanceValue >= 0 ? [0, 128, 0] : [255, 0, 0]

                    // Add minus sign if needed
                    if (balanceValue < 0 && !data.cell.text[0].startsWith('-')) {
                        data.cell.text[0] = '-' + data.cell.text[0]
                    }
                }
            }
        }
    })

    // Add footer
    const pageCount = (doc as any).internal.getNumberOfPages()
    for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i)
        doc.setFontSize(PDF_STYLES.footer.fontSize)
        doc.setTextColor(0, 0, 0) // Reset to black for footer
        doc.text(
            `Page ${i} of ${pageCount}`,
            pageWidth / 2,
            doc.internal.pageSize.height - PDF_STYLES.footer.margin,
            { align: 'center' }
        )
    }

    return doc
}

export const handleAccountLedgerPDF = async (
    accountId: string,
    currencyCode: string,
    accountName: string,
    action: 'save' | 'print'
): Promise<void> => {
    try {
        const doc = await generateAccountLedgerPDF(accountId, currencyCode, accountName)
        const fileName = `${accountName}_${currencyCode}_${dayjs().format('YYYY-MM-DD')}`

        if (action === 'save') {
            await savePDF(doc, fileName)
        } else {
            await printPDF(doc)
        }
    } catch (error) {
        console.error('Failed to generate PDF:', error)
        throw error
    }
}
