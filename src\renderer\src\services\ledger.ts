import { http } from './http'
import { Channels } from '@/common/constants'
import type {
    GetAccountLedgerParams,
    GetAccountStatementParams,
    ReconcileBalanceParams,
    GetDailyLedgerParams,
    GetTransactionsByCategoryParams,
    GetTransactionsByLocationParams,
    GetBalanceSheetParams,
    GetProfitLossStatementParams,
} from '@/common/types'


export async function getAccountStatement(accountId: string, params: GetAccountStatementParams) {
    return await http.get(Channels.GET_ACCOUNT_STATEMENT_LEDGER, { params: { accountId }, query: params })
}

export async function reconcileBalance(data: ReconcileBalanceParams) {
    return await http.post(Channels.RECONCILE_BALANCE, { body: data })
}

export async function getDailyLedger(params: GetDailyLedgerParams) {
    return await http.get(Channels.GET_DAILY_LEDGER, { query: params })
}

export async function getTransactionsByCategory(params: GetTransactionsByCategoryParams) {
    return await http.get(Channels.GET_TRANSACTIONS_BY_CATEGORY, { query: params })
}

export async function getTransactionsByLocation(params: GetTransactionsByLocationParams) {
    return await http.get(Channels.GET_TRANSACTIONS_BY_LOCATION, { query: params })
}

export async function getBalanceSheet(params?: GetBalanceSheetParams) {
    return await http.get(Channels.GET_BALANCE_SHEET, params ? { query: params } : undefined)
}

export async function getProfitLossStatement(params: GetProfitLossStatementParams) {
    return await http.get(Channels.GET_PROFIT_LOSS_STATEMENT, { query: params })
}

export async function getPaginatedLedgerEntries(accountId: string, params: {
    page?: number
    limit?: number
    currencyId: string
    startDate?: string | Date
    endDate?: string | Date
}) {
    return await http.get(Channels.GET_PAGINATED_LEDGER, { params: { accountId }, query: params })
}

export async function getAccountLedgerEntriesForPDF(accountId: string, currencyCode: string) {
    return await http.get(Channels.GET_ACCOUNT_LEDGER_ENTRIES_FOR_PDF, { params: { accountId, currencyCode } })
}




