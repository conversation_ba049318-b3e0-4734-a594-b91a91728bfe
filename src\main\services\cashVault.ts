import { CashVault } from '@prisma/client';
import { prisma } from '../db';
import {
    InitializeCashVaultData,
    CashVaultBalances,
    GetCashVaultStatementParams,
    CashVaultStatement,
    CashVaultStatementEntry,
    CashVaultStatementSummary
} from '@/common/types';

class CashVaultService {


    private async ensureCashVaultExists() {
        const cashVault = await prisma.cashVault.findFirst();
        if (!cashVault) {
            throw new Error('Cash vault not initialized');
        }
        return cashVault
    }

    async initializeCashVault(data: InitializeCashVaultData) {
        const cashVault = await prisma.cashVault.findFirst()
        if (cashVault) {
            throw new Error('Cash vault already initialized');
        }

        return await prisma.$transaction(async (tx) => {
            // Create the cash vault first
            const vault = await tx.cashVault.create({
                data: {
                    pkrBalance: data.pkrBalance ?? 0,
                    usdBalance: data.usdBalance ?? 0,
                    aedBalance: data.aedBalance ?? 0,
                    afnBalance: data.afnBalance ?? 0
                }
            });

            // Create ledger entries for each non-zero balance
            const currencies = [
                { code: 'PKR', balance: data.pkrBalance },
                { code: 'USD', balance: data.usdBalance },
                { code: 'AED', balance: data.aedBalance },
                { code: 'AFN', balance: data.afnBalance }
            ];

            for (const { code, balance } of currencies) {
                if (balance && balance > 0) {
                    await tx.ledgerEntry.create({
                        data: {
                            amount: balance,
                            type: 'CREDIT',
                            description: `Opening balance for Cash Vault - ${code}`,
                            sourceType: 'OTHER',
                            destinationType: 'CASH_VAULT',
                            transactionType: 'OPENING_BALANCE',
                            currency: { connect: { code } },
                            createdBy: { connect: { id: data.userId } }
                        }
                    });
                }
            }

            return vault;
        });
    }

    async getCashVaultBalances(): Promise<CashVaultBalances> {
        const cashVault = await this.ensureCashVaultExists();
        return cashVault;
    }

    async getBalanceByCurrency(currency: string) {
        const cashVault = await this.ensureCashVaultExists();

        const balanceKey = `${currency.toLowerCase()}Balance` as keyof CashVault;
        const balance = cashVault[balanceKey];

        if (typeof balance !== 'number') {
            throw new Error(`Unsupported currency: ${currency}`);
        }

        return { currency, balance };
    }

    async adjustBalance(currency: string, adjustment: number, reason: string, userId: string) {
        return await prisma.$transaction(async (tx) => {
            await this.ensureCashVaultExists();

            await tx.ledgerEntry.create({
                data: {
                    amount: Math.abs(adjustment),
                    type: adjustment > 0 ? 'CREDIT' : 'DEBIT',
                    description: `Manual vault balance adjustment: ${reason}`,
                    sourceType: adjustment > 0 ? 'OTHER' : 'CASH_VAULT',
                    destinationType: adjustment > 0 ? 'CASH_VAULT' : 'OTHER',
                    transactionType: 'OTHER',
                    currency: { connect: { code: currency } },
                    createdBy: { connect: { id: userId } }
                }
            });

            return await tx.cashVault.updateMany({
                data: this.getUpdateDataForCurrency(currency, adjustment)
            });
        });
    }

    private getUpdateDataForCurrency(currency: string, adjustment: number) {
        const supportedCurrencies = ['PKR', 'USD', 'AED', 'AFN'];
        if (!supportedCurrencies.includes(currency)) {
            throw new Error(`Unsupported currency: ${currency}`);
        }

        return {
            [currency.toLowerCase() + 'Balance']: {
                increment: adjustment
            }
        };
    }

    // Similar to SmallCounter but referencing CASH_VAULT
    async getAllTransactions(page: number = 1, pageSize: number = 20, startDate?: Date, endDate?: Date, sortOrder: 'asc' | 'desc' = 'asc') {
        const skip = (page - 1) * pageSize;

        // Handle date filtering
        let dateFilter = {}
        if (startDate && endDate) {
            if (startDate.toDateString() === endDate.toDateString()) {
                // Same day - filter for entire day
                const start = new Date(startDate)
                start.setHours(0, 0, 0, 0)
                const end = new Date(startDate)
                end.setHours(23, 59, 59, 999)
                dateFilter = {
                    date: {
                        gte: start,
                        lte: end
                    }
                }
            } else {
                // Date range
                dateFilter = {
                    date: {
                        gte: startDate,
                        lte: endDate
                    }
                }
            }
        }

        const [transactions, total] = await Promise.all([
            prisma.ledgerEntry.findMany({
                where: {
                    OR: [
                        { sourceType: 'CASH_VAULT' },
                        { destinationType: 'CASH_VAULT' }
                    ],
                    isDeleted: false,
                    ...dateFilter
                },
                include: {
                    currency: true,
                    createdBy: { select: { name: true } },
                    account: { select: { name: true } }
                },
                orderBy: { date: sortOrder },
                skip,
                take: pageSize
            }),
            prisma.ledgerEntry.count({
                where: {
                    OR: [
                        { sourceType: 'CASH_VAULT' },
                        { destinationType: 'CASH_VAULT' }
                    ],
                    isDeleted: false,
                    ...dateFilter
                }
            })
        ]);

        return {
            transactions,
            pagination: {
                total,
                pages: Math.ceil(total / pageSize),
                currentPage: page,
                pageSize
            }
        };
    }

    async reconcileBalance(currency: string) {
        const currencyId = await prisma.currency.findFirst({
            where: { code: currency },
            select: { id: true }
        });

        if (!currencyId) {
            throw new Error(`Currency ${currency} not found`);
        }

        // Calculate credits and debits separately using database aggregation
        const [credits, debits] = await Promise.all([
            prisma.ledgerEntry.aggregate({
                where: {
                    currencyId: currencyId.id,
                    isDeleted: false,
                    type: 'CREDIT',
                    OR: [
                        { sourceType: 'CASH_VAULT' },
                        { destinationType: 'CASH_VAULT' }
                    ]
                },
                _sum: { amount: true }
            }),
            prisma.ledgerEntry.aggregate({
                where: {
                    currencyId: currencyId.id,
                    isDeleted: false,
                    type: 'DEBIT',
                    OR: [
                        { sourceType: 'CASH_VAULT' },
                        { destinationType: 'CASH_VAULT' }
                    ]
                },
                _sum: { amount: true }
            })
        ]);

        // Calculate the net balance
        const calculatedBalance = (credits._sum.amount ?? 0) - (debits._sum.amount ?? 0);

        // Get current balance from the vault
        const currentBalance = await this.getBalanceByCurrency(currency);
        const isReconciled = calculatedBalance === currentBalance.balance;

        return {
            isReconciled,
            currentBalance: currentBalance.balance,
            calculatedBalance,
            difference: currentBalance.balance - calculatedBalance
        };
    }

    async generateCashVaultStatement(currencyCode: string, params: GetCashVaultStatementParams): Promise<CashVaultStatement> {
        const { startDate, endDate, page = 1, pageSize = 100 } = params;

        // Validate input
        if (!startDate || !endDate || !currencyCode) {
            throw new Error('Currency code, start date and end date are required');
        }

        // Get the currency
        const currency = await prisma.currency.findUnique({
            where: { code: currencyCode }
        });

        if (!currency) {
            throw new Error(`Currency with code ${currencyCode} not found`);
        }

        // Ensure cash vault exists
        await this.ensureCashVaultExists();

        // Helper function to process date range for queries
        const processDateRange = (start: Date, end: Date) => {
            if (start.toDateString() === end.toDateString()) {
                // Same day - filter for entire day
                const startOfDay = new Date(start);
                startOfDay.setHours(0, 0, 0, 0);
                const endOfDay = new Date(start);
                endOfDay.setHours(23, 59, 59, 999);
                return {
                    gte: startOfDay,
                    lte: endOfDay
                };
            } else {
                return {
                    gte: start,
                    lte: end
                };
            }
        };

        // Calculate opening balance (sum of all transactions before start date)
        const creditsBefore = await prisma.ledgerEntry.aggregate({
            where: {
                destinationType: 'CASH_VAULT',
                date: { lt: startDate },
                currency: { code: currencyCode },
                isDeleted: false
            },
            _sum: { amount: true }
        });

        const debitsBefore = await prisma.ledgerEntry.aggregate({
            where: {
                sourceType: 'CASH_VAULT',
                date: { lt: startDate },
                currency: { code: currencyCode },
                isDeleted: false
            },
            _sum: { amount: true }
        });

        // Calculate opening balance
        const openingBalance = (creditsBefore._sum.amount || 0) - (debitsBefore._sum.amount || 0);

        // Get total count for pagination
        const total = await prisma.ledgerEntry.count({
            where: {
                OR: [
                    { destinationType: 'CASH_VAULT' },
                    { sourceType: 'CASH_VAULT' }
                ],
                currency: { code: currencyCode },
                date: processDateRange(startDate, endDate),
                isDeleted: false
            }
        });

        // Fetch transactions for the current page
        const transactions = await prisma.ledgerEntry.findMany({
            where: {
                OR: [
                    { destinationType: 'CASH_VAULT' },
                    { sourceType: 'CASH_VAULT' }
                ],
                currency: { code: currencyCode },
                date: processDateRange(startDate, endDate),
                isDeleted: false
            },
            include: {
                createdBy: { select: { name: true } }
            },
            orderBy: { date: 'asc' },
            skip: (page - 1) * pageSize,
            take: pageSize
        });

        // Calculate running balance and prepare statement entries
        let runningBalance = openingBalance;
        let totalCredits = 0;
        let totalDebits = 0;

        const statementEntries: CashVaultStatementEntry[] = transactions.map(tx => {
            // Determine if this is a credit or debit for the cash vault
            const isCashVaultCredit = tx.destinationType === 'CASH_VAULT';

            // Update running balance based on transaction type
            if (isCashVaultCredit) {
                runningBalance += tx.amount;
                totalCredits += tx.amount;
            } else {
                runningBalance -= tx.amount;
                totalDebits += tx.amount;
            }

            // Create statement entry with credit/debit columns
            return {
                id: tx.id,
                date: tx.date,
                description: tx.description || '',
                credit: isCashVaultCredit ? tx.amount : null,
                debit: !isCashVaultCredit ? tx.amount : null,
                runningBalance,
                createdBy: tx.createdBy
            };
        });

        // Calculate summary
        const summary: CashVaultStatementSummary = {
            totalCredits,
            totalDebits,
            net: totalCredits - totalDebits
        };

        return {
            currencyCode,
            startDate,
            endDate,
            entries: statementEntries,
            openingBalance,
            closingBalance: runningBalance,
            summary,
            pagination: {
                total,
                pages: Math.ceil(total / pageSize),
                currentPage: page,
                pageSize
            }
        };
    }
}

export const cashVaultService = new CashVaultService();