import { prisma } from '../db';
import { Prisma } from '@prisma/client';


const DEFAULT_CURRENCIES = [
    { code: 'PKR', name: 'Pakistani Rupee' },
    { code: 'USD', name: 'US Dollar' },
    { code: 'AED', name: 'UAE Dirham' },
    { code: 'AFN', name: 'Afghan Afghani' }
];

class CurrencyService {
    async initializeCurrencies() {
        for (const currency of DEFAULT_CURRENCIES) {
            await prisma.currency.upsert({
                where: { code: currency.code },
                update: {},
                create: currency
            });
        }
    }

    // Get all currencies
    async getAllCurrencies() {
        return await prisma.currency.findMany({
            where: { isDeleted: false }
        });
    }

    // Get currency transactions summary
    async getCurrencyTransactions(
        currencyCode: string,
        startDate?: Date,
        endDate?: Date,
        page: number = 1,
        pageSize: number = 20
    ) {
        const where: Prisma.LedgerEntryWhereInput = {
            currency: { code: currencyCode },
            isDeleted: false
        };

        if (startDate && endDate) {
            where.date = { gte: startDate, lte: endDate };
        }

        const [transactions, total] = await Promise.all([
            prisma.ledgerEntry.findMany({
                where,
                include: {
                    currency: true,
                    createdBy: { select: { name: true } },
                    account: { select: { name: true } }
                },
                orderBy: { date: 'desc' },
                skip: (page - 1) * pageSize,
                take: pageSize
            }),
            prisma.ledgerEntry.count({ where })
        ]);

        return {
            transactions,
            pagination: {
                total,
                pages: Math.ceil(total / pageSize),
                currentPage: page,
                pageSize
            }
        };
    }

    // Get currency summary (total inflow/outflow)
    async getCurrencySummary(currencyCode: string, startDate?: Date, endDate?: Date) {

        console.log('startDate', startDate, 'endDate', endDate);
        const where: Prisma.LedgerEntryWhereInput = {
            currency: { code: currencyCode },
            isDeleted: false
        };

        if (startDate && endDate) {
            // If dates are the same, set time range from start to end of day
            if (startDate.toDateString() === endDate.toDateString()) {
                const start = new Date(startDate);
                start.setHours(0, 0, 0, 0);

                const end = new Date(endDate);
                end.setHours(23, 59, 59, 999);

                where.date = { gte: start, lte: end };
            } else {
                where.date = { gte: startDate, lte: endDate };
            }
        }

        // Use aggregation to efficiently calculate totals
        const [credits, debits] = await Promise.all([
            prisma.ledgerEntry.aggregate({
                where: {
                    ...where,
                    type: 'CREDIT'
                },
                _sum: {
                    amount: true
                }
            }),
            prisma.ledgerEntry.aggregate({
                where: {
                    ...where,
                    type: 'DEBIT'
                },
                _sum: {
                    amount: true
                }
            })
        ]);

        const inflow = credits._sum.amount || 0;
        const outflow = debits._sum.amount || 0;
        const netFlow = inflow - outflow;

        return { inflow, outflow, netFlow };
    }

    // Get all currency balances across all locations
    async getCurrencyBalances(currencyCode: string) {
        const [smallCounter, cashVault, bankAccounts, accountBalances] = await Promise.all([
            prisma.smallCounter.findFirst(),
            prisma.cashVault.findFirst(),
            prisma.bankAccount.findMany({ where: { isActive: true } }),
            prisma.accountBalance.findMany({
                where: { currency: { code: currencyCode } },
                include: { account: true }
            })
        ]);

        const balanceKey = `${currencyCode.toLowerCase()}Balance` as keyof typeof smallCounter;

        return {
            smallCounter: smallCounter ? smallCounter[balanceKey] as number : 0,
            cashVault: cashVault ? cashVault[balanceKey] as number : 0,
            bankAccounts: bankAccounts.reduce((sum, acc) => sum + acc.balance, 0),
            accountBalances: accountBalances.map(bal => ({
                accountName: bal.account.name,
                balance: bal.balance
            })),
            totalSystemBalance: (smallCounter ? smallCounter[balanceKey] as number : 0) +
                (cashVault ? cashVault[balanceKey] as number : 0) +
                bankAccounts.reduce((sum, acc) => sum + acc.balance, 0)
        };
    }

    async getCurrencyBalance(currencyId: string) {
        const currency = await prisma.currency.findUnique({
            where: { id: currencyId }
        })

        if (!currency) {
            throw new Error('Currency not found')
        }

        // Get balances from all locations
        const [smallCounter, cashVault, bankAccounts, accountBalances] = await Promise.all([
            prisma.smallCounter.findFirst(),
            prisma.cashVault.findFirst(),
            currency.code === 'PKR' ? prisma.bankAccount.findMany({
                where: {
                    isActive: true
                }
            }) : Promise.resolve([]),
            prisma.accountBalance.findMany({
                where: {
                    currencyId
                }
            })
        ])

        // Calculate total balance
        const balanceField = `${currency.code.toLowerCase()}Balance` as keyof typeof smallCounter
        const smallCounterBalance = smallCounter ? smallCounter[balanceField] as number : 0
        const cashVaultBalance = cashVault ? cashVault[balanceField] as number : 0
        const bankAccountsBalance = bankAccounts.reduce((sum, account) => sum + account.balance, 0)
        const accountsBalance = accountBalances.reduce((sum, balance) => sum + balance.balance, 0)

        return {
            currency,
            balances: {
                smallCounter: smallCounterBalance,
                cashVault: cashVaultBalance,
                bankAccounts: bankAccountsBalance,
                accounts: accountsBalance,
                total: smallCounterBalance + cashVaultBalance + bankAccountsBalance + accountsBalance
            },
            locations: {
                smallCounter,
                cashVault,
                bankAccounts,
                accountBalances
            }
        }
    }

}

export const currencyService = new CurrencyService();