import { Card, Form, InputNumber, Button, message } from 'antd'
import { useApi } from '@/renderer/hooks'
import { smallCounterApi } from '@/renderer/services'
import type { InitializeSmallCounterData } from '@/common/types'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'

interface SmallCounterInitializeProps {
  setRefreshTrigger: (value: any) => void
}

export const SmallCounterInitialize = ({ setRefreshTrigger }: SmallCounterInitializeProps) => {
  const [form] = Form.useForm()
  const user = useSelector((state: IRootState) => state.user.data)

  const handleInitialize = async (values: InitializeSmallCounterData) => {
    const response = await smallCounterApi.initializeSmallCounter({
      ...values,
      userId: user?.id || ''
    })

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Small Counter initialized successfully')
    form.resetFields()
    setRefreshTrigger((prev) => prev + 1)
  }

  return (
    <Card title="Initialize Small Counter">
      <Form form={form} onFinish={handleInitialize} layout="vertical">
        <Form.Item name="pkrBalance" label="PKR Balance">
          <InputNumber
            style={{ width: '100%' }}
            formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
          />
        </Form.Item>
        <Form.Item name="usdBalance" label="USD Balance">
          <InputNumber
            style={{ width: '100%' }}
            formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
          />
        </Form.Item>
        <Form.Item name="aedBalance" label="AED Balance">
          <InputNumber
            style={{ width: '100%' }}
            formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
          />
        </Form.Item>
        <Form.Item name="afnBalance" label="AFN Balance">
          <InputNumber
            style={{ width: '100%' }}
            formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
          />
        </Form.Item>
        <Button type="primary" htmlType="submit" block>
          Initialize
        </Button>
      </Form>
    </Card>
  )
}
