import { Card, Typography } from 'antd'
import { BankOutlined } from '@ant-design/icons'
import { formatCurrency } from '@/renderer/utils'
import { LocationBalanceResponse } from '@/renderer/services/exchange'

const { Text } = Typography

interface LocationCardProps {
  location: LocationBalanceResponse | null
  title: string
  loading: boolean
}

export const LocationCard = ({ location, title, loading }: LocationCardProps) => {
  if (!location) return null

  return (
    <Card 
      className="mt-2" 
      size="small" 
      title={
        <div className="flex items-center">
          <BankOutlined className="mr-2" />
          <span>{title}</span>
        </div>
      }
      loading={loading}
    >
      <div className="mb-2">
        <Text strong>Location:</Text> {location.location.replace('_', ' ')}
      </div>
      {location.bankName && (
        <div className="mb-2">
          <Text strong>Bank:</Text> {location.bankName}
        </div>
      )}
      {location.bankAccountNumber && (
        <div className="mb-2">
          <Text strong>Account:</Text> {location.bankAccountNumber}
        </div>
      )}
      <div>
        <Text strong>Balance:</Text> {formatCurrency(location.balance, location.currency)}
      </div>
    </Card>
  )
}
