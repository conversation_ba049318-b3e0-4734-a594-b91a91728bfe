import { http } from './http';
import { Channels } from '@/common/constants';
import { CreateAccountData, UpdateAccountData, GetAccountsParams } from '@/common/types';

export const createAccount = async (data: CreateAccountData) => {
    return await http.post(Channels.CREATE_ACCOUNT, { body: data });
};

export const deleteAccount = async (id: string) => {
    return await http.delete(Channels.DELETE_ACCOUNT, { params: { id } });
};

export const updateAccount = async (id: string, data: UpdateAccountData) => {
    return await http.put(Channels.UPDATE_ACCOUNT, {
        params: { id },
        body: data
    });
};

export const getAccounts = async (params: Partial<GetAccountsParams> = {}) => {
    const defaultParams: GetAccountsParams = {
        page: params.page ?? 1,
        limit: params.limit ?? 10,
        type: params.type,
        search: params.search,
        includeDeleted: params.includeDeleted
    };

    return await http.get(Channels.GET_ACCOUNTS, { query: defaultParams });
};

export const getAccountById = async (id: string) => {
    return await http.get(Channels.GET_ACCOUNT_BY_ID, { params: { id } });
};

export const getAccountsForSelect = async () => {
    return await http.get(Channels.GET_ACCOUNTS_SELECT);
};

export const getCustomerAccountsForSelect = async () => {
    return await http.get(Channels.GET_CUSTOMER_ACCOUNTS_SELECT);
};

export const getTenantAccountsForSelect = async () => {
    return await http.get(Channels.GET_TENANT_ACCOUNTS_SELECT);
};

export const getAccountStatement = async (accountId: string, startDate: Date, endDate: Date) => {
    return await http.get(Channels.GET_ACCOUNT_STATEMENT, {
        params: { accountId },
        query: { startDate: startDate.toISOString(), endDate: endDate.toISOString() }
    });
};

export const getBalanceSummary = async (accountId: string) => {
    return await http.get(Channels.GET_BALANCE_SUMMARY, {
        params: { accountId }
    });
};

export const getAccountStatistics = async (accountId: string) => {
    return await http.get(Channels.GET_ACCOUNT_STATISTICS, {
        params: { accountId }
    });
};