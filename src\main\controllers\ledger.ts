import { GetAccountLedgerParams, GetAccountStatementParams, GetBalanceSheetParams, GetDailyLedgerParams, GetProfitLossStatementParams, GetTransactionsByCategoryParams, GetTransactionsByLocationParams, IRequest, ReconcileBalanceParams } from '@/common/types'
import { ledgerService } from '../services'

class LedgerController {


    async getPaginatedLedgerEntries(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { accountId } = req.params ?? {}
        const { page, limit, currencyId, startDate, endDate } = req.query ?? {}

        console.log('Date parameters received:', { startDate, endDate })

        // Convert string dates to Date objects if they exist
        // We'll let the service handle the time adjustment for consistent filtering
        const startDateObj = startDate ? new Date(startDate) : undefined
        const endDateObj = endDate ? new Date(endDate) : undefined

        console.log('Converted date objects:', {
            startDateObj: startDateObj?.toISOString(),
            endDateObj: endDateObj?.toISOString()
        })

        // Log if we're dealing with the same day for both start and end date
        if (startDateObj && endDateObj) {
            const sameDay =
                startDateObj.getFullYear() === endDateObj.getFullYear() &&
                startDateObj.getMonth() === endDateObj.getMonth() &&
                startDateObj.getDate() === endDateObj.getDate();

            console.log('Same day selected:', sameDay);
        }

        return await ledgerService.getPaginatedLedgerEntries(accountId, {
            page,
            limit,
            currencyId,
            startDate: startDateObj,
            endDate: endDateObj
        })
    }

    async getAccountStatement(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { accountId } = req.params ?? {}
        const { startDate, endDate, currencyId } = req.query as GetAccountStatementParams

        if (!accountId) {
            throw new Error('Account ID is required')
        }
        if (!startDate || !endDate || !currencyId) {
            throw new Error('Start date, end date, and currency ID are required')
        }

        return await ledgerService.getAccountStatement(accountId, {
            startDate: new Date(startDate),
            endDate: new Date(endDate),
            currencyId
        })
    }

    async reconcileBalance(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { location, currencyId, bankAccountId } = req.body as ReconcileBalanceParams

        if (!location || !currencyId) {
            throw new Error('Location and currency ID are required')
        }

        return await ledgerService.reconcileBalance({
            location,
            currencyId,
            bankAccountId
        })
    }

    async getDailyLedger(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { date } = req.query as GetDailyLedgerParams
        if (!date) {
            throw new Error('Date is required')
        }
        return await ledgerService.getDailyLedger({ date: new Date(date) })
    }

    async getTransactionsByCategory(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { startDate, endDate, categories } = req.query as GetTransactionsByCategoryParams
        if (!startDate || !endDate) {
            throw new Error('Start date and end date are required')
        }
        return await ledgerService.getTransactionsByCategory({
            startDate: new Date(startDate),
            endDate: new Date(endDate),
            categories
        })
    }

    async getTransactionsByLocation(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { startDate, endDate, locations } = req.query as GetTransactionsByLocationParams
        if (!startDate || !endDate) {
            throw new Error('Start date and end date are required')
        }
        return await ledgerService.getTransactionsByLocation({
            startDate: new Date(startDate),
            endDate: new Date(endDate),
            locations
        })
    }

    async getBalanceSheet(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { date } = req.query as GetBalanceSheetParams
        return await ledgerService.getBalanceSheet({ date: date ? new Date(date) : new Date() })
    }

    async getProfitLossStatement(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { startDate, endDate } = req.query as GetProfitLossStatementParams
        if (!startDate || !endDate) {
            throw new Error('Start date and end date are required')
        }
        return await ledgerService.getProfitLossStatement({
            startDate: new Date(startDate),
            endDate: new Date(endDate)
        })
    }

    async getAccountLedgerEntriesForPDF(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { accountId, currencyCode } = req.params ?? {}
        if (!accountId || !currencyCode) {
            throw new Error('Account ID and currency code are required')
        }
        return await ledgerService.getAccountLedgerEntriesForPDF(accountId, currencyCode)
    }

}

export const ledgerController = new LedgerController()
