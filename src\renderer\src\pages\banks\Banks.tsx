import { useState } from 'react'
import { Layout, Input, But<PERSON>, Card } from 'antd'
import { FiPlus } from 'react-icons/fi'
import { BankList } from './components/BankList'
import { AddBank } from './components/AddBank'
import { TransitionWrapper } from '@/renderer/components'

const { Content } = Layout
const { Search } = Input

const Banks = () => {
  const [search, setSearch] = useState('')
  const [isAddMode, setIsAddMode] = useState(false)
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  return (
    <Layout className="relative h-full">
      <TransitionWrapper isVisible={!isAddMode} direction="left">
        <Content className="p-6">
          <Card>
            <div className="mb-6 flex items-center justify-between">
              <div>
                <h1 className="mb-1 text-2xl font-semibold">Bank Accounts</h1>
                <p className="text-gray-500">Manage your bank accounts and transactions</p>
              </div>
              <div className="flex items-center gap-4">
                <Search
                  placeholder="Search banks..."
                  allowClear
                  onChange={(e) => setSearch(e.target.value)}
                  className="w-64"
                />
                <Button
                  type="primary"
                  icon={<FiPlus />}
                  onClick={() => setIsAddMode(true)}
                  className="bg-blue-500 hover:!bg-blue-600"
                >
                  Add Bank Account
                </Button>
              </div>
            </div>

            <BankList searchQuery={search} refreshTrigger={refreshTrigger} />
          </Card>
        </Content>
      </TransitionWrapper>

      <TransitionWrapper isVisible={isAddMode} direction="right">
        <AddBank onClose={() => setIsAddMode(false)} setRefreshTrigger={setRefreshTrigger} />
      </TransitionWrapper>
    </Layout>
  )
}

export default Banks
