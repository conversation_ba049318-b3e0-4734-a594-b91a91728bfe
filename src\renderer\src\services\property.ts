import { http } from './http'
import { Channels } from '@/common'
import type { CreatePropertyData, GetPropertiesParams, UpdatePropertyData } from '@/common/types'

export const createProperty = async (data: CreatePropertyData) => {
    return await http.post(Channels.CREATE_PROPERTY, { body: data })
}

export const updateProperty = async (id: string, data: UpdatePropertyData) => {
    return await http.put(Channels.UPDATE_PROPERTY, {
        params: { id },
        body: data
    })
}

export const deleteProperty = async (id: string, reason: string) => {
    return await http.delete(Channels.DELETE_PROPERTY, {
        params: { id },
        body: { reason }
    })
}

export const getProperty = async (id: string) => {
    return await http.get(Channels.GET_PROPERTY, {
        params: { id }
    })
}

export const getProperties = async (params: Partial<GetPropertiesParams> = {}) => {
    const defaultParams: GetPropertiesParams = {
        page: params.page ?? 1,
        limit: params.limit ?? 20,
        type: params.type,
        isAvailable: params.isAvailable,
        search: params.search
    }

    return await http.get(Channels.GET_PROPERTIES, { query: defaultParams })
}

export const isPropertyAvailable = async (id: string) => {
    return await http.get(Channels.IS_PROPERTY_AVAILABLE, {
        params: { id }
    })
}

export const getPropertyHistory = async (id: string) => {
    return await http.get(Channels.GET_PROPERTY_HISTORY, {
        params: { id }
    })
}

export const getVacantProperties = async () => {
    return await http.get(Channels.GET_VACANT_PROPERTIES)
}
