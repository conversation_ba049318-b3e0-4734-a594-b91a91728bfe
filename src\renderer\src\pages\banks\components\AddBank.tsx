import { Form, Input, Button, InputN<PERSON>ber, App } from 'antd'
import { FiArrowLeft } from 'react-icons/fi'
import { useApi } from '@/renderer/hooks'
import { bankAccountApi } from '@/renderer/services'
// import { useCurrentUser } from '@/renderer/hooks/useCurrentUser'
import type { CreateBankAccountData } from '@/common/types'
import { useBankContext } from '@/renderer/contexts/BankContext'
import { useState } from 'react'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { useTheme } from '@/renderer/contexts'

interface AddBankProps {
  onClose: () => void
  setRefreshTrigger: (value: any) => void
}

export const AddBank = ({ onClose, setRefreshTrigger }: AddBankProps) => {
  const [form] = Form.useForm()
  const [isLoading, setIsLoading] = useState(false)

  const user = useSelector((state: IRootState) => state.user.data)
  const { isDarkMode } = useTheme()

  // const { request: createBank, isLoading } = useApi(bankAccountApi.createBankAccount)

  const { message } = App.useApp()
  const { refreshBanks } = useBankContext()

  const handleSubmit = async (values: CreateBankAccountData) => {
    if (!user?.id) {
      message.error('User session not found')
      return
    }
    setIsLoading(true)
    const response = await bankAccountApi.createBankAccount({
      ...values,
      userId: user?.id
    })

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      setIsLoading(false)
      return
    }
    setIsLoading(false)

    message.success('Bank account created successfully')
    form.resetFields()
    refreshBanks()
    setRefreshTrigger((prev) => prev + 1)
    onClose()
  }

  return (
    <div className="h-full overflow-y-auto p-6">
      <div className="mb-6 flex items-center">
        <Button icon={<FiArrowLeft />} onClick={onClose} className="mr-4" />
        <h1 className="text-2xl font-semibold">Add New Bank Account</h1>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        className={`m-auto max-w-2xl rounded-lg p-6 shadow-lg ${isDarkMode ? 'bg-zinc-900' : 'bg-white'}`}
        initialValues={{
          openingBalance: 0
        }}
      >
        <div className={`mb-6 rounded-lg p-6 shadow-lg ${isDarkMode ? 'bg-black' : 'bg-slate-50'}`}>
          <h2 className="mb-4 text-lg font-medium">Bank Information</h2>
          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name="bankName"
              label="Bank Name"
              rules={[
                { required: true, message: 'Please enter bank name' },
                { min: 3, message: 'Bank name must be at least 3 characters' }
              ]}
            >
              <Input placeholder="Enter bank name" />
            </Form.Item>

            <Form.Item
              name="accountNumber"
              label="Account Number"
              rules={[
                { required: true, message: 'Please enter account number' },
                { min: 10, message: 'Account number must be at least 10 characters' }
              ]}
            >
              <Input placeholder="Enter account number" />
            </Form.Item>
          </div>

          <Form.Item name="branchCode" label="Branch Code">
            <Input placeholder="Enter branch code (optional)" />
          </Form.Item>
        </div>

        <div className={`rounded-lg p-6 shadow-lg ${isDarkMode ? 'bg-black' : 'bg-slate-50'}`}>
          <h2 className="mb-4 text-lg font-medium">Opening Balance</h2>
          <Form.Item
            name="openingBalance"
            label="Initial Balance"
            help="Enter negative value for credit balance"
          >
            <InputNumber
              className="w-full"
              formatter={(value) => `Rs ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={(value) => Number(value!.replace(/Rs\s?|(,*)/g, ''))}
              placeholder="Enter opening balance"
              min={Number(0)}
            />
          </Form.Item>
        </div>

        <div className="mt-8 flex justify-end">
          <Button onClick={onClose} className="mr-4">
            Cancel
          </Button>
          <Button type="primary" htmlType="submit" loading={isLoading}>
            Create Bank Account
          </Button>
        </div>
      </Form>
    </div>
  )
}
