import { http } from './http';
import { Channels } from '@/common/constants';
import { CreateExchangeData, GetExchangeHistoryParams, CreateStructuredExchangeData, TransactionLocation } from '@/common/types';



export const createStructuredExchange = async (data: CreateStructuredExchangeData) => {
    return await http.post(Channels.CREATE_STRUCTURED_EXCHANGE, { body: data });
};

// Helper method to validate bank selection
export const validateBankForCurrency = (currency: string, bankId?: string) => {
    if (currency === 'PKR' && !bankId) {
        throw new Error('Bank account is required for PKR transactions');
    }
    if (currency !== 'PKR' && bankId) {
        throw new Error('Bank accounts can only be used with PKR');
    }
};

// Helper method to get available locations for currency
export const getAvailableLocations = (currency: string) => {
    const locations = ['SMALL_COUNTER', 'VAULT'];
    if (currency === 'PKR') {
        locations.push('BANK_ACCOUNT');
    }
    return locations;
};


export const getExchangeById = async (id: string) => {
    return await http.get(Channels.GET_EXCHANGE_BY_ID, { params: { id } });
};

export const getExchangeHistory = async (params: Partial<GetExchangeHistoryParams> = {}) => {
    return await http.get(Channels.GET_EXCHANGE_HISTORY, { query: params });
};

export const getExchangeStatistics = async () => {
    return await http.get(Channels.GET_EXCHANGE_STATISTICS);
};

export const getDailyExchangeSummary = async (date: Date) => {
    return await http.get(Channels.GET_DAILY_EXCHANGE_SUMMARY, {
        query: { date: date.toISOString() }
    });
};

export const getMonthlyExchangeReport = async (month: Date) => {
    return await http.get(Channels.GET_MONTHLY_EXCHANGE_REPORT, {
        query: { month: month.toISOString() }
    });
};

export const getMostExchangedCurrencies = async (limit?: number) => {
    return await http.get(Channels.GET_MOST_EXCHANGED_CURRENCIES, {
        query: { limit }
    });
};

export const exchangeBetweenLocations = async (data: CreateExchangeData) => {
    return await http.post(Channels.EXCHANGE_BETWEEN_LOCATIONS, { body: data });
};

export const getLocationExchangeHistory = async (location: TransactionLocation) => {
    return await http.get(Channels.GET_LOCATION_EXCHANGE_HISTORY, {
        params: { location }
    });
};

export interface LocationBalanceResponse {
    location: TransactionLocation;
    currency: string;
    balance: number;
    bankName?: string;
    bankAccountNumber?: string;
}

export const getLocationBalance = async (
    location: TransactionLocation,
    currency: string,
    bankId?: string
) => {
    return await http.get(Channels.GET_LOCATION_BALANCE, {
        query: { location, currency, bankId }
    });
};