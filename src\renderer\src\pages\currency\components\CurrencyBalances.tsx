import { Card, Select, Row, Col, Statistic, Spin, Table, Typography } from 'antd'
import { useEffect, useState } from 'react'
import { useApi } from '@/renderer/hooks'
import { currencyApi } from '@/renderer/services'
import type { Currency, CurrencyBalancesResponse } from '@/common/types'
import { formatCurrency } from '@/renderer/utils'

export const CurrencyBalances = () => {
  const [selectedCurrency, setSelectedCurrency] = useState<string>('PKR')

  const {
    data: currencies,
    isLoading: loadingCurrencies,
    request: fetchCurrencies
  } = useApi<Currency[], []>(currencyApi.getAllCurrencies)

  const {
    data: balances,
    isLoading: loadingBalances,
    request: fetchBalances
  } = useApi<CurrencyBalancesResponse, [string]>(currencyApi.getCurrencyBalances)

  useEffect(() => {
    fetchCurrencies()
    fetchBalances(selectedCurrency)
  }, [selectedCurrency])

  if (loadingCurrencies) return <Spin />

  return (
    <div className="flex flex-col gap-6">
      <div className="flex gap-4">
        <Select value={selectedCurrency} onChange={setSelectedCurrency} style={{ width: 200 }}>
          {currencies?.map((currency) => (
            <Select.Option key={currency.code} value={currency.code}>
              {currency.name} ({currency.code})
            </Select.Option>
          ))}
        </Select>
      </div>

      <Row gutter={[16, 16]}>
        {/* <Col span={6}>
          <Card>
            <Statistic
              title="Small Counter Balance"
              value={balances?.smallCounter ?? 0}
              loading={loadingBalances}
              precision={2}
              formatter={(value) => formatCurrency(value as number, selectedCurrency)}
            />
          </Card>
        </Col> */}
        <Col span={8}>
          <Card>
            <Statistic
              title="Cash Vault Balance"
              value={balances?.cashVault ?? 0}
              loading={loadingBalances}
              precision={2}
              formatter={(value) => formatCurrency(value as number, selectedCurrency)}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="Bank Accounts Balance"
              value={balances?.bankAccounts ?? 0}
              loading={loadingBalances}
              precision={2}
              formatter={(value) => formatCurrency(value as number, selectedCurrency)}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="Total System Balance"
              value={balances?.totalSystemBalance ?? 0}
              loading={loadingBalances}
              precision={2}
              formatter={(value) => formatCurrency(value as number, selectedCurrency)}
            />
          </Card>
        </Col>
      </Row>

      <Card title="Account Balances">
        <Table
          size="small"
          dataSource={balances?.accountBalances}
          rowKey="accountName"
          pagination={false}
          scroll={{ y: 400 }}
          columns={[
            {
              title: 'Account Name',
              dataIndex: 'accountName',
              key: 'accountName',
              fixed: 'left',
              width: 250
            },
            {
              title: `Balance (${selectedCurrency})`,
              dataIndex: 'balance',
              key: 'balance',
              align: 'right',
              width: 200,
              render: (balance: number) => (
                <Typography.Text
                  style={{
                    color:
                      balance > 0
                        ? '#52c41a' // green
                        : balance < 0
                          ? '#f5222d' // red
                          : '#8c8c8c' // gray
                  }}
                >
                  {formatCurrency(balance, selectedCurrency)}
                </Typography.Text>
              )
            }
          ]}
          sticky
          virtual
          summary={(data) => {
            const total = data?.reduce((sum, row) => sum + (row.balance || 0), 0) ?? 0
            return (
              <Table.Summary fixed="bottom">
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0}>
                    <strong>Total</strong>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={1} align="right">
                    <Typography.Text
                      strong
                      style={{
                        color:
                          total > 0
                            ? '#52c41a' // green
                            : total < 0
                              ? '#f5222d' // red
                              : '#8c8c8c' // gray
                      }}
                    >
                      {formatCurrency(total, selectedCurrency)}
                    </Typography.Text>
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              </Table.Summary>
            )
          }}
        />
      </Card>
    </div>
  )
}
