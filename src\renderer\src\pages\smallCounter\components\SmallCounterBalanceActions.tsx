import { Button, Modal, Form, InputNumber, Select, Input, message } from 'antd'
import { useState } from 'react'
import { useApi } from '@/renderer/hooks'
import { smallCounterApi } from '@/renderer/services'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'

export const SmallCounterBalanceActions = () => {
  const [adjustForm] = Form.useForm()
  const [transferForm] = Form.useForm()
  const [isAdjustModalOpen, setIsAdjustModalOpen] = useState(false)
  const [isTransferModalOpen, setIsTransferModalOpen] = useState(false)

  const user = useSelector((state: IRootState) => state.user.data)

  const {
    request: adjustBalance,
    error: adjustError,
    errorMessage: adjustErrorMessage
  } = useApi(smallCounterApi.adjustBalance)

  const {
    request: transferToVault,
    error: transferError,
    errorMessage: transferErrorMessage
  } = useApi(smallCounterApi.transferToVault)

  const handleAdjust = async (values: any) => {
    if (!user?.id) {
      message.error('User session not found')
      return
    }

    await adjustBalance(values.currency, values.amount, values.reason, user.id)
    if (!adjustError) {
      message.success('Balance adjusted successfully')
      setIsAdjustModalOpen(false)
      adjustForm.resetFields()
    } else {
      message.error(adjustErrorMessage)
    }
  }

  const handleTransfer = async (values: any) => {
    if (!user?.id) {
      message.error('User session not found')
      return
    }

    await transferToVault(values.currency, values.amount, user.id)
    if (!transferError) {
      message.success('Amount transferred to vault successfully')
      setIsTransferModalOpen(false)
      transferForm.resetFields()
    } else {
      message.error(transferErrorMessage)
    }
  }

  return (
    <div className="mt-4 flex gap-4">
      <Button type="primary" onClick={() => setIsAdjustModalOpen(true)}>
        Adjust Balance
      </Button>

      <Button onClick={() => setIsTransferModalOpen(true)}>Transfer to Vault</Button>

      <Modal
        title="Adjust Balance"
        open={isAdjustModalOpen}
        onOk={adjustForm.submit}
        onCancel={() => setIsAdjustModalOpen(false)}
      >
        <Form form={adjustForm} onFinish={handleAdjust} layout="vertical">
          <Form.Item name="currency" label="Currency" rules={[{ required: true }]}>
            <Select>
              <Select.Option value="PKR">PKR</Select.Option>
              <Select.Option value="USD">USD</Select.Option>
              <Select.Option value="AED">AED</Select.Option>
              <Select.Option value="AFN">AFN</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item name="amount" label="Amount" rules={[{ required: true }]}>
            <InputNumber
              style={{ width: '100%' }}
              formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
            />
          </Form.Item>

          <Form.Item name="reason" label="Reason" rules={[{ required: true }]}>
            <Input.TextArea />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="Transfer to Vault"
        open={isTransferModalOpen}
        onOk={transferForm.submit}
        onCancel={() => setIsTransferModalOpen(false)}
      >
        <Form form={transferForm} onFinish={handleTransfer} layout="vertical">
          <Form.Item name="currency" label="Currency" rules={[{ required: true }]}>
            <Select>
              <Select.Option value="PKR">PKR</Select.Option>
              <Select.Option value="USD">USD</Select.Option>
              <Select.Option value="AED">AED</Select.Option>
              <Select.Option value="AFN">AFN</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item name="amount" label="Amount" rules={[{ required: true }]}>
            <InputNumber
              style={{ width: '100%' }}
              formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}
