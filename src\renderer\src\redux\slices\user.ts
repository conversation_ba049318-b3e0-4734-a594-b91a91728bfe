import { ILicense, IUser } from "@/common";
import { createSlice } from "@reduxjs/toolkit";

type IInitialState = {
    data: IUser | null;
    license: ILicense | null;
    isAuthenticated: boolean;
}

const initialState: IInitialState = {
    data: null,
    license: null,
    isAuthenticated: false,
}

const userSlice = createSlice({
    name: "user",
    initialState,
    reducers: {
        init: (state, action) => {
            state.data = action.payload;
            state.isAuthenticated = true;
        },
        initLicense: (state, action) => {
            state.license = action.payload;
        },
        logout: (state) => {
            state.data = null;
            state.isAuthenticated = false;
            state.license = null
        }
    }
});

export const userActions = userSlice.actions;
export const userReducer = userSlice.reducer;