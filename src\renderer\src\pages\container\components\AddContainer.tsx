import { Form, Input, DatePicker, InputNumber, Button, Card, Select, App, theme } from 'antd'
import { useState } from 'react'
import { FiArrowLeft, FiPlus } from 'react-icons/fi'
import { containerApi } from '@/renderer/services'
import { StockItemsList } from './StockItemsList'
import type { CreateContainerData } from '@/common/types'
import { usePartnerContext, useTheme } from '@/renderer/contexts'
import { handleDatePickerValue } from '@/renderer/utils'

interface AddContainerProps {
  onClose: () => void
  setRefreshTrigger: (value: any) => void
}

export const AddContainer = ({ onClose, setRefreshTrigger }: AddContainerProps) => {
  const [form] = Form.useForm()

  const [cars, setCars] = useState<any[]>([])
  const [carParts, setCarParts] = useState<any[]>([])
  const [electronics, setElectronics] = useState<any[]>([])
  const [scraps, setScraps] = useState<any[]>([])

  const [isLoading, setIsLoading] = useState(false)

  const { partners } = usePartnerContext()

  const { message } = App.useApp()

  const { isDarkMode } = useTheme()

  // const { request: createContainer, isLoading, error } = useApi(containerApi.createContainer)

  const handleSubmit = async (values: any) => {
    setIsLoading(true)

    const containerData: CreateContainerData = {
      ...values,
      openedAt: handleDatePickerValue(values.openedAt?.toDate()),
      cars,
      carParts,
      electronics,
      scraps,
      createdById: 'currentUserId' // Replace with actual user ID
    }

    const response = await containerApi.createContainer(containerData)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      setIsLoading(false)
      return
    }

    message.success('Container created successfully')
    form.resetFields()
    setCars([])
    setCarParts([])
    setElectronics([])
    setScraps([])
    setRefreshTrigger((prev) => prev + 1)
    setIsLoading(false)
    onClose()
  }

  const { token } = theme.useToken()

  return (
    <div className="h-full overflow-y-auto p-6">
      <div className="mb-6 flex items-center">
        <Button icon={<FiArrowLeft />} onClick={onClose} className="mr-4" />
        <h1 className="text-2xl font-semibold">Add New Container</h1>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        style={{ backgroundColor: token.colorBgContainer }}
        className={`m-auto max-w-5xl rounded-lg p-6 shadow-lg`}
      >
        <div className="grid grid-cols-2 gap-6">
          <Form.Item name="containerNumber" label="Container Number" rules={[{ required: true }]}>
            <Input />
          </Form.Item>

          <Form.Item name="openedAt" label="Opening Date" rules={[{ required: true }]}>
            <DatePicker className="w-full" />
          </Form.Item>

          <Form.Item name="driverExpense" label="Driver Expense" rules={[{ required: true }]}>
            <InputNumber
              className="w-full"
              formatter={(value) => `Rs ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={(value) => Number(value!.replace(/Rs\s?|(,*)/g, ''))}
            />
          </Form.Item>

          <Form.Item name="taxes" label="Taxes" rules={[{ required: true }]}>
            <InputNumber
              className="w-full"
              formatter={(value) => `Rs ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={(value) => Number(value!.replace(/Rs\s?|(,*)/g, ''))}
            />
          </Form.Item>

          <Form.Item name="containerCost" label="Container Cost" rules={[{ required: true }]}>
            <InputNumber
              className="w-full"
              formatter={(value) => `Rs ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={(value) => Number(value!.replace(/Rs\s?|(,*)/g, ''))}
            />
          </Form.Item>

          <Form.Item name="routeExpense" label="Route Expense" rules={[{ required: true }]}>
            <InputNumber
              className="w-full"
              formatter={(value) => `Rs ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={(value) => Number(value!.replace(/Rs\s?|(,*)/g, ''))}
            />
          </Form.Item>

          <Form.Item name="fieldRent" label="Field Rent" rules={[{ required: true }]}>
            <InputNumber
              className="w-full"
              formatter={(value) => `Rs ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={(value) => Number(value!.replace(/Rs\s?|(,*)/g, ''))}
            />
          </Form.Item>

          <Form.Item name="partnerId" label="Partner">
            <Select
              placeholder="Select partner"
              allowClear
              options={partners}
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>
        </div>

        <div className="mt-8 grid grid-cols-2 gap-6">
          <StockItemsList
            title="Cars"
            items={cars}
            onAdd={(item) => setCars([...cars, item])}
            onRemove={(index) => setCars(cars.filter((_, i) => i !== index))}
            type="car"
          />

          <StockItemsList
            title="Car Parts"
            items={carParts}
            onAdd={(item) => setCarParts([...carParts, item])}
            onRemove={(index) => setCarParts(carParts.filter((_, i) => i !== index))}
            type="carPart"
          />

          <StockItemsList
            title="Electronics"
            items={electronics}
            onAdd={(item) => setElectronics([...electronics, item])}
            onRemove={(index) => setElectronics(electronics.filter((_, i) => i !== index))}
            type="electronic"
          />

          <StockItemsList
            title="Scraps"
            items={scraps}
            onAdd={(item) => setScraps([...scraps, item])}
            onRemove={(index) => setScraps(scraps.filter((_, i) => i !== index))}
            type="scrap"
          />
        </div>

        <div className="mt-8 flex justify-end">
          <Button onClick={onClose} className="mr-4">
            Cancel
          </Button>
          <Button type="primary" htmlType="submit" loading={isLoading}>
            Create Container
          </Button>
        </div>
      </Form>
    </div>
  )
}
