import { InitializeCashVaultData, IRequest, GetCashVaultStatementParams } from '@/common/types';
import { cashVaultService } from '../services';

class CashVaultController {
    async initializeCashVault(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        return await cashVaultService.initializeCashVault(req.body as InitializeCashVaultData);
    }

    async getCashVaultBalances() {
        return await cashVaultService.getCashVaultBalances();
    }

    async getBalanceByCurrency(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { currency } = req.params ?? {};
        if (!currency) {
            throw new Error('Currency is required');
        }
        return await cashVaultService.getBalanceByCurrency(currency);
    }

    async adjustBalance(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { currency, adjustment, reason, userId } = req.body ?? {};
        if (!currency || !adjustment || !reason || !userId) {
            throw new Error('Missing required fields');
        }
        return await cashVaultService.adjustBalance(
            currency,
            Number(adjustment),
            reason,
            userId
        );
    }

    async reconcileBalance(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { currency } = req.params ?? {};
        if (!currency) {
            throw new Error('Currency is required');
        }
        return await cashVaultService.reconcileBalance(currency);
    }

    async getAllTransactions(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { page, pageSize, startDate, endDate, sortOrder } = req.query ?? {};
        return await cashVaultService.getAllTransactions(
            Number(page) || 1,
            Number(pageSize) || 20,
            startDate ? new Date(startDate) : undefined,
            endDate ? new Date(endDate) : undefined,
            sortOrder as 'asc' | 'desc' || 'asc' // Default to ascending order if not specified
        );
    }

    async generateCashVaultStatement(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { currencyCode } = req.params ?? {};
        const { startDate, endDate, page, pageSize } = req.query ?? {};

        if (!currencyCode || !startDate || !endDate) {
            throw new Error('Currency code, start date, and end date are required');
        }

        return await cashVaultService.generateCashVaultStatement(currencyCode, {
            startDate: new Date(startDate),
            endDate: new Date(endDate),
            page: page ? Number(page) : undefined,
            pageSize: pageSize ? Number(pageSize) : undefined
        });
    }
}

export const cashVaultController = new CashVaultController();