import { useEffect } from 'react'
import { Typography, Space, Button, Alert, Skeleton, Row, Col, Statistic, Progress } from 'antd'
import { useApi } from '@/renderer/hooks'
import { dashboardApi } from '@/renderer/services'
import { AccountStats as AccountStatsType } from '@/common/types'
import { FaUsers, FaSync } from 'react-icons/fa'
import { useTheme } from '@/renderer/contexts'

const { Title } = Typography

const AccountStats = () => {
  const { isDarkMode } = useTheme()

  const {
    data,
    isLoading,
    error,
    request: fetchAccountStats
  } = useApi<AccountStatsType, []>(dashboardApi.getAccountStats)

  useEffect(() => {
    fetchAccountStats()
  }, [])

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <FaUsers className="text-2xl text-orange-600" />
            <Title level={5} className="!mb-0">
              Account Statistics
            </Title>
          </Space>
          <Button icon={<FaSync />} loading />
        </Space>
        <Skeleton active paragraph={{ rows: 4 }} />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <FaUsers className="text-2xl text-orange-600" />
            <Title level={5} className="!mb-0">
              Account Statistics
            </Title>
          </Space>
          <Button icon={<FaSync />} onClick={fetchAccountStats} />
        </Space>
        <Alert
          message="Error"
          description="Failed to load account statistics"
          type="error"
          showIcon
        />
      </div>
    )
  }

  if (!data) return null

  return (
    <div className="flex flex-col gap-4">
      <Space className="flex justify-between">
        <Space>
          <FaUsers className="text-2xl text-orange-600" />
          <Title level={5} className="!mb-0">
            Account Statistics
          </Title>
        </Space>
        <Button icon={<FaSync />} onClick={fetchAccountStats} />
      </Space>

      <div className={`rounded-lg p-4 ${isDarkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Statistic title="Total Accounts" value={data.total} />
          </Col>
          <Col span={12}>
            <Statistic title="Active Customers" value={data.customers} />
          </Col>
          <Col span={24}>
            <Title level={5} className="mb-2">
              Account Distribution
            </Title>
            <Row gutter={[16, 16]}>
              <Col span={8}>
                <Statistic title="Customers" value={data.customers} />
                <Progress percent={Math.round((data.customers / data.total) * 100)} size="small" />
              </Col>
              <Col span={8}>
                <Statistic title="Tenants" value={data.tenants} />
                <Progress percent={Math.round((data.tenants / data.total) * 100)} size="small" />
              </Col>
              <Col span={8}>
                <Statistic title="Both" value={data.both} />
                <Progress percent={Math.round((data.both / data.total) * 100)} size="small" />
              </Col>
            </Row>
          </Col>
        </Row>
      </div>
    </div>
  )
}

export default AccountStats
