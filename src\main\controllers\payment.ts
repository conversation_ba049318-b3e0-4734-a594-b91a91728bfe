import { IRequest } from '@/common/types';
import { paymentService } from '../services';
import {
    CreatePaymentData,
    DeletePaymentParams,
    GetPaymentsParams,
    TransferBetweenLocationsData,
    CreateAccountTransferData,
    DeleteTransferData,
    GetTransfersParams
} from '@/common/types';

class PaymentController {
    async createPayment(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as CreatePaymentData;

        // Validate required fields
        if (!data.accountId) throw new Error('Account ID is required');
        if (!data.amount || data.amount <= 0) throw new Error('Valid amount is required');
        if (!data.currencyCode) throw new Error('Currency code is required');
        if (!data.userId) throw new Error('User ID is required');
        if (!data.sourceLocation) throw new Error('Source location is required');
        if (!data.destinationType) throw new Error('Destination type is required');

        // Validate bank transactions (only PKR allowed)
        if ((data.sourceLocation === 'BANK_ACCOUNT' || data.destinationType === 'BANK_ACCOUNT') && data.currencyCode !== 'PKR') {
            throw new Error('Only PKR can be used with bank accounts');
        }

        // Validate bank account IDs
        if (data.sourceLocation === 'BANK_ACCOUNT' && !data.sourceBankId) {
            throw new Error('Bank account ID required for bank source');
        }
        if (data.destinationType === 'BANK_ACCOUNT' && !data.destinationBankId) {
            throw new Error('Bank account ID required for bank destination');
        }

        return await paymentService.createPayment(data);
    }

    async deletePayment(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as DeletePaymentParams;

        // Validate required fields
        if (!data.id) throw new Error('Payment ID is required');
        if (!data.reason) throw new Error('Delete reason is required');
        if (!data.userId) throw new Error('User ID is required');

        return await paymentService.deletePayment(data);
    }

    async getPayments(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const params = req.query as GetPaymentsParams;

        // Validate pagination
        if (params.page && params.page < 1) throw new Error('Page must be greater than 0');
        if (params.limit && params.limit < 1) throw new Error('Limit must be greater than 0');

        // Validate date range
        if (params.startDate && params.endDate) {
            const start = new Date(params.startDate);
            const end = new Date(params.endDate);
            if (isNaN(start.getTime())) throw new Error('Invalid start date');
            if (isNaN(end.getTime())) throw new Error('Invalid end date');
            if (start > end) throw new Error('Start date cannot be after end date');
        }

        return await paymentService.getPayments(params);
    }

    async getPaymentSummaryByAccount(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { accountId, startDate, endDate } = req.query || {};

        // Validate required fields
        if (!accountId) throw new Error('Account ID is required');

        // Validate date range if provided
        if (startDate && endDate) {
            const start = new Date(startDate as string);
            const end = new Date(endDate as string);
            if (isNaN(start.getTime())) throw new Error('Invalid start date');
            if (isNaN(end.getTime())) throw new Error('Invalid end date');
            if (start > end) throw new Error('Start date cannot be after end date');
        }

        return await paymentService.getPaymentSummaryByAccount(
            accountId as string,
            startDate ? new Date(startDate as string) : undefined,
            endDate ? new Date(endDate as string) : undefined
        );
    }

    async getDailyPaymentReport(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { date } = req.query || {};

        // Validate date
        if (!date) throw new Error('Date is required');
        const reportDate = new Date(date as string);
        if (isNaN(reportDate.getTime())) throw new Error('Invalid date');

        return await paymentService.getDailyPaymentReport(reportDate);
    }

    async getOutstandingPayments(_event: Electron.IpcMainInvokeEvent) {
        return await paymentService.getOutstandingPayments();
    }

    async getPaymentStatistics(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { startDate, endDate } = req.query || {};

        // Validate required fields
        if (!startDate || !endDate) throw new Error('Start and end dates are required');

        // Validate date range
        const start = new Date(startDate as string);
        const end = new Date(endDate as string);
        if (isNaN(start.getTime())) throw new Error('Invalid start date');
        if (isNaN(end.getTime())) throw new Error('Invalid end date');
        if (start > end) throw new Error('Start date cannot be after end date');

        return await paymentService.getPaymentStatistics(start, end);
    }

    async transferBetweenLocations(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as TransferBetweenLocationsData;

        console.log(data)

        // Validate required fields
        if (!data.fromLocation) throw new Error('Source location is required');
        if (!data.toLocation) throw new Error('Destination location is required');
        if (!data.amount || data.amount <= 0) throw new Error('Valid amount is required');
        if (!data.currencyCode) throw new Error('Currency code is required');
        if (!data.userId) throw new Error('User ID is required');

        // Validate locations
        if (data.fromLocation === data.toLocation) {
            if (data.fromLocation !== 'BANK_ACCOUNT' || data.toLocation !== 'BANK_ACCOUNT') {
                throw new Error('Source and destination locations cannot be the same');
            }
        }

        if (data.fromLocation === 'BANK_ACCOUNT' && data.toLocation === 'BANK_ACCOUNT') {
            if (data.fromBankId === data.toBankId) {
                throw new Error('Can not transfer to same bank account');
            }
        }


        // Validate bank transactions
        if ((data.fromLocation === 'BANK_ACCOUNT' || data.toLocation === 'BANK_ACCOUNT') && data.currencyCode !== 'PKR') {
            throw new Error('Only PKR can be used with bank accounts');
        }

        // Validate bank IDs
        if (data.fromLocation === 'BANK_ACCOUNT' && !data.fromBankId) {
            throw new Error('Source bank ID required for bank transfer');
        }
        if (data.toLocation === 'BANK_ACCOUNT' && !data.toBankId) {
            throw new Error('Destination bank ID required for bank transfer');
        }

        return await paymentService.transferBetweenLocations(data);
    }

    async transferBetweenAccounts(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as CreateAccountTransferData;

        if (!data.sourceAccountId || !data.destinationAccountId || !data.amount || !data.currencyCode || !data.userId) {
            throw new Error('Missing required fields');
        }

        if (data.amount <= 0) {
            throw new Error('Amount must be greater than 0');
        }

        return await paymentService.transferBetweenAccounts(data);
    }

    async getTransfers(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { page = 1, limit = 10, startDate, endDate, search, accountId, currencyCode, type, includeDeleted, orderBy } = req.query ?? {};

        return await paymentService.getTransfers({
            page: Number(page),
            limit: Number(limit),
            startDate: startDate ? new Date(startDate as string) : undefined,
            endDate: endDate ? new Date(endDate as string) : undefined,
            search: search as string,
            accountId: accountId as string,
            currencyCode: currencyCode as string,
            type: type as 'SENT' | 'RECEIVED' | 'ALL',
            includeDeleted,
            orderBy: (orderBy as 'asc' | 'desc') || 'desc'
        });
    }

    async deleteTransfer(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as DeleteTransferData;

        console.log(data)

        if (!data.ledgerEntryId || !data.userId || !data.reason) {
            throw new Error('Missing required fields');
        }

        return await paymentService.deleteTransfer(data);
    }
}

export const paymentController = new PaymentController();
