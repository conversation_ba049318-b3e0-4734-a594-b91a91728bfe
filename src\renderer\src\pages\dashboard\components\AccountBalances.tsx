import { useEffect } from 'react'
import { Typography, Space, Button, Alert, Skeleton, Row, Col, Table } from 'antd'
import { useApi } from '@/renderer/hooks'
import { dashboardApi } from '@/renderer/services'
import { AccountBalanceSummary } from '@/common/types'
import { FaWallet, FaSync } from 'react-icons/fa'
import { formatCurrency } from '@/renderer/utils'
import { useTheme } from '@/renderer/contexts'

const { Title } = Typography

const AccountBalances = () => {
  const { isDarkMode } = useTheme()

  const {
    data,
    isLoading,
    error,
    request: fetchAccountBalances
  } = useApi<AccountBalanceSummary[], []>(dashboardApi.getAccountBalances)

  useEffect(() => {
    fetchAccountBalances()
  }, [])

  const columns = [
    {
      title: 'Currency',
      dataIndex: 'currency',
      key: 'currency'
    },
    {
      title: 'Receivables',
      dataIndex: 'receivables',
      key: 'receivables',
      align: 'right' as const,
      render: (value: number, record: any) => formatCurrency(value, record.currency)
    },
    {
      title: 'Payables',
      dataIndex: 'payables',
      key: 'payables',
      align: 'right' as const,
      render: (value: number, record: any) => formatCurrency(value, record.currency)
    },
    {
      title: 'Net Balance',
      dataIndex: 'netBalance',
      key: 'netBalance',
      align: 'right' as const,
      render: (value: number, record: any) => formatCurrency(value, record.currency)
    }
  ]

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <FaWallet className="text-2xl text-red-600" />
            <Title level={5} className="!mb-0">
              Account Balances
            </Title>
          </Space>
          <Button icon={<FaSync />} loading />
        </Space>
        <Skeleton active paragraph={{ rows: 4 }} />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <FaWallet className="text-2xl text-red-600" />
            <Title level={5} className="!mb-0">
              Account Balances
            </Title>
          </Space>
          <Button icon={<FaSync />} onClick={fetchAccountBalances} />
        </Space>
        <Alert
          message="Error"
          description="Failed to load account balances"
          type="error"
          showIcon
        />
      </div>
    )
  }

  if (!data) return null

  return (
    <div className="flex flex-col gap-4">
      <Space className="flex justify-between">
        <Space>
          <FaWallet className="text-2xl text-red-600" />
          <Title level={5} className="!mb-0">
            Account Balances
          </Title>
        </Space>
        <Button icon={<FaSync />} onClick={fetchAccountBalances} />
      </Space>

      <div className={`rounded-lg p-4 ${isDarkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Table
              dataSource={data}
              columns={columns}
              pagination={false}
              size="small"
              className={isDarkMode ? 'dark-table' : ''}
            />
          </Col>
        </Row>
      </div>
    </div>
  )
}

export default AccountBalances
