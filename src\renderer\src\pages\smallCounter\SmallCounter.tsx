import { Ta<PERSON>, Card, Spin } from 'antd'
import { useApi } from '@/renderer/hooks'
import { smallCounterApi } from '@/renderer/services'
import { useEffect, useState } from 'react'
import {
  SmallCounterBalanceDisplay,
  SmallCounterBalanceActions,
  SmallCounterTransactionHistory,
  SmallCounterInitialize
} from './components'

const SmallCounter = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const {
    error: smallCounterError,
    request: smallCounterRequest,
    isLoading: smallCounterLoading
  } = useApi(smallCounterApi.getBalances)

  useEffect(() => {
    smallCounterRequest()
  }, [refreshTrigger])

  if (smallCounterLoading) {
    return <Spin />
  }

  const smallCounterInitialized = !smallCounterError

  return (
    <div className="flex h-full flex-col gap-6 p-6">
      <div>
        {!smallCounterInitialized ? (
          <SmallCounterInitialize setRefreshTrigger={setRefreshTrigger} />
        ) : (
          <Card title="Small Counter">
            <SmallCounterBalanceDisplay />
            <SmallCounterBalanceActions />
          </Card>
        )}
      </div>

      {smallCounterInitialized && (
        <Tabs
          type="card"
          items={[
            {
              key: 'transactions',
              label: 'Transactions',
              children: <SmallCounterTransactionHistory />
            }
          ]}
        />
      )}
    </div>
  )
}

export default SmallCounter
