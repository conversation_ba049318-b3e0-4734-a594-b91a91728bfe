import { propertyService } from '../services/property'
import type { CreatePropertyData, GetPropertiesParams, IRequest, UpdatePropertyData } from '@/common/types'

class PropertyController {
    async createProperty(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as CreatePropertyData
        return propertyService.createProperty(data)
    }

    async updateProperty(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const id = req.params?.id as string
        if (!id) throw new Error('Property ID is required')

        const data = req.body as UpdatePropertyData
        return propertyService.updateProperty(id, data)
    }

    async deleteProperty(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const id = req.params?.id as string
        if (!id) throw new Error('Property ID is required')

        const reason = req.body?.reason as string
        if (!reason) throw new Error('Delete reason is required')

        return propertyService.deleteProperty(id, reason)
    }

    async getProperty(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const id = req.params?.id as string
        if (!id) throw new Error('Property ID is required')

        return propertyService.getProperty(id)
    }

    async getProperties(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const params = req.query as GetPropertiesParams
        return propertyService.getProperties(params)
    }

    async isPropertyAvailable(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const id = req.params?.id as string
        if (!id) throw new Error('Property ID is required')

        return propertyService.isPropertyAvailable(id)
    }

    async getPropertyHistory(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const id = req.params?.id as string
        if (!id) throw new Error('Property ID is required')

        return propertyService.getPropertyHistory(id)
    }

    async getVacantProperties(_event: Electron.IpcMainInvokeEvent, _req: IRequest) {
        return propertyService.getVacantProperties()
    }
}

export const propertyController = new PropertyController();
