import { Card, Descriptions, Table, Tag, Spin } from 'antd'
import dayjs from 'dayjs'
import { useState, useEffect } from 'react'
import { formatCurrencyWithoutSymbol } from '@/renderer/utils'
import { accountsApi } from '@/renderer/services'

interface AccountDetailsCardProps {
  subTitle?: string
  accountId?: string
  account?: {
    id: string
    name: string
    type: string
    phoneNumber: string
    address: string
    createdAt: Date
    balances: Array<{
      balance: number
      currency: {
        code: string
      }
    }>
  }
}

export const AccountDetailsCard = ({
  subTitle = '',
  accountId,
  account: externalAccount
}: AccountDetailsCardProps) => {
  const [account, setAccount] = useState<any>(externalAccount || null)
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch account data if accountId is provided and no external account is passed
  useEffect(() => {
    const fetchAccountData = async () => {
      if (!accountId) return

      setLoading(true)
      setError(null)

      try {
        const response = await accountsApi.getAccountById(accountId)
        if (response.error?.error || response.data?.error) {
          const errorMsg =
            response.error?.message ||
            response.data?.error?.message ||
            'Failed to fetch account details'
          console.error(errorMsg)
          setError(errorMsg)
          return
        }

        setAccount(response.data.data)
      } catch (error) {
        console.error('Error fetching account data:', error)
        setError('Failed to fetch account details')
      } finally {
        setLoading(false)
      }
    }

    // If an external account is provided, use it
    if (externalAccount) {
      setAccount(externalAccount)
      return
    }

    // Otherwise, fetch the account data if we have an ID
    if (accountId) {
      fetchAccountData()
    } else {
      setAccount(null)
    }
  }, [accountId, externalAccount])

  // Handle loading state
  if (loading) {
    return (
      <Card size="small" title={`Account Details - ${subTitle}`} className="mb-4 shadow-lg">
        <div className="flex h-40 items-center justify-center">
          <Spin />
        </div>
      </Card>
    )
  }

  // Handle error state
  if (error) {
    return (
      <Card size="small" title={`Account Details - ${subTitle}`} className="mb-4 shadow-lg">
        <div className="flex h-40 items-center justify-center text-red-500">{error}</div>
      </Card>
    )
  }

  // Handle no account state
  if (!account) {
    return null
    // return (
    //   <Card size="small" title={`Account Details - ${subTitle}`} className="mb-4 shadow-lg">
    //     <div className="flex h-40 items-center justify-center text-gray-500">
    //       No account selected
    //     </div>
    //   </Card>
    // )
  }

  const balanceColumns = [
    {
      title: 'Currency',
      dataIndex: ['currency', 'code'],
      key: 'currency'
    },
    {
      title: 'Balance',
      dataIndex: 'balance',
      key: 'balance',
      render: (balance: number) => {
        const color = balance > 0 ? 'green' : balance < 0 ? 'red' : 'default'
        return <Tag color={color}>{formatCurrencyWithoutSymbol(balance || 0)}</Tag>
      }
    }
  ]

  return (
    <Card size="small" title={`Account Details - ${subTitle}`} className="mb-4 shadow-lg">
      <Descriptions column={2} className="mb-4">
        <Descriptions.Item label="Name">{account.name}</Descriptions.Item>
        <Descriptions.Item label="Type">{account.type}</Descriptions.Item>
        <Descriptions.Item label="Phone">{account.phoneNumber}</Descriptions.Item>
        <Descriptions.Item label="Address">{account.address}</Descriptions.Item>
        <Descriptions.Item label="Created At">
          {dayjs(account.createdAt).format('DD/MM/YYYY')}
        </Descriptions.Item>
      </Descriptions>

      <div>
        <h4 className="mb-2 font-medium">Account Balances</h4>
        <Table
          dataSource={account.balances}
          columns={balanceColumns}
          pagination={false}
          size="small"
          rowKey={(record: any) => record.currency?.code || 'unknown'}
        />
      </div>
    </Card>
  )
}
