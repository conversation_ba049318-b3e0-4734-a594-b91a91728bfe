import { Roles } from '../constants'

export type IObject = {
  [key: string | number]: any
}

export type IServerResponse = {
  data: null | any
  error: {
    message: string
    error: null | any
  }
}

export type IRoles = {
  ADMIN: 'ADMIN'
  NORMAL: 'NORMAL'
}

export type IMenu = {
  key: string
  label: string
  roles: `${Roles}`[]
  icon: React.ReactNode
}

type IRoleKeys = keyof IRoles

export type IUser = {
  id: string
  username: string
  name: string
  password?: string
  role: IRoles[IRoleKeys]
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export type IRequest = {
  params?: IObject
  query?: IObject
  body?: IObject
}


export * from './container'
export * from './user'
export * from './partner'
export * from './stock'
export * from './account'
export * from './exchange'
export * from './smallCounter'
export * from './cashVault'
export * from './currency'
export * from './bankAccount'
export * from './sale'
export * from './counter'
export * from './expense'
export * from './property'
export * from './rentedProperty'
export * from './ledger'
export * from './license'
export * from './payment'
export * from './dashboard'
export * from './manualEntry'