import { DatePicker, Form, Select, Space, Checkbox, Radio } from 'antd'
import type { GetExpensesParams } from '@/common/types'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import { SortAscendingOutlined, SortDescendingOutlined } from '@ant-design/icons'
import { useState } from 'react'

const { RangePicker } = DatePicker

const EXPENSE_CATEGORIES = [
  'Office Supplies',
  'Utilities',
  'Rent',
  'Salaries',
  'Marketing',
  'Travel',
  'Maintenance',
  'Other'
]

interface ExpenseFiltersProps {
  filters: GetExpensesParams
  onFiltersChange: (filters: GetExpensesParams) => void
}

export const ExpenseFilters = ({ filters, onFiltersChange }: ExpenseFiltersProps) => {
  const handleDateRangeChange = (_: any, dateStrings: [string, string]) => {
    if (!dateStrings[0] || !dateStrings[1]) {
      const { startDate, endDate, ...rest } = filters
      onFiltersChange({ ...rest, page: 1 })
      return
    }

    onFiltersChange({
      ...filters,
      startDate: new Date(dateStrings[0]),
      endDate: new Date(dateStrings[1]),
      page: 1
    })
  }

  const handleCategoryChange = (category: string | undefined) => {
    if (!category) {
      const { category, ...rest } = filters
      onFiltersChange({ ...rest, page: 1 })
      return
    }

    onFiltersChange({
      ...filters,
      category,
      page: 1
    })
  }

  const handleIncludeDeletedChange = (checked: boolean) => {
    onFiltersChange({
      ...filters,
      includeDeleted: checked,
      page: 1
    })
  }

  const handleOrderByChange = (value: 'asc' | 'desc') => {
    onFiltersChange({
      ...filters,
      orderBy: value,
      page: 1
    })
  }

  return (
    <Space size="middle" className="w-full" wrap>
      <Form.Item label="Date Range" className="mb-0">
        <RangePicker
          value={
            filters.startDate && filters.endDate
              ? [dayjs(filters.startDate), dayjs(filters.endDate)]
              : null
          }
          onChange={handleDateRangeChange}
          className="w-[300px]"
          allowClear
          format="YYYY-MM-DD"
        />
      </Form.Item>

      <Form.Item label="Category" className="mb-0">
        <Select
          allowClear
          value={filters.category}
          onChange={handleCategoryChange}
          placeholder="Select category"
          className="w-[200px]"
        >
          {EXPENSE_CATEGORIES.map((category) => (
            <Select.Option key={category} value={category}>
              {category}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item className="mb-0">
        <Checkbox
          checked={filters.includeDeleted}
          onChange={(e) => handleIncludeDeletedChange(e.target.checked)}
        >
          Show Deleted
        </Checkbox>
      </Form.Item>

      <Form.Item label="Sort By Date" className="mb-0">
        <Select
          value={filters.orderBy || 'desc'}
          onChange={handleOrderByChange}
          className="w-[170px]"
          options={[
            { 
              label: <div className="flex items-center"><SortDescendingOutlined className="mr-2" /> Newest First</div>, 
              value: 'desc' 
            },
            { 
              label: <div className="flex items-center"><SortAscendingOutlined className="mr-2" /> Oldest First</div>, 
              value: 'asc' 
            }
          ]}
        />
      </Form.Item>
    </Space>
  )
}
