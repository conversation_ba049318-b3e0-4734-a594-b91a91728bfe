import React from 'react'
import { MdCurrencyExchange, MdDelete, MdOutlineDashboard, MdPayment } from 'react-icons/md'
import { FaShippingFast } from 'react-icons/fa'
import { GiWallet } from 'react-icons/gi'
import { Roles } from './roles'
import { App_Routes } from './routes'
import { IMenu } from '../types'
import {
  MdPeople,
  MdInventory2,
  MdOutlineReceiptLong,
  MdStorefront,
  MdPointOfSale,
  MdShoppingCart,
  MdOutlineApartment
} from 'react-icons/md'
import {
  FaMoneyBillTransfer,
  FaUsersGear,
  FaBoxesStacked,
  FaVault,
  FaUsers
} from 'react-icons/fa6'
import {
  BsCashStack,
  BsJournalBookmark,
  BsBank2
} from 'react-icons/bs'
import { TbReportMoney } from 'react-icons/tb'

const iconProps = {
  size: 18
}

export const Menus: IMenu[] = [
  {
    key: App_Routes.DASHBOARD,
    label: 'Dashboard',
    roles: [Roles.ADMIN, Roles.USER],
    icon: React.createElement(MdOutlineDashboard, iconProps)
  },
  // User Management
  {
    key: App_Routes.STAFF,
    label: 'Staff',
    roles: [Roles.ADMIN],
    icon: React.createElement(FaUsersGear, iconProps)
  },
  {
    key: App_Routes.PARTNER,
    label: 'Partner',
    roles: [Roles.ADMIN],
    icon: React.createElement(MdPeople, iconProps)
  },
  // Financial Management
  {
    key: App_Routes.BANKS,
    label: 'Banks',
    roles: [Roles.ADMIN],
    icon: React.createElement(BsBank2, iconProps)
  },
  {
    key: App_Routes.CASH_VAULT,
    label: 'Cash Vault',
    roles: [Roles.ADMIN],
    icon: React.createElement(FaVault, iconProps)
  },
  // {
  //   key: App_Routes.SMALL_COUNTER,
  //   label: 'Small Counter',
  //   roles: [Roles.ADMIN],
  //   icon: React.createElement(GiWallet, iconProps)
  // },
  {
    key: App_Routes.CURRENCY,
    label: 'Currency',
    roles: [Roles.ADMIN],
    icon: React.createElement(MdCurrencyExchange, iconProps)
  },
  {
    key: App_Routes.EXCHANGE,
    label: 'Exchange',
    roles: [Roles.ADMIN],
    icon: React.createElement(FaMoneyBillTransfer, iconProps)
  },
  {
    key: App_Routes.PROPERTY,
    label: 'Property',
    roles: [Roles.ADMIN],
    icon: React.createElement(MdOutlineApartment, iconProps)
  },
  // Accounting
  {
    key: App_Routes.ACCOUNTS,
    label: 'Accounts',
    roles: [Roles.ADMIN],
    icon: React.createElement(FaUsers, iconProps)  // Changed to FaUsers for customers/tenants
  },
  {
    key: App_Routes.LEDGER,
    label: 'Ledger',
    roles: [Roles.ADMIN],
    icon: React.createElement(TbReportMoney, iconProps)
  },
  {
    key: App_Routes.EXPENSE,
    label: 'Expense',
    roles: [Roles.ADMIN],
    icon: React.createElement(BsCashStack, iconProps)
  },
  {
    key: App_Routes.PAYMENT,
    label: 'Payment',
    roles: [Roles.ADMIN],
    icon: React.createElement(MdPayment, iconProps)
  },
  // Inventory & Sales
  {
    key: App_Routes.STOCK,
    label: 'Stock',
    roles: [Roles.ADMIN],
    icon: React.createElement(FaBoxesStacked, iconProps)
  },
  {
    key: App_Routes.CONTAINER,
    label: 'Container',
    roles: [Roles.ADMIN],
    icon: React.createElement(FaShippingFast, iconProps)
  },
  {
    key: App_Routes.SALE,
    label: 'Sale',
    roles: [Roles.ADMIN],
    icon: React.createElement(MdPointOfSale, iconProps)
  },
  {
    key: App_Routes.MANUAL_ENTRY,
    label: 'Manual Entry',
    roles: [Roles.ADMIN],
    icon: React.createElement(BsJournalBookmark, iconProps)
  },
  {
    key: App_Routes.RESET,
    label: 'Reset',
    roles: [Roles.ADMIN],
    icon: React.createElement(MdDelete, iconProps)
  },

]
