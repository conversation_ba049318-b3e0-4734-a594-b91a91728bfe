import { CreateWalkInSaleData, CreateAccountSaleData, IRequest, GetSalesParams, DeleteSaleData, GetContainerByStockIdParams } from '@/common/types';
import { saleService } from '../services';
import { ItemType } from '@prisma/client';

class SaleController {
    async createWalkInSale(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        return await saleService.createWalkInSale(req.body as CreateWalkInSaleData);
    }

    async createAccountSale(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        return await saleService.createAccountSale(req.body as CreateAccountSaleData);
    }

    async deleteSale(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id, reason, userId } = req.body as DeleteSaleData;

        if (!id) {
            throw new Error('Sale ID is required');
        }
        if (!reason) {
            throw new Error('Reason is required');
        }
        if (!userId) {
            throw new Error('User ID is required');
        }

        return await saleService.deleteSale(id, reason, userId);
    }

    async getSales(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        return await saleService.getSales(req.query as GetSalesParams);
    }

    async getAvailableItems(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { itemType } = req.params as { itemType: ItemType };
        return await saleService.getAvailableItems(itemType);
    }

    async getSaleById(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params as { id: string };
        return await saleService.getSaleById(id);
    }

    async getContainerByStockId(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { itemId, itemType } = req.params as GetContainerByStockIdParams;

        if (!itemId) {
            throw new Error('Item ID is required');
        }
        if (!itemType) {
            throw new Error('Item Type is required');
        }

        return await saleService.getContainerByStockId({ itemId, itemType });
    }
}

export const saleController = new SaleController();