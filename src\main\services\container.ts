import { prisma } from '../db';
import { Prisma } from '@prisma/client';
import {
    CreateContainerData,
    GetContainersParams,
    GetContainerStockParams,
    ContainerPDFData
} from '@/common/types';


class ContainerService {
    async createContainer(data: CreateContainerData) {
        return await prisma.$transaction(async (tx) => {
            // Create container
            const container = await tx.container.create({
                data: {
                    containerNumber: data.containerNumber,
                    openedAt: data.openedAt,
                    driverExpense: data.driverExpense,
                    taxes: data.taxes,
                    containerCost: data.containerCost,
                    routeExpense: data.routeExpense,
                    fieldRent: data.fieldRent,
                    partnerId: data.partnerId
                }
            });

            // Create cars if provided
            if (data.cars?.length) {
                await tx.car.createMany({
                    data: data.cars.map(car => ({
                        ...car,
                        containerId: container.id
                    }))
                });
            }

            // Create car parts if provided
            if (data.carParts?.length) {
                await tx.carPart.createMany({
                    data: data.carParts.map(part => ({
                        ...part,
                        containerId: container.id,
                        initialQuantity: part.quantity
                    }))
                });
            }

            // Create electronics if provided
            if (data.electronics?.length) {
                await tx.electronic.createMany({
                    data: data.electronics.map(electronic => ({
                        ...electronic,
                        containerId: container.id,
                        initialQuantity: electronic.quantity
                    }))
                });
            }

            // Create scraps if provided
            if (data.scraps?.length) {
                await tx.scrap.createMany({
                    data: data.scraps.map(scrap => ({
                        ...scrap,
                        containerId: container.id,
                        initialQuantity: scrap.quantity
                    }))
                });
            }

            return container;
        });
    }

    async deleteContainer(id: string, deleteReason: string, deletedById: string) {
        return await prisma.$transaction(async (tx) => {
            // Check if any items are sold
            const [soldCars, soldParts, soldElectronics, soldScraps] = await Promise.all([
                tx.car.count({ where: { containerId: id, status: 'SOLD' } }),
                tx.carPart.count({ where: { containerId: id, status: 'SOLD' } }),
                tx.electronic.count({ where: { containerId: id, status: 'SOLD' } }),
                tx.scrap.count({ where: { containerId: id, status: 'SOLD' } })
            ]);

            if (soldCars || soldParts || soldElectronics || soldScraps) {
                throw new Error('Cannot delete container with sold items');
            }

            // Soft delete all related items
            const now = new Date();
            await Promise.all([
                tx.car.updateMany({
                    where: { containerId: id },
                    data: { isDeleted: true, deleteReason, deletedAt: now }
                }),
                tx.carPart.updateMany({
                    where: { containerId: id },
                    data: { isDeleted: true, deleteReason, deletedAt: now }
                }),
                tx.electronic.updateMany({
                    where: { containerId: id },
                    data: { isDeleted: true, deleteReason, deletedAt: now }
                }),
                tx.scrap.updateMany({
                    where: { containerId: id },
                    data: { isDeleted: true, deleteReason, deletedAt: now }
                })
            ]);

            // Soft delete container
            return await tx.container.update({
                where: { id },
                data: { isDeleted: true, deleteReason, deletedAt: now }
            });
        });
    }

    async getContainers({ page, limit, partnerId, search, startDate, endDate, orderBy }: GetContainersParams) {
        const where: Prisma.ContainerWhereInput = {
            isDeleted: false,
            ...(partnerId && { partnerId }),
            ...(search && {
                containerNumber: {
                    contains: search,
                    mode: 'insensitive'
                }
            }),
            ...(startDate && endDate && {
                openedAt: {
                    gte: startDate,
                    lte: endDate
                }
            })
        };

        const [containersRaw, total] = await Promise.all([
            prisma.container.findMany({
                where,
                include: {
                    partner: {
                        select: {
                            id: true,
                            name: true
                        }
                    },
                    cars: {
                        select: {
                            sale: {
                                where: { isDeleted: false },
                                select: { totalAmount: true }
                            }
                        }
                    },
                    carParts: {
                        select: {
                            sale: {
                                where: { isDeleted: false },
                                select: { totalAmount: true }
                            }
                        }
                    },
                    electronics: {
                        select: {
                            sale: {
                                where: { isDeleted: false },
                                select: { totalAmount: true }
                            }
                        }
                    },
                    scraps: {
                        select: {
                            sale: {
                                where: { isDeleted: false },
                                select: { totalAmount: true }
                            }
                        }
                    }
                },
                orderBy: { openedAt: orderBy },
                skip: (page - 1) * limit,
                take: limit
            }),
            prisma.container.count({ where })
        ]);

        // Process containers to add sales and profit calculations
        const containers = containersRaw.map(container => {
            const totalSales = [
                ...container.cars.map(item => item.sale?.totalAmount || 0),
                ...container.carParts.map(item => item.sale?.reduce((sum, sale) => sum + sale.totalAmount, 0) || 0),
                ...container.electronics.map(item => item.sale?.reduce((sum, sale) => sum + sale.totalAmount, 0) || 0),
                ...container.scraps.map(item => item.sale?.reduce((sum, sale) => sum + sale.totalAmount, 0) || 0)
            ].reduce((sum, amount) => sum + amount, 0);

            const totalExpenses = container.containerCost + container.driverExpense +
                container.taxes + container.fieldRent + container.routeExpense;

            const expenseWithoutContainerCost = totalExpenses - container.containerCost;
            const salesMinusExpenses = totalSales - expenseWithoutContainerCost;

            return {
                ...container,
                totalSales,
                salesMinusExpenses
            };
        });

        return { containers, total };
    }

    async getContainerStock({ containerId, page, limit, itemType }: GetContainerStockParams) {
        const container = await prisma.container.findUnique({
            where: { id: containerId },
            include: {
                cars: itemType === 'CAR' ? {
                    where: { isDeleted: false },
                    skip: (page - 1) * limit,
                    take: limit
                } : false,
                carParts: itemType === 'CAR_PART' ? {
                    where: { isDeleted: false },
                    skip: (page - 1) * limit,
                    take: limit
                } : false,
                electronics: itemType === 'ELECTRONIC' ? {
                    where: { isDeleted: false },
                    skip: (page - 1) * limit,
                    take: limit
                } : false,
                scraps: itemType === 'SCRAP' ? {
                    where: { isDeleted: false },
                    skip: (page - 1) * limit,
                    take: limit
                } : false,
            }
        });

        if (!container) {
            throw new Error('Container not found');
        }

        return container;
    }

    // Additional suggested methods
    async getContainerById(id: string) {
        return await prisma.container.findUnique({
            where: { id },
            include: {
                partner: true,
                cars: true,
                carParts: true,
                electronics: true,
                scraps: true,
                // _count: {
                //     select: {
                //         cars: true,
                //         carParts: true,
                //         electronics: true,
                //         scraps: true
                //     }
                // }
            }
        });
    }

    async getContainerSummary(id: string) {
        const container = await prisma.container.findUnique({
            where: { id },
            include: {
                cars: {
                    select: {
                        status: true,
                        sale: {
                            where: { isDeleted: false },
                            select: { totalAmount: true }
                        }
                    }
                },
                carParts: {
                    select: {
                        status: true,
                        quantity: true,
                        sale: {
                            where: { isDeleted: false },
                            select: { totalAmount: true }
                        }
                    }
                },
                electronics: {
                    select: {
                        status: true,
                        quantity: true,
                        sale: {
                            where: { isDeleted: false },
                            select: { totalAmount: true }
                        }
                    }
                },
                scraps: {
                    select: {
                        status: true,
                        quantity: true,
                        sale: {
                            where: { isDeleted: false },
                            select: { totalAmount: true }
                        }
                    }
                }
            }
        });

        if (!container) {
            throw new Error('Container not found');
        }

        // Calculate summaries
        const totalExpenses = container.containerCost + container.driverExpense +
            container.taxes + container.fieldRent + container.routeExpense;

        const expenseWithoutContainerCost = totalExpenses - container.containerCost;

        const totalSales = [
            ...container.cars.map(item => item.sale?.totalAmount || 0),
            ...container.carParts.map(item => item.sale?.reduce((sum, sale) => sum + sale.totalAmount, 0) || 0),
            ...container.electronics.map(item => item.sale?.reduce((sum, sale) => sum + sale.totalAmount, 0) || 0),
            ...container.scraps.map(item => item.sale?.reduce((sum, sale) => sum + sale.totalAmount, 0) || 0)
        ].reduce((sum, amount) => sum + amount, 0);

        return {
            totalExpenses,
            totalSales,
            profit: totalSales - totalExpenses,
            salesMinusExpenses: totalSales - expenseWithoutContainerCost,
            itemsSummary: {
                cars: {
                    total: container.cars.length,
                    sold: container.cars.filter(c => c.status === 'SOLD').length
                },
                carParts: {
                    total: container.carParts.reduce((sum, p) => sum + p.quantity, 0),
                    sold: container.carParts.filter(p => p.status === 'SOLD')
                        .reduce((sum, p) => sum + p.quantity, 0)
                },
                electronics: {
                    total: container.electronics.reduce((sum, e) => sum + e.quantity, 0),
                    sold: container.electronics.filter(e => e.status === 'SOLD')
                        .reduce((sum, e) => sum + e.quantity, 0)
                },
                scraps: {
                    total: container.scraps.reduce((sum, s) => sum + s.quantity, 0),
                    sold: container.scraps.filter(s => s.status === 'SOLD')
                        .reduce((sum, s) => sum + s.quantity, 0)
                }
            }
        };
    }


    async checkCarWithSameChassisNumberExists(chassisNumber: string) {
        const car = await prisma.car.findFirst({
            where: { chassisNumber: chassisNumber },
            include: {
                container: {
                    select: {
                        containerNumber: true,
                        openedAt: true
                    }
                }
            }
        });

        if (car) {
            throw new Error(`Car with the same chassis number already exists. Name: ${car.name} - Chassis Number: ${car.chassisNumber} Model Number: ${car.modelNumber} Color: ${car.color}. in container ${car.container?.containerNumber} opened at ${car.container?.openedAt}`);
        }

        return !!car;
    }

    async getContainersForPDF(startDate?: Date, endDate?: Date, partnerId?: string) {
        const where: Prisma.ContainerWhereInput = {
            isDeleted: false,
            ...(partnerId && { partnerId }),
            ...(startDate && endDate && {
                openedAt: {
                    gte: startDate,
                    lte: endDate
                }
            })
        };

        const containersRaw = await prisma.container.findMany({
            where,
            select: {
                id: true,
                containerNumber: true,
                openedAt: true,
                containerCost: true,
                driverExpense: true,
                taxes: true,
                fieldRent: true,
                routeExpense: true,
                partner: {
                    select: {
                        name: true
                    }
                },
                cars: {
                    select: {
                        sale: {
                            where: { isDeleted: false },
                            select: { totalAmount: true }
                        }
                    }
                },
                carParts: {
                    select: {
                        sale: {
                            where: { isDeleted: false },
                            select: { totalAmount: true }
                        }
                    }
                },
                electronics: {
                    select: {
                        sale: {
                            where: { isDeleted: false },
                            select: { totalAmount: true }
                        }
                    }
                },
                scraps: {
                    select: {
                        sale: {
                            where: { isDeleted: false },
                            select: { totalAmount: true }
                        }
                    }
                }
            },
            orderBy: { openedAt: 'desc' }
        });

        // Process containers to add sales and profit calculations
        const containers: ContainerPDFData[] = containersRaw.map(container => {
            const totalSales = [
                ...container.cars.map(item => item.sale?.totalAmount || 0),
                ...container.carParts.map(item => item.sale?.reduce((sum, sale) => sum + sale.totalAmount, 0) || 0),
                ...container.electronics.map(item => item.sale?.reduce((sum, sale) => sum + sale.totalAmount, 0) || 0),
                ...container.scraps.map(item => item.sale?.reduce((sum, sale) => sum + sale.totalAmount, 0) || 0)
            ].reduce((sum, amount) => sum + amount, 0);

            const totalExpenses = container.containerCost + container.driverExpense +
                container.taxes + container.fieldRent + container.routeExpense;

            const expenseWithoutContainerCost = totalExpenses - container.containerCost;
            const salesMinusExpenses = totalSales - expenseWithoutContainerCost;

            return {
                containerNumber: container.containerNumber,
                openedAt: container.openedAt,
                totalCost: totalExpenses,
                salesMinusExpenses: salesMinusExpenses,
                partner: container.partner?.name || null
            };
        });

        // Calculate summary data
        const summary = {
            totalContainers: containers.length,
            totalCost: containers.reduce((sum, container) => sum + container.totalCost, 0),
            totalSalesMinusExpenses: containers.reduce((sum, container) => sum + container.salesMinusExpenses, 0)
        };

        return {
            containers,
            summary
        };
    }
}

export const containerService = new ContainerService();