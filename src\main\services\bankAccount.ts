import { prisma } from '../db';
import {
    CreateBankAccountData,
    GetTransactionHistoryParams,
    GetBankStatementParams,
    BankStatementEntry,
    BankStatementSummary
} from '@/common/types';
import { processDateRange } from '../utils/helperFunctions';

class BankAccountService {
    async createBankAccount(data: CreateBankAccountData) {
        const existing = await prisma.bankAccount.findUnique({
            where: { accountNumber: data.accountNumber }
        });

        if (existing) {
            throw new Error('Bank account with this number already exists');
        }

        return await prisma.$transaction(async (tx) => {
            const account = await tx.bankAccount.create({
                data: {
                    accountNumber: data.accountNumber,
                    bankName: data.bankName,
                    branchCode: data.branchCode,
                    balance: data.openingBalance ?? 0
                }
            });

            if (data.openingBalance && data.openingBalance > 0) {
                await tx.ledgerEntry.create({
                    data: {
                        amount: data.openingBalance,
                        type: 'CREDIT',
                        description: 'Opening Balance',
                        sourceType: 'OTHER',
                        destinationType: 'BANK_ACCOUNT',
                        transactionType: 'OPENING_BALANCE',
                        currency: { connect: { code: 'PKR' } },
                        bankAccount: { connect: { id: account.id } },
                        createdBy: { connect: { id: data.userId } }
                    }
                });
            }

            return account;
        });
    }

    async deleteBankAccount(id: string) {
        return await prisma.$transaction(async (tx) => {
            // First check if bank account exists
            const account = await tx.bankAccount.findUnique({
                where: { id }
            });

            if (!account) {
                throw new Error('Bank account not found');
            }

            // Check for active non-opening-balance transactions
            const hasActiveTransactions = await tx.ledgerEntry.findFirst({
                where: {
                    bankAccountId: id,
                    NOT: { transactionType: 'OPENING_BALANCE' },
                    isDeleted: false
                }
            });

            if (hasActiveTransactions) {
                throw new Error('Cannot delete bank account with existing active transactions');
            }

            // Delete all ledger entries (including soft-deleted ones and opening balance)
            await tx.ledgerEntry.deleteMany({
                where: { bankAccountId: id }
            });

            // Finally delete the bank account
            return await tx.bankAccount.delete({
                where: { id }
            });
        });
    }

    async getAllBankAccounts() {
        return await prisma.bankAccount.findMany({
            where: { isActive: true }
        });
    }

    async getBankAccountsForSelect() {
        const banksList = await prisma.bankAccount.findMany({
            where: { isActive: true },
            select: { id: true, accountNumber: true, bankName: true }
        });
        return banksList.map((bank) => ({
            label: bank.bankName + ' - ' + bank.accountNumber,
            value: bank.id
        }));
    }

    async getBankAccountById(id: string) {
        return await prisma.bankAccount.findUnique({
            where: { id }
        });
    }

    async adjustBalance(id: string, adjustment: number, reason: string, userId: string) {
        return await prisma.$transaction(async (tx) => {
            const account = await tx.bankAccount.findUnique({
                where: { id }
            });

            if (!account) throw new Error('Bank account not found');
            if (!account.isActive) throw new Error('Bank account is inactive');

            // Create ledger entry for audit trail
            await tx.ledgerEntry.create({
                data: {
                    amount: Math.abs(adjustment),
                    type: adjustment > 0 ? 'CREDIT' : 'DEBIT',
                    description: `Manual bank balance adjustment: ${reason}`,
                    sourceType: adjustment > 0 ? 'OTHER' : 'BANK_ACCOUNT',
                    destinationType: adjustment > 0 ? 'BANK_ACCOUNT' : 'OTHER',
                    transactionType: 'OTHER',
                    currency: { connect: { code: 'PKR' } }, // Bank accounts only handle PKR
                    bankAccount: { connect: { id } },
                    createdBy: { connect: { id: userId } }
                }
            });

            return await tx.bankAccount.update({
                where: { id },
                data: { balance: { increment: adjustment } }
            });
        });
    }

    async deactivateAccount(id: string) {
        const account = await prisma.bankAccount.findUnique({
            where: { id }
        });

        if (!account) throw new Error('Bank account not found');
        if (account.balance !== 0) {
            throw new Error('Cannot deactivate account with non-zero balance');
        }

        return await prisma.bankAccount.update({
            where: { id },
            data: { isActive: false }
        });
    }

    async getTransactionHistory(id: string, params: GetTransactionHistoryParams = {}) {
        const {
            page = 1,
            pageSize = 20,
            sortOrder = 'desc',
            startDate,
            endDate
        } = params;

        // Build the where clause
        const where: any = {
            bankAccountId: id,
            isDeleted: false
        };

        // Add date range if provided
        if (startDate && endDate) {
            where.date = processDateRange(startDate, endDate);
        }

        // Execute queries in parallel
        const [transactions, total] = await Promise.all([
            prisma.ledgerEntry.findMany({
                where,
                include: {
                    createdBy: { select: { name: true } }
                },
                orderBy: { date: sortOrder },
                skip: (page - 1) * pageSize,
                take: pageSize
            }),
            prisma.ledgerEntry.count({ where })
        ]);

        return {
            transactions,
            pagination: {
                total,
                pages: Math.ceil(total / pageSize),
                currentPage: page,
                pageSize
            }
        };
    }



    async generateBankStatement(id: string, params: GetBankStatementParams) {
        const { startDate, endDate, page = 1, pageSize = 100 } = params;

        // Validate input
        if (!startDate || !endDate) {
            throw new Error('Start date and end date are required');
        }

        // Get the bank account
        const account = await prisma.bankAccount.findUnique({
            where: { id }
        });

        if (!account) throw new Error('Bank account not found');

        // Calculate the opening balance by summing all transactions before the start date
        const [creditsBefore, debitsBefore] = await Promise.all([
            prisma.ledgerEntry.aggregate({
                where: {
                    bankAccountId: id,
                    date: { lt: startDate },
                    type: 'CREDIT',
                    isDeleted: false
                },
                _sum: { amount: true }
            }),
            prisma.ledgerEntry.aggregate({
                where: {
                    bankAccountId: id,
                    date: { lt: startDate },
                    type: 'DEBIT',
                    isDeleted: false
                },
                _sum: { amount: true }
            })
        ]);

        // Calculate opening balance
        const openingBalance = (creditsBefore._sum.amount || 0) - (debitsBefore._sum.amount || 0);

        // Get total count for pagination
        const total = await prisma.ledgerEntry.count({
            where: {
                bankAccountId: id,
                date: processDateRange(startDate, endDate),
                isDeleted: false
            }
        });

        // Fetch transactions for the current page
        const transactions = await prisma.ledgerEntry.findMany({
            where: {
                bankAccountId: id,
                date: processDateRange(startDate, endDate),
                isDeleted: false
            },
            include: {
                createdBy: { select: { name: true } }
            },
            orderBy: { date: 'asc' }, // Always oldest first for bank statement
            skip: (page - 1) * pageSize,
            take: pageSize
        });

        // Calculate running balance and prepare statement entries
        let runningBalance = openingBalance;
        let totalCredits = 0;
        let totalDebits = 0;

        const statementEntries: BankStatementEntry[] = transactions.map(tx => {
            // Update running balance based on transaction type
            if (tx.type === 'CREDIT') {
                runningBalance += tx.amount;
                totalCredits += tx.amount;
            } else {
                runningBalance -= tx.amount;
                totalDebits += tx.amount;
            }

            // Create statement entry with credit/debit columns
            return {
                id: tx.id,
                date: tx.date,
                description: tx.description || '',
                credit: tx.type === 'CREDIT' ? tx.amount : null,
                debit: tx.type === 'DEBIT' ? tx.amount : null,
                runningBalance,
                createdBy: tx.createdBy
            };
        });

        // Calculate summary
        const summary: BankStatementSummary = {
            totalCredits,
            totalDebits,
            net: totalCredits - totalDebits
        };

        return {
            account,
            startDate,
            endDate,
            entries: statementEntries,
            openingBalance,
            closingBalance: runningBalance,
            summary,
            pagination: {
                total,
                pages: Math.ceil(total / pageSize),
                currentPage: page,
                pageSize
            }
        };
    }

    async reconcileBalance(id: string) {
        const ledgerBalance = await prisma.ledgerEntry.aggregate({
            where: {
                bankAccountId: id,
                isDeleted: false
            },
            _sum: { amount: true }
        });

        const account = await prisma.bankAccount.findUnique({
            where: { id }
        });

        if (!account) throw new Error('Bank account not found');

        const calculatedBalance = (ledgerBalance._sum.amount ?? 0);
        const isReconciled = calculatedBalance === account.balance;

        return {
            isReconciled,
            currentBalance: account.balance,
            calculatedBalance,
            difference: account.balance - calculatedBalance
        };
    }

}

export const bankAccountService = new BankAccountService();