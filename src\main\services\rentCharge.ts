// this function will be used only in the backend at the launch of the app.
// it will calculate the rent for all the rented properties and update the database.

import { TransactionLocation, TransactionType, TransactionCategory } from "@prisma/client"
import { prisma } from "../db"


// TODO: update balance of anyone who has to pay rent
export const checkAndProcessPendingRentCharges = async () => {
    return prisma.$transaction(async (tx) => {


        // Get the first active admin user
        const systemUser = await tx.user.findFirst({
            where: {
                isActive: true,
                role: 'ADMIN',
                NOT: {
                    name: 'System Administrator'
                }
            }
        })

        if (!systemUser) {
            throw new Error('No active admin user found to process rent charges')
        }

        const today = new Date()
        const pkr = await tx.currency.findFirst({ where: { code: 'PKR' } })
        if (!pkr) throw new Error('PKR currency not found')

        // Get all active rentals
        const activeRentals = await tx.rentedProperty.findMany({
            where: {
                isDeleted: false,
                OR: [
                    { endDate: null },
                    { endDate: { gt: today } }
                ]
            },
            include: {
                property: true,
                tenant: true
            }
        })

        const charges = [] as any[]

        for (const rental of activeRentals) {
            // Get the last rent charge
            const lastCharge = await tx.ledgerEntry.findFirst({
                where: {
                    accountId: rental.tenantId,
                    transactionType: TransactionCategory.RENT,
                    type: TransactionType.DEBIT,
                    isDeleted: false
                },
                orderBy: { date: 'desc' }
            })

            const rentalStartDate = new Date(rental.startDate)
            const lastChargeDate = lastCharge ? new Date(lastCharge.date) : rentalStartDate

            // Calculate months to charge (your original logic)
            const monthDifference = (today.getMonth() + 12 * today.getFullYear()) -
                (lastChargeDate.getMonth() + 12 * lastChargeDate.getFullYear())

            // Only proceed if we're past the rental day and there's a month difference
            if (monthDifference > 0 && today.getDate() >= rentalStartDate.getDate()) {
                // Double check we haven't already charged this month
                const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
                const existingMonthCharge = await tx.ledgerEntry.findFirst({
                    where: {
                        accountId: rental.tenantId,
                        transactionType: TransactionCategory.RENT,
                        type: TransactionType.DEBIT,
                        date: { gte: startOfMonth },
                        isDeleted: false
                    }
                })

                if (!existingMonthCharge) {
                    // Create the rent charge ledger entry
                    const charge = await tx.ledgerEntry.create({
                        data: {
                            date: today,
                            amount: rental.monthlyRent,
                            type: TransactionType.DEBIT,
                            description: `Monthly rent charge for ${rental.property.name}`,
                            accountId: rental.tenantId,
                            sourceType: TransactionLocation.ACCOUNT,
                            destinationType: TransactionLocation.ACCOUNT,
                            transactionType: TransactionCategory.RENT,
                            createdById: systemUser.id,
                            currencyId: pkr.id
                        }
                    })
                    charges.push(charge)

                    // Update the tenant's PKR balance
                    await tx.accountBalance.update({
                        where: {
                            accountId_currencyId: {
                                accountId: rental.tenantId,
                                currencyId: pkr.id
                            }
                        },
                        data: {
                            balance: {
                                decrement: rental.monthlyRent
                            }
                        }
                    })
                }
            }
        }

        return charges
    })
}