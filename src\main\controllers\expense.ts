import { CreateExpenseData, DeleteExpenseData, GetExpensesParams, IRequest } from '@/common/types';
import { expenseService } from '../services';
import { TransactionLocation } from '@prisma/client';

class ExpenseController {
    async createExpense(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { userId, amount, category, description, paymentSource, bankAccountId, date } = req.body as CreateExpenseData;
        console.log(req.body)

        console.log(userId, amount, category, description, paymentSource, bankAccountId)

        if (!userId || !amount || !category || !description || !paymentSource) {
            throw new Error('Missing required data');
        }

        if (paymentSource === 'BANK_ACCOUNT' && !bankAccountId) {
            throw new Error('Bank account ID is required for bank payments');
        }

        return await expenseService.createExpense(req.body as CreateExpenseData);
    }

    async deleteExpense(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id, reason, userId } = req.body as DeleteExpenseData;

        if (!id || !reason || !userId) {
            throw new Error('Missing required fields');
        }

        return await expenseService.deleteExpense({ id, reason, userId });
    }

    async getExpenses(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { query } = req;

        return await expenseService.getExpenses(query as GetExpensesParams);
    }
}

export const expenseController = new ExpenseController();