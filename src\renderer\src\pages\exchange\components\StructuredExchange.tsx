import React, { useEffect, useState } from 'react'
import {
  App,
  Button,
  Card,
  Form,
  Input,
  InputNumber,
  Select,
  Space,
  Checkbox,
  Descriptions,
  Tag
} from 'antd'
import { ArrowLeftOutlined, SwapOutlined } from '@ant-design/icons'
import type { CreateStructuredExchangeData } from '@/common/types'
import { exchangeApi } from '@/renderer/services'
import { useAccountContext, useBankContext } from '@/renderer/contexts'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { AccountDetailsCard, LocationDetailsCard } from '@/renderer/components'
import { formatCurrencyWithoutSymbol } from '@/renderer/utils'

interface StructuredExchangeProps {
  onClose: () => void
  setRefreshTrigger: React.Dispatch<React.SetStateAction<number>>
}

type TransactionLocation =
  // | 'SMALL_COUNTER'
  'CASH_VAULT' | 'BANK_ACCOUNT' | 'ACCOUNT' | 'EXTERNAL' | 'OTHER'

const TransactionLocation = {
  // SMALL_COUNTER: 'SMALL_COUNTER',
  CASH_VAULT: 'CASH_VAULT',
  BANK_ACCOUNT: 'BANK_ACCOUNT',
  ACCOUNT: 'ACCOUNT',
  EXTERNAL: 'EXTERNAL',
  OTHER: 'OTHER'
}

const CURRENCIES = ['PKR', 'USD', 'AED', 'AFN']

export const StructuredExchange = ({ onClose, setRefreshTrigger }: StructuredExchangeProps) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [showReverseRate, setShowReverseRate] = useState(false)
  const { message } = App.useApp()

  // State for selected items to display in the right panel
  const [selectedAccountId, setSelectedAccountId] = useState<string | undefined>(undefined)
  const [selectedInputBankId, setSelectedInputBankId] = useState<string | undefined>(undefined)
  const [selectedOutputBankId, setSelectedOutputBankId] = useState<string | undefined>(undefined)
  const [calculatedOutputAmount, setCalculatedOutputAmount] = useState<number | null>(null)

  const { accounts } = useAccountContext()
  const { banks } = useBankContext()

  const user = useSelector((state: IRootState) => state.user.data)

  // Watch form values for dynamic validation
  const inputSource = Form.useWatch('inputSource', form)
  const inputCurrency = Form.useWatch('inputCurrency', form)
  const outputCurrency = Form.useWatch('outputCurrency', form)
  const inputLocation = Form.useWatch('inputLocation', form)
  const keepInAccount = Form.useWatch('keepInAccount', form)
  const outputLocation = Form.useWatch('outputLocation', form)
  const exchangeRate = Form.useWatch('exchangeRate', form)

  useEffect(() => {
    form.setFieldsValue({ inputBankId: undefined })
    setSelectedInputBankId(undefined)
  }, [inputLocation])

  useEffect(() => {
    form.setFieldsValue({ outputBankId: undefined })
    setSelectedOutputBankId(undefined)
  }, [outputLocation])

  // Get available locations based on currency
  const getAvailableLocations = (currency: string) => {
    const pkrLocations = [
      // { value: TransactionLocation.SMALL_COUNTER, label: 'Small Counter' },
      { value: TransactionLocation.CASH_VAULT, label: 'Cash Vault' },
      { value: TransactionLocation.BANK_ACCOUNT, label: 'Bank Account' }
    ]

    const otherLocations = [
      // { value: TransactionLocation.SMALL_COUNTER, label: 'Small Counter' },
      { value: TransactionLocation.CASH_VAULT, label: 'Cash Vault' }
    ]

    if (currency === 'PKR') {
      return pkrLocations
    }

    return otherLocations
  }

  // Reset dependent fields when currency changes
  useEffect(() => {
    if (inputCurrency) {
      form.setFieldsValue({
        inputLocation: undefined,
        inputBankId: undefined,
        inputSource: undefined
      })
    }
  }, [inputCurrency])

  useEffect(() => {
    if (outputCurrency) {
      form.setFieldsValue({ outputLocation: undefined, outputBankId: undefined })
    }
  }, [outputCurrency])

  // Update exchange rate when reverse rate changes
  const handleReverseRateChange = (value: number | null) => {
    if (!value) {
      form.setFieldValue('exchangeRate', null)
      return
    }
    const actualRate = Number(1 / value) // Keep 8 decimal places for precision
    form.setFieldValue('exchangeRate', actualRate)
  }

  // Handle account selection
  const handleAccountChange = (value: string) => {
    setSelectedAccountId(value)
  }

  // Handle input bank selection
  const handleInputBankChange = (value: string) => {
    setSelectedInputBankId(value)
  }

  // Handle output bank selection
  const handleOutputBankChange = (value: string) => {
    setSelectedOutputBankId(value)
  }

  // Calculate output amount when input amount or exchange rate changes
  useEffect(() => {
    const inputAmount = form.getFieldValue('inputAmount')
    const exchangeRate = form.getFieldValue('exchangeRate')

    if (inputAmount && exchangeRate) {
      const outputAmount = Number((inputAmount * exchangeRate).toFixed(2))
      setCalculatedOutputAmount(outputAmount)
    } else {
      setCalculatedOutputAmount(null)
    }
  }, [form.getFieldValue('inputAmount'), form.getFieldValue('exchangeRate')])

  const handleSubmit = async (values: any) => {
    setLoading(true)

    // Calculate the output amount with proper precision
    const outputAmount = Number((values.inputAmount * values.exchangeRate).toFixed(2))

    // If using reverse rate, we need to pass both rates
    const data = {
      ...values,
      outputAmount,
      userId: user?.id,
      isReverseRate: showReverseRate,
      displayRate: showReverseRate ? 1 / values.exchangeRate : values.exchangeRate
    }

    const response = await exchangeApi.createStructuredExchange(data)
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    setRefreshTrigger((prev) => prev + 1)
    message.success('Exchange created successfully')
    form.resetFields()
    onClose()
  }

  return (
    <div className="flex">
      <div className="w-full flex-[2]">
        <Card className="h-fit w-full shadow-sm">
          <div className="mb-6 flex items-center gap-4">
            <Button icon={<ArrowLeftOutlined />} onClick={onClose} />
            <h2 className="text-xl font-semibold">New Exchange</h2>
          </div>

          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            className="w-full"
            requiredMark={false}
          >
            {/* Account Selection */}
            <Form.Item
              name="accountId"
              label="Account"
              rules={[{ required: true, message: 'Please select an account' }]}
            >
              <Select
                showSearch
                placeholder="Select account"
                optionFilterProp="label"
                options={accounts}
                onChange={handleAccountChange}
              />
            </Form.Item>

            {/* Input Currency Section */}
            <div className="rounded-lg border border-gray-200 bg-black px-4 py-2">
              <h3 className="mb-4 font-medium">Input Currency</h3>
              <Space direction="vertical" className="w-full">
                {/* group currency and source in the same row */}
                <div className="flex gap-2">
                  <Form.Item
                    name="inputCurrency"
                    label="Currency"
                    className="w-full"
                    rules={[
                      { required: true, message: 'Please select currency' },
                      {
                        validator: (_, value) =>
                          value === outputCurrency
                            ? Promise.reject('Input and output currencies must be different')
                            : Promise.resolve()
                      }
                    ]}
                  >
                    <Select placeholder="Select currency">
                      {CURRENCIES.map((currency) => (
                        <Select.Option key={currency} value={currency}>
                          {currency}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Form.Item
                    name="inputSource"
                    label="Source"
                    className="w-full"
                    rules={[{ required: true, message: 'Please select source' }]}
                  >
                    <Select placeholder="Select source">
                      <Select.Option value="NEW_DEPOSIT">New Deposit</Select.Option>
                      <Select.Option value="ACCOUNT_BALANCE">Use Account Balance</Select.Option>
                    </Select>
                  </Form.Item>
                </div>

                {/* group amount and allow loan together */}

                <div className="flex items-center gap-2">
                  <Form.Item
                    className="w-full"
                    name="inputAmount"
                    label="Amount"
                    rules={[{ required: true, message: 'Please enter amount' }]}
                  >
                    <InputNumber
                      className="w-full"
                      placeholder="Enter amount"
                      formatter={(value) => ` ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={(value) =>
                        Number(value!.replace(new RegExp(`${inputCurrency}\\s?|(,*)`, 'g'), ''))
                      }
                      min={Number(0)}
                    />
                  </Form.Item>

                  {inputSource === 'ACCOUNT_BALANCE' && (
                    <Form.Item
                      className="ml-6 mt-8 w-full"
                      name="allowLoan"
                      valuePropName="checked"
                    >
                      <Checkbox>Allow Loan</Checkbox>
                    </Form.Item>
                  )}
                </div>

                <div className="flex gap-2">
                  {inputSource === 'NEW_DEPOSIT' && (
                    <>
                      <Form.Item
                        name="inputLocation"
                        label="Input Location"
                        className="w-full"
                        rules={[{ required: true, message: 'Please select source location' }]}
                      >
                        <Select
                          placeholder="Select location"
                          options={inputCurrency ? getAvailableLocations(inputCurrency) : []}
                        />
                      </Form.Item>

                      {inputLocation === TransactionLocation.BANK_ACCOUNT && (
                        <Form.Item
                          name="inputBankId"
                          label="Bank Account"
                          className="w-full"
                          rules={[{ required: true, message: 'Please select bank account' }]}
                        >
                          <Select
                            placeholder="Select bank account"
                            options={banks}
                            onChange={handleInputBankChange}
                          />
                        </Form.Item>
                      )}
                    </>
                  )}
                </div>
              </Space>
            </div>

            {/* Exchange Details */}
            <div className="mt-2 rounded-lg border border-gray-200 bg-black px-4 py-2">
              <h3 className="mb-4 font-medium">Exchange Details</h3>
              <Space direction="vertical" className="w-full">
                <Form.Item>
                  <Checkbox
                    checked={showReverseRate}
                    onChange={(e) => setShowReverseRate(e.target.checked)}
                  >
                    Use reverse rate input (1 output currency = X input currency)
                  </Checkbox>
                </Form.Item>

                {showReverseRate ? (
                  <div className="flex gap-2">
                    <Form.Item className="w-full" label="Rate Helper">
                      <InputNumber
                        className="w-full"
                        placeholder={`1 ${outputCurrency || 'output currency'} = ? ${inputCurrency || 'input currency'}`}
                        onChange={handleReverseRateChange}
                        min={1}
                      />
                    </Form.Item>

                    <Form.Item className="w-full" name="exchangeRate" label="Actual Exchange Rate">
                      <InputNumber
                        className="w-full"
                        disabled
                        placeholder="Will be calculated automatically"
                      />
                    </Form.Item>
                  </div>
                ) : (
                  <Form.Item
                    name="exchangeRate"
                    label="Exchange Rate"
                    rules={[{ required: true, message: 'Please enter exchange rate' }]}
                  >
                    <InputNumber className="w-full" min={0} placeholder="Enter exchange rate" />
                  </Form.Item>
                )}

                <Form.Item name="description" label="Description">
                  <Input.TextArea rows={1} placeholder="Enter description (optional)" />
                </Form.Item>
              </Space>
            </div>

            {/* Output Currency Section */}
            <div className="mt-4 rounded-lg border border-gray-200 bg-black p-4">
              <h3 className="mb-4 font-medium">Output Currency</h3>
              <Space direction="vertical" className="w-full">
                <div className="flex gap-2">
                  <Form.Item
                    className="w-full"
                    name="outputCurrency"
                    label="Currency"
                    rules={[
                      { required: true, message: 'Please select currency' },
                      {
                        validator: (_, value) =>
                          value === inputCurrency
                            ? Promise.reject('Input and output currencies must be different')
                            : Promise.resolve()
                      }
                    ]}
                  >
                    <Select placeholder="Select currency">
                      {CURRENCIES.map((currency) => (
                        <Select.Option key={currency} value={currency}>
                          {currency}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Form.Item
                    name="keepInAccount"
                    label="Keep in Account"
                    className="w-full"
                    rules={[{ required: true, message: 'Please select an option' }]}
                  >
                    <Select placeholder="Select option">
                      <Select.Option value={true}>Yes</Select.Option>
                      <Select.Option value={false}>No</Select.Option>
                    </Select>
                  </Form.Item>
                </div>

                {keepInAccount === false && (
                  <div className="flex gap-2">
                    <Form.Item
                      className="flex-1"
                      name="outputLocation"
                      label="Output Location"
                      rules={[{ required: true, message: 'Please select destination location' }]}
                    >
                      <Select
                        placeholder="Select location"
                        options={outputCurrency ? getAvailableLocations(outputCurrency) : []}
                      />
                    </Form.Item>

                    {outputLocation === TransactionLocation.BANK_ACCOUNT && (
                      <Form.Item
                        className="flex-[2]"
                        name="outputBankId"
                        label="Bank Account"
                        rules={[{ required: true, message: 'Please select bank account' }]}
                      >
                        <Select
                          placeholder="Select bank account"
                          options={banks}
                          onChange={handleOutputBankChange}
                        />
                      </Form.Item>
                    )}
                  </div>
                )}
              </Space>
            </div>

            <Form.Item className="mt-6">
              <Button type="primary" htmlType="submit" loading={loading} block>
                Create Exchange
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>

      <Space direction="vertical" className="flex-[3] p-4">
        <div className="flex gap-4">
          {/* Account Details Card */}

          <div className="flex-1">
            {selectedAccountId && <AccountDetailsCard accountId={selectedAccountId} />}
          </div>

          <div className="flex-1">
            {/* Input Location Details */}
            {inputLocation === 'CASH_VAULT' && inputCurrency && (
              <LocationDetailsCard
                subTitle="Input Location"
                locationType="CASH_VAULT"
                currencyCode={inputCurrency}
              />
            )}

            {inputLocation === 'BANK_ACCOUNT' && selectedInputBankId && (
              <LocationDetailsCard
                subTitle="Input Location"
                locationType="BANK_ACCOUNT"
                bankId={selectedInputBankId}
              />
            )}
          </div>
        </div>

        <div className="flex gap-4">
          {/* Exchange Summary Card */}
          <div className="flex-1">
            <Card title="Exchange Summary" className="mb-4 shadow-lg">
              <Descriptions column={1}>
                <Descriptions.Item label="Input Currency">{inputCurrency}</Descriptions.Item>
                <Descriptions.Item label="Output Currency">{outputCurrency}</Descriptions.Item>
                <Descriptions.Item label="Exchange Rate">
                  {showReverseRate ? (
                    <>
                      1 {outputCurrency} = {1 / exchangeRate} {inputCurrency}
                    </>
                  ) : (
                    <>
                      1 {inputCurrency} = {exchangeRate} {outputCurrency}
                    </>
                  )}
                </Descriptions.Item>
                {calculatedOutputAmount !== null && (
                  <Descriptions.Item label="Calculated Output Amount">
                    <Tag color="green">
                      {formatCurrencyWithoutSymbol(calculatedOutputAmount)} {outputCurrency}
                    </Tag>
                  </Descriptions.Item>
                )}
              </Descriptions>
            </Card>
          </div>

          <div className="flex-1">
            {/* Output Location Details */}
            {keepInAccount === false && outputLocation === 'CASH_VAULT' && outputCurrency && (
              <LocationDetailsCard
                subTitle="Output Location"
                locationType="CASH_VAULT"
                currencyCode={outputCurrency}
              />
            )}
            {keepInAccount === false &&
              outputLocation === 'BANK_ACCOUNT' &&
              selectedOutputBankId && (
                <LocationDetailsCard
                  subTitle="Output Location"
                  locationType="BANK_ACCOUNT"
                  bankId={selectedOutputBankId}
                />
              )}
          </div>
        </div>
      </Space>
    </div>
  )
}
