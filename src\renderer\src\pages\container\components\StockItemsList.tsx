import { Card, Button, List, Modal, Form, App } from 'antd'
import { useState } from 'react'
import { FiPlus, FiTrash2 } from 'react-icons/fi'
import { CarForm } from './forms/CarForm'
import { CarPartForm } from './forms/CarPartForm'
import { ElectronicForm } from './forms/ElectronicForm'
import { ScrapForm } from './forms/ScrapForm'
import { useTheme } from '@/renderer/contexts'
import { containerApi } from '@/renderer/services'

interface StockItemsListProps {
  title: string
  items: any[]
  onAdd: (item: any) => void
  onRemove: (index: number) => void
  type: 'car' | 'carPart' | 'electronic' | 'scrap'
}

export const StockItemsList = ({ title, items, onAdd, onRemove, type }: StockItemsListProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [form] = Form.useForm()

  const { isDarkMode } = useTheme()
  const { message } = App.useApp()

  const handleAdd = async () => {
    try {
      const values = await form.validateFields()

      if (type === 'car') {
        // Check if the chassis number is unique, both in the container and in the database
        const ensureChasisNumberIsUnique = async () => {
          const chassisNumber = values.chassisNumber.trim()

          // If it's an auto-generated chassis number, we can skip database check
          // as the timestamp ensures uniqueness
          const isAutoGenerated = chassisNumber.startsWith('AUTO-')

          // Still need to check container items to be sure
          const chassisNumberExists = items.find((item) => item.chassisNumber === chassisNumber)

          if (chassisNumberExists) {
            message.error(
              `Car with chassis number ${chassisNumber} already exists in this container.`
            )
            return
          }

          // Only check database for non-auto-generated chassis numbers
          if (!isAutoGenerated) {
            const response = await containerApi.checkCarWithSameChassisNumberExists(chassisNumber)
            if (response.error.error || response.data.error) {
              message.error(response.error.message || response.data.error.message)
              return
            }
          }

          onAdd({ ...values, chassisNumber })
          form.resetFields()
          setIsModalOpen(false)
        }

        await ensureChasisNumberIsUnique()
      } else {
        onAdd(values)
        form.resetFields()
        setIsModalOpen(false)
      }
    } catch (error) {
      console.error('Validation failed:', error)
    }
  }

  const getFormComponent = () => {
    switch (type) {
      case 'car':
        return <CarForm form={form} />
      case 'carPart':
        return <CarPartForm form={form} />
      case 'electronic':
        return <ElectronicForm form={form} />
      case 'scrap':
        return <ScrapForm form={form} />
    }
  }

  const renderItemContent = (item: any) => {
    switch (type) {
      case 'car':
        return `Name: ${item.name} - Chassis Number: ${item.chassisNumber} \n Model Number: ${item.modelNumber} - Color: ${item.color}`
      case 'carPart':
      case 'electronic':
        return `${item.name} (${item.quantity})`
      case 'scrap':
        return `${item.description} (${item.quantity})`
    }
  }

  return (
    <Card
      title={title}
      extra={
        <Button
          type="primary"
          icon={<FiPlus />}
          onClick={() => setIsModalOpen(true)}
          className="bg-green-500 hover:!bg-green-600"
        >
          Add {title}
        </Button>
      }
      className={`h-fit shadow-inner ${isDarkMode ? 'bg-black' : 'bg-slate-50'}`}
    >
      <List
        dataSource={items}
        renderItem={(item, index) => (
          <List.Item
            className="flex items-center justify-between"
            actions={[
              <Button
                key="delete"
                type="text"
                danger
                icon={<FiTrash2 />}
                onClick={() => onRemove(index)}
              />
            ]}
          >
            <span>{renderItemContent(item)}</span>
          </List.Item>
        )}
        locale={{ emptyText: `No ${title.toLowerCase()} added yet` }}
      />

      <Modal
        title={`Add ${title.slice(0, -1)}`}
        open={isModalOpen}
        onOk={handleAdd}
        onCancel={() => {
          form.resetFields()
          setIsModalOpen(false)
        }}
      >
        <Form form={form} layout="vertical">
          {getFormComponent()}
        </Form>
      </Modal>
    </Card>
  )
}
