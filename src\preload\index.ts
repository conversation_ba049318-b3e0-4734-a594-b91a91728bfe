import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'

// Custom APIs for renderer
const api = {
  // ... existing APIs ...

  // Backup related events
  onBackupProgress: (callback: (data: any) => void) => {
    ipcRenderer.on('backup-progress', (_event, data) => callback(data))
    return () => {
      ipcRenderer.removeListener('backup-progress', callback)
    }
  },
  onRestoreProgress: (callback: (data: any) => void) => {
    ipcRenderer.on('restore-progress', (_event, data) => callback(data))
    return () => {
      ipcRenderer.removeListener('restore-progress', callback)
    }
  }
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.api = api
}
