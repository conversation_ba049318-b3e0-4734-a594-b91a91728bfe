import { A5_CONFIG, PDF_STYLES, savePDF, printPDF } from '@/renderer/utils/pdfUtils'
import { bankAccountApi } from '@/renderer/services'
import jsPDF from 'jspdf'
import 'jspdf-autotable'
import dayjs from 'dayjs'
import type { BankStatement, GetBankStatementParams } from '@/common/types'

export const generateBankStatementPDF = async (
    bankId: string,
    startDate: Date,
    endDate: Date
): Promise<jsPDF> => {
    // Fetch all statement entries for PDF (no pagination)
    const response = await bankAccountApi.generateBankStatement(bankId, {
        startDate,
        endDate,
        page: 1,
        pageSize: 10000 // Large page size to get all entries
    })

    if (response.error.error || response.data.error) {
        throw new Error(response.error.message || response.data.error.message)
    }

    const statement: BankStatement = response.data.data

    // Create PDF document
    const doc = new jsPDF(A5_CONFIG)
    const pageWidth = doc.internal.pageSize.width

    // Define header content function
    const drawHeader = () => {
        // Add header
        doc.setFontSize(PDF_STYLES.header.fontSize)
        doc.text('Bank Statement', pageWidth / 2, 10, { align: 'center' })

        // Add bank details
        doc.setFontSize(PDF_STYLES.header.titleFontSize)
        doc.text([
            `Bank: ${statement.account.bankName}`,
            `Account: ${statement.account.accountNumber}`,
            `Period: ${dayjs(statement.startDate).format('DD/MM/YYYY')} - ${dayjs(statement.endDate).format('DD/MM/YYYY')}`,
        ], 10, 20)

        // Add summary with colors
        let xPos = pageWidth - 10
        let yPos = 20
        
        doc.setTextColor(0, 0, 0)
        doc.text('Opening Balance:', xPos, yPos, { align: 'right' })
        yPos += 5
        doc.text(statement.openingBalance.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }), xPos, yPos, { align: 'right' })
        
        yPos += 8
        doc.text('Total Credits:', xPos, yPos, { align: 'right' })
        yPos += 5
        doc.setTextColor(0, 128, 0) // Green for credits
        doc.text(statement.summary.totalCredits.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }), xPos, yPos, { align: 'right' })
        
        yPos += 8
        doc.setTextColor(0, 0, 0)
        doc.text('Total Debits:', xPos, yPos, { align: 'right' })
        yPos += 5
        doc.setTextColor(255, 0, 0) // Red for debits
        doc.text(statement.summary.totalDebits.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }), xPos, yPos, { align: 'right' })
        
        yPos += 8
        doc.setTextColor(0, 0, 0)
        doc.text('Closing Balance:', xPos, yPos, { align: 'right' })
        yPos += 5
        doc.setTextColor(statement.closingBalance >= 0 ? 0 : 255, statement.closingBalance >= 0 ? 128 : 0, 0)
        doc.text(statement.closingBalance.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }), xPos, yPos, { align: 'right' })

        // Reset text color
        doc.setTextColor(0, 0, 0)
    }

    // Draw the header once before the table
    drawHeader()

    // Add entries table with separate credit/debit columns
    // @ts-ignore (jspdf-autotable types are not properly recognized)
    doc.autoTable({
        startY: 70,
        columns: [
            { header: 'S.No', dataKey: 'serialNumber' },
            { header: 'Date', dataKey: 'date' },
            { header: 'Description', dataKey: 'description' },
            { header: 'Credit', dataKey: 'credit' },
            { header: 'Debit', dataKey: 'debit' },
            { header: 'Balance', dataKey: 'balance' }
        ],
        body: statement.entries.map((entry, index) => ({
            serialNumber: index + 1,
            date: dayjs(entry.date).format('DD/MM/YYYY'),
            description: entry.description,
            credit: entry.credit ? entry.credit.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }) : '',
            debit: entry.debit ? entry.debit.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }) : '',
            balance: entry.runningBalance.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 }),
            _credit: entry.credit,
            _debit: entry.debit,
            _balance: entry.runningBalance
        })),
        styles: {
            ...PDF_STYLES.table.styles,
            fontSize: 7, // Smaller font size for A5
            fillColor: [255, 255, 255] // Ensure all cells have white background
        },
        headStyles: {
            ...PDF_STYLES.table.headStyles,
            textColor: [0, 0, 0], // Black text for header
            fillColor: [240, 240, 240] // Light gray background for header
        },
        footStyles: PDF_STYLES.table.footStyles,
        margin: { left: 5, right: 5 },
        columnStyles: {
            serialNumber: { cellWidth: 10, halign: 'center' },
            date: { cellWidth: 18 },
            description: { cellWidth: 'auto' },
            credit: {
                cellWidth: 20,
                halign: 'right'
            },
            debit: {
                cellWidth: 20,
                halign: 'right'
            },
            balance: {
                cellWidth: 22,
                halign: 'right'
            }
        },
        didParseCell: function(data) {
            if (data.section === 'body') {
                // Color the credit values green
                if (data.column.dataKey === 'credit' && data.row.raw._credit) {
                    data.cell.styles.textColor = [0, 128, 0]; // Green
                }
                
                // Color the debit values red
                if (data.column.dataKey === 'debit' && data.row.raw._debit) {
                    data.cell.styles.textColor = [255, 0, 0]; // Red
                }
                
                // Color the balance based on value
                if (data.column.dataKey === 'balance') {
                    data.cell.styles.textColor = data.row.raw._balance >= 0 ? [0, 128, 0] : [255, 0, 0];
                }
            }
        }
    })

    // Add footer
    const pageCount = (doc as any).internal.getNumberOfPages()
    for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i)
        doc.setFontSize(PDF_STYLES.footer.fontSize)
        doc.setTextColor(0, 0, 0) // Reset to black for footer
        doc.text(
            `Page ${i} of ${pageCount}`,
            pageWidth / 2,
            doc.internal.pageSize.height - PDF_STYLES.footer.margin,
            { align: 'center' }
        )
    }

    return doc
}

export const handleBankStatementPDF = async (
    bankId: string,
    bankName: string,
    startDate: Date,
    endDate: Date,
    action: 'save' | 'print'
): Promise<void> => {
    try {
        const doc = await generateBankStatementPDF(bankId, startDate, endDate)
        const fileName = `${bankName}_Statement_${dayjs(startDate).format('YYYY-MM-DD')}_to_${dayjs(endDate).format('YYYY-MM-DD')}`

        if (action === 'save') {
            await savePDF(doc, fileName)
        } else {
            await printPDF(doc)
        }
    } catch (error) {
        console.error('Failed to generate PDF:', error)
        throw error
    }
}
