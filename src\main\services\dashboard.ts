import { prisma } from '../db'
import { Prisma, TransactionType, TransactionLocation, ItemType } from '@prisma/client'
import {
    TimeRange,
    CashOverview,
    BankAccountSummary,
    TransactionStats,
    AccountStats,
    InventorySummary,
    CurrencyTransactionSummary,
    HighValueTransaction,
    PropertyStatusSummary,
    TransactionTrend,
    AccountBalanceSummary,
    LocationTransferSummary,
    SalesTrendData
} from '@/common/types'
import dayjs from 'dayjs'

class DashboardService {
    // QUICK LOADING FUNCTIONS

    /**
     * Get current cash balances from all locations
     * UI: Card Grid with currency icons
     */
    async getCashOverview(): Promise<CashOverview> {
        const [smallCounter, cashVault, bankAccounts] = await Promise.all([
            prisma.smallCounter.findFirst(),
            prisma.cashVault.findFirst(),
            prisma.bankAccount.aggregate({
                where: { isActive: true },
                _sum: { balance: true }
            })
        ])

        if (!smallCounter || !cashVault) {
            throw new Error('Cash locations not initialized')
        }

        const bankBalance = bankAccounts._sum.balance || 0

        return {
            smallCounter: {
                pkr: smallCounter.pkrBalance,
                usd: smallCounter.usdBalance,
                aed: smallCounter.aedBalance,
                afn: smallCounter.afnBalance
            },
            cashVault: {
                pkr: cashVault.pkrBalance,
                usd: cashVault.usdBalance,
                aed: cashVault.aedBalance,
                afn: cashVault.afnBalance
            },
            bankAccounts: bankBalance,
            totalPKR: smallCounter.pkrBalance + cashVault.pkrBalance + bankBalance,
            totalByLocation: {
                smallCounter: smallCounter.pkrBalance + smallCounter.usdBalance + smallCounter.aedBalance + smallCounter.afnBalance,
                cashVault: cashVault.pkrBalance + cashVault.usdBalance + cashVault.aedBalance + cashVault.afnBalance,
                bankAccounts: bankBalance
            }
        }
    }

    /**
     * Get active bank accounts with balances
     * UI: Card Grid with bank icons
     */
    async getBankAccountsSummary(): Promise<BankAccountSummary[]> {
        return await prisma.bankAccount.findMany({
            where: { isActive: true },
            select: {
                id: true,
                accountNumber: true,
                bankName: true,
                balance: true
            }
        })
    }

    /**
     * Get transaction statistics for the current day
     * UI: Statistic Cards with icons
     */
    async getTransactionStats(timeRange: TimeRange): Promise<TransactionStats> {
        const startDate = this.getStartDate(timeRange)

        const transactions = await prisma.ledgerEntry.groupBy({
            by: ['type', 'transactionType'],
            where: {
                date: { gte: startDate },
                isDeleted: false
            },
            _count: true
        })

        const stats = {
            totalTransactions: 0,
            creditCount: 0,
            debitCount: 0,
            volumeByType: {} as Record<string, number>
        }

        transactions.forEach(tx => {
            const count = tx._count
            stats.totalTransactions += count
            if (tx.type === 'CREDIT') stats.creditCount += count
            if (tx.type === 'DEBIT') stats.debitCount += count
            stats.volumeByType[tx.transactionType] = (stats.volumeByType[tx.transactionType] || 0) + count
        })

        return stats
    }

    /**
     * Get active accounts count
     * UI: Info Cards
     */
    async getAccountStats(): Promise<AccountStats> {
        const accounts = await prisma.account.groupBy({
            by: ['type'],
            where: { isDeleted: false },
            _count: true
        })

        const stats = {
            customers: 0,
            tenants: 0,
            both: 0,
            total: 0
        }

        accounts.forEach(acc => {
            const count = acc._count
            if (acc.type === 'CUSTOMER') stats.customers = count
            if (acc.type === 'TENANT') stats.tenants = count
            if (acc.type === 'BOTH') stats.both = count
            stats.total += count
        })

        return stats
    }

    /**
     * Get available inventory counts
     * UI: Card Grid with icons
     */
    async getInventorySummary(): Promise<InventorySummary> {
        const [cars, parts, electronics, scrap] = await Promise.all([
            prisma.car.count({
                where: { status: 'AVAILABLE', isDeleted: false }
            }),
            prisma.carPart.count({
                where: { status: 'AVAILABLE', isDeleted: false }
            }),
            prisma.electronic.count({
                where: { status: 'AVAILABLE', isDeleted: false }
            }),
            prisma.scrap.count({
                where: { status: 'AVAILABLE', isDeleted: false }
            })
        ])

        return {
            cars,
            parts,
            electronics,
            scrap,
            totalItems: cars + parts + electronics + scrap
        }
    }

    // MODERATELY QUICK FUNCTIONS

    /**
     * Get currency-wise transaction summary
     * UI: Table with colored amounts
     */
    async getCurrencyTransactions(timeRange: TimeRange): Promise<CurrencyTransactionSummary[]> {
        const startDate = this.getStartDate(timeRange)

        const transactions = await prisma.ledgerEntry.groupBy({
            by: ['currencyId', 'type'],
            where: {
                date: { gte: startDate },
                isDeleted: false
            },
            _sum: { amount: true },
            _count: true
        })

        const currencyMap = new Map<string, CurrencyTransactionSummary>()

        // Get all currencies first
        const currencies = await prisma.currency.findMany({
            where: { isDeleted: false }
        })

        // Initialize summaries
        currencies.forEach(currency => {
            currencyMap.set(currency.id, {
                currency: currency.code,
                credits: 0,
                debits: 0,
                transactionCount: 0,
                net: 0
            })
        })

        // Aggregate transactions
        transactions.forEach(tx => {
            const summary = currencyMap.get(tx.currencyId)
            if (summary) {
                if (tx.type === 'CREDIT') {
                    summary.credits += tx._sum.amount || 0
                } else {
                    summary.debits += tx._sum.amount || 0
                }
                summary.transactionCount += tx._count
                summary.net = summary.credits - summary.debits
            }
        })

        return Array.from(currencyMap.values())
    }

    /**
     * Get account receivables and payables summary
     * UI: Table with colored amounts
     */
    async getAccountBalances(): Promise<AccountBalanceSummary[]> {
        const balances = await prisma.accountBalance.groupBy({
            by: ['currencyId'],
            where: {
                account: { isDeleted: false }
            },
            _sum: { balance: true }
        })

        const currencies = await prisma.currency.findMany({
            where: { isDeleted: false }
        })

        return currencies.map(currency => {
            const balance = balances.find(b => b.currencyId === currency.id)
            const total = balance?._sum.balance || 0
            return {
                currency: currency.code,
                receivables: total > 0 ? total : 0,
                payables: total < 0 ? Math.abs(total) : 0,
                net: total
            }
        })
    }

    /**
     * Get location transfer summary
     * UI: Table with flow indicators
     */
    async getLocationTransfers(timeRange: TimeRange): Promise<LocationTransferSummary[]> {
        const startDate = this.getStartDate(timeRange)

        const transfers = await prisma.ledgerEntry.groupBy({
            by: ['sourceType', 'destinationType', 'currencyId', 'type'],
            where: {
                date: { gte: startDate },
                isDeleted: false,
                transactionType: 'TRANSFER'
            },
            _sum: { amount: true }
        })

        const currencies = await prisma.currency.findMany({
            where: { isDeleted: false }
        })

        const locations = Object.values(TransactionLocation)
        const summaryMap = new Map<string, LocationTransferSummary>()

        // Initialize summaries
        locations.forEach(location => {
            summaryMap.set(location, {
                location,
                inflow: {},
                outflow: {},
                net: {}
            })

            // Initialize currency amounts
            currencies.forEach(currency => {
                const summary = summaryMap.get(location)!
                summary.inflow[currency.code] = 0
                summary.outflow[currency.code] = 0
                summary.net[currency.code] = 0
            })
        })

        // Aggregate transfers
        transfers.forEach(transfer => {
            const currency = currencies.find(c => c.id === transfer.currencyId)
            if (!currency) return

            const amount = transfer._sum.amount || 0
            const currencyCode = currency.code

            // Update source location (outflow)
            const sourceLocation = summaryMap.get(transfer.sourceType)
            if (sourceLocation) {
                sourceLocation.outflow[currencyCode] += amount
                sourceLocation.net[currencyCode] -= amount
            }

            // Update destination location (inflow)
            const destLocation = summaryMap.get(transfer.destinationType)
            if (destLocation) {
                destLocation.inflow[currencyCode] += amount
                destLocation.net[currencyCode] += amount
            }
        })

        return Array.from(summaryMap.values())
    }

    /**
     * Get sales trends with walk-in vs registered breakdown
     * UI: Line Chart with multiple series
     */
    async getSalesTrends(timeRange: TimeRange): Promise<SalesTrendData[]> {
        const startDate = this.getStartDate(timeRange)
        const format = timeRange === '1D' ? 'HH:00' : 'YYYY-MM-DD'

        const sales = await prisma.sale.findMany({
            where: {
                date: { gte: startDate },
                isDeleted: false
            },
            include: {
                account: true,
                car: true,
                carPart: true,
                electronic: true,
                scrap: true
            }
        })

        const dateMap = new Map<string, SalesTrendData>()

        // Initialize all dates in the range
        let currentDate = dayjs(startDate)
        const endDate = dayjs()

        while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
            const dateKey = currentDate.format(format)
            dateMap.set(dateKey, {
                date: dateKey,
                walkIn: { quantity: 0, amount: 0 },
                registered: { quantity: 0, amount: 0 },
                total: { quantity: 0, amount: 0 },
                byType: {
                    cars: { quantity: 0, amount: 0 },
                    parts: { quantity: 0, amount: 0 },
                    electronics: { quantity: 0, amount: 0 },
                    scrap: { quantity: 0, amount: 0 }
                }
            })
            currentDate = timeRange === '1D'
                ? currentDate.add(1, 'hour')
                : currentDate.add(1, 'day')
        }

        // Aggregate sales data
        sales.forEach(sale => {
            const dateKey = dayjs(sale.date).format(format)
            const data = dateMap.get(dateKey)
            if (!data) return

            const isWalkIn = !sale.accountId
            const target = isWalkIn ? data.walkIn : data.registered

            target.quantity += sale.quantity
            target.amount += sale.totalAmount

            data.total.quantity += sale.quantity
            data.total.amount += sale.totalAmount

            // Aggregate by type
            if (sale.car) {
                data.byType.cars.quantity += 1
                data.byType.cars.amount += sale.totalAmount
            } else if (sale.carPart) {
                data.byType.parts.quantity += sale.quantity
                data.byType.parts.amount += sale.totalAmount
            } else if (sale.electronic) {
                data.byType.electronics.quantity += sale.quantity
                data.byType.electronics.amount += sale.totalAmount
            } else if (sale.scrap) {
                data.byType.scrap.quantity += sale.quantity
                data.byType.scrap.amount += sale.totalAmount
            }
        })

        // console.log(Array.from(dateMap.values()))

        return Array.from(dateMap.values())
    }

    // Helper function to get start date based on time range
    private getStartDate(timeRange: TimeRange): Date {
        const now = dayjs()
        switch (timeRange) {
            case '1D':
                return now.startOf('day').toDate()
            case '7D':
                return now.subtract(7, 'day').startOf('day').toDate()
            case '14D':
                return now.subtract(14, 'day').startOf('day').toDate()
            case '30D':
                return now.subtract(30, 'day').startOf('day').toDate()
            default:
                return now.startOf('day').toDate()
        }
    }
}

export const dashboardService = new DashboardService()
