import { http } from './http';
import { Channels } from '@/common/constants';
import type {
    CreateExpenseData,
    DeleteExpenseData,
    GetExpensesParams
} from '@/common/types';



export const createExpense = async (data: CreateExpenseData) => {
    return await http.post(Channels.CREATE_EXPENSE, {
        body: data
    });
};

export const deleteExpense = async (data: DeleteExpenseData) => {
    return await http.delete(Channels.DELETE_EXPENSE, {
        body: {
            id: data.id,
            reason: data.reason,
            userId: data.userId
        }
    });
};

export const getExpenses = async (params: GetExpensesParams) => {
    return await http.get(Channels.GET_EXPENSES, {
        query: params
    });
};

