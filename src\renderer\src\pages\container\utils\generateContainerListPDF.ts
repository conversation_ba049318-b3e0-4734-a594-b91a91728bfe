import { A5_CONFIG, PDF_STYLES, savePDF, printPDF } from '@/renderer/utils/pdfUtils'
import { containerApi } from '@/renderer/services'
import jsPDF from 'jspdf'
import 'jspdf-autotable'
import dayjs from 'dayjs'

export const generateContainerListPDF = async (
    startDate?: Date,
    endDate?: Date,
    partnerId?: string
): Promise<jsPDF> => {
    // Call the API to get container data
    const response = await containerApi.getContainersForPDF(
        startDate || new Date(0), // Use Unix epoch as fallback
        endDate || new Date(),    // Use current date as fallback
        partnerId || ""           // Use empty string as fallback
    );

    if (response.error.error || response.data.error) {
        throw new Error(response.error.message || response.data.error.message);
    }

    const { containers, summary } = response.data.data;

    // Create PDF document (using A4 landscape for container list to fit more columns)
    const doc = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4'
    });

    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;

    // Define reusable function to draw the header
    const drawHeader = () => {
        // Add header
        doc.setFontSize(PDF_STYLES.header.fontSize);
        doc.text('Container List', pageWidth / 2, 15, { align: 'center' });

        // Add filter info
        doc.setFontSize(PDF_STYLES.header.titleFontSize);
        let filterText = 'All Containers';
        if (startDate && endDate) {
            filterText = `Period: ${dayjs(startDate).format('DD/MM/YYYY')} - ${dayjs(endDate).format('DD/MM/YYYY')}`;
        }
        if (partnerId) {
            filterText += ` | Partner: ${containers.find(c => c.partner)?.partner || 'Selected Partner'}`;
        }

        doc.text(filterText, pageWidth / 2, 22, { align: 'center' });

        // Add summary box
        const summaryY = 28;
        doc.setFillColor(240, 240, 240);
        doc.rect(10, summaryY, pageWidth - 20, 20, 'F');

        doc.setFontSize(10);
        doc.setTextColor(0, 0, 0);
        doc.text(`Total Containers: ${summary.totalContainers}`, 15, summaryY + 6);

        // Format total cost with proper formatting to prevent overflow
        const totalCostFormatted = summary.totalCost.toLocaleString('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        });
        doc.text(`Total Cost: ${totalCostFormatted}`, 15, summaryY + 12);

        // Right aligned profit info with color
        doc.text('Sales Minus Expenses:', pageWidth - 15, summaryY + 6, { align: 'right' });

        // Set color based on profit or loss
        const isProfit = summary.totalSalesMinusExpenses >= 0;
        doc.setTextColor(isProfit ? 0 : 255, isProfit ? 128 : 0, 0);

        // Format sales minus expenses with proper formatting to prevent overflow
        const salesMinusExpensesFormatted = summary.totalSalesMinusExpenses.toLocaleString('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        });
        doc.text(
            salesMinusExpensesFormatted,
            pageWidth - 15,
            summaryY + 12,
            { align: 'right' }
        );
        doc.setTextColor(0, 0, 0); // Reset text color

        return summaryY + 25; // Return the Y position where the header ends
    }

    // Draw the header and get the starting Y position for the table
    const tableStartY = drawHeader();

    // Calculate the table width to fit the page
    const tableMargin = 10; // margin on both sides
    const availableWidth = pageWidth - (tableMargin * 2);

    // Add containers table
    // @ts-ignore (jspdf-autotable types are not properly recognized)
    doc.autoTable({
        startY: tableStartY,
        margin: { left: tableMargin, right: tableMargin },
        tableWidth: availableWidth,
        columns: [
            { header: 'Container #', dataKey: 'containerNumber' },
            { header: 'Date Opened', dataKey: 'date' },
            { header: 'Partner', dataKey: 'partner' },
            { header: 'Total Cost', dataKey: 'totalCost' },
            { header: 'Sales - Expenses', dataKey: 'salesMinusExpenses' }
        ],
        body: containers.map(container => ({
            containerNumber: container.containerNumber,
            date: dayjs(container.openedAt).format('DD/MM/YYYY'),
            partner: container.partner || '-',
            totalCost: container.totalCost.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 }),
            salesMinusExpenses: container.salesMinusExpenses.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 }),
            _salesMinusExpenses: container.salesMinusExpenses // Hidden field for color calculation
        })),
        styles: {
            ...PDF_STYLES.table.styles,
            overflow: 'ellipsize' // Prevent text from overflowing
        },
        headStyles: PDF_STYLES.table.headStyles,
        footStyles: PDF_STYLES.table.footStyles,
        columnStyles: {
            containerNumber: {
                cellWidth: availableWidth * 0.20 // 20% of available width
            },
            date: {
                cellWidth: availableWidth * 0.15 // 15% of available width
            },
            partner: {
                cellWidth: availableWidth * 0.21 // 21% of available width  
            },
            totalCost: {
                cellWidth: availableWidth * 0.22, // 22% of available width
                halign: 'right'
            },
            salesMinusExpenses: {
                cellWidth: availableWidth * 0.22, // 22% of available width
                halign: 'right'
            }
        },
        didParseCell: function (data) {
            // Only style non-header cells
            if (data.section === 'body') {
                if (data.column.dataKey === 'salesMinusExpenses') {
                    // Get the raw value from our hidden field
                    const row = data.row.raw;
                    const value = row._salesMinusExpenses;

                    // Set color based on profit or loss
                    data.cell.styles.textColor = value >= 0 ? [0, 128, 0] : [255, 0, 0];
                }
            }
        },
        // Ensure header isn't repeated on subsequent pages
        showHead: 'firstPage'
    });

    // Add footer with page numbers
    const pageCount = (doc as any).internal.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(PDF_STYLES.footer.fontSize);
        doc.text(
            `Page ${i} of ${pageCount} - Generated on ${dayjs().format('DD/MM/YYYY HH:mm')}`,
            pageWidth / 2,
            pageHeight - PDF_STYLES.footer.margin,
            { align: 'center' }
        );
    }

    return doc;
}

export const handleContainerListPDF = async (
    startDate: Date | undefined,
    endDate: Date | undefined,
    partnerId: string | undefined,
    action: 'save' | 'print'
): Promise<void> => {
    try {
        const doc = await generateContainerListPDF(startDate, endDate, partnerId);
        const fileName = `Container_List_${dayjs().format('YYYY-MM-DD')}`;

        if (action === 'save') {
            await savePDF(doc, fileName);
        } else {
            await printPDF(doc);
        }
    } catch (error) {
        console.error('Failed to generate PDF:', error);
        throw error;
    }
}
