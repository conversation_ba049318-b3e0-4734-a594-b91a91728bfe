import { prisma } from '../db'
import type { CreatePropertyData, GetPropertiesParams, UpdatePropertyData } from '@/common/types'
import { Prisma } from '@prisma/client'

class PropertyService {
    async createProperty(data: CreatePropertyData) {
        return prisma.property.create({
            data
        })
    }

    async updateProperty(id: string, data: UpdatePropertyData) {
        return prisma.property.update({
            where: { id },
            data
        })
    }

    async deleteProperty(id: string, reason: string) {
        return prisma.property.update({
            where: { id },
            data: {
                isDeleted: true,
                deleteReason: reason,
                deletedAt: new Date()
            }
        })
    }

    async getProperty(id: string) {
        return prisma.property.findFirst({
            where: {
                id,
                isDeleted: false
            },
            include: {
                rentals: {
                    where: { isDeleted: false },
                    orderBy: { startDate: 'desc' },
                    include: {
                        tenant: true,
                    }
                }
            }
        })
    }

    async getProperties({ page = 1, limit = 20, search, type, isAvailable }: GetPropertiesParams) {
        const where: Prisma.PropertyWhereInput = {
            isDeleted: false,
            ...(search && {
                OR: [
                    { name: { contains: search, mode: 'insensitive' as Prisma.QueryMode } },
                    { address: { contains: search, mode: 'insensitive' as Prisma.QueryMode } }
                ]
            }),

            // Properly handles the isAvailable filter to show:
            // All properties when isAvailable is undefined
            // Only available properties when isAvailable is true
            // Only rented properties when isAvailable is false
            ...(type && { type }),
            ...(typeof isAvailable === 'boolean' && {
                rentals: isAvailable ? {
                    none: {
                        isDeleted: false,
                        OR: [
                            { endDate: null },
                            { endDate: { gt: new Date() } }
                        ]
                    }
                } : {
                    some: {
                        isDeleted: false,
                        OR: [
                            { endDate: null },
                            { endDate: { gt: new Date() } }
                        ]
                    }
                }
            })
        }

        const [total, properties] = await Promise.all([
            prisma.property.count({ where }),
            prisma.property.findMany({
                where,
                // include: {
                //     rentals: {
                //         where: { isDeleted: false },
                //         // orderBy: { startDate: 'desc' },
                //         // include: {
                //         //     tenant: true,
                //         //     rentPayments: {
                //         //         where: { isDeleted: false },
                //         //         orderBy: { date: 'desc' },
                //         //         take: 1
                //         //     }
                //         // }
                //     }
                // },
                include: { currentTenant: true },
                skip: (page - 1) * limit,
                take: limit,
                orderBy: { name: 'asc' }
            })
        ])

        return {
            properties,
            pagination: {
                total,
                page,
                limit
            }
        }
    }

    // Check if a property is available for rent
    async isPropertyAvailable(id: string) {
        const property = await prisma.property.findFirst({
            where: {
                id,
                isDeleted: false,
                rentals: {
                    none: {
                        isDeleted: false,
                        OR: [
                            { endDate: null },
                            { endDate: { gt: new Date() } }
                        ]
                    }
                }
            }
        })

        return !!property
    }

    // Get rental history of a property
    async getPropertyHistory(id: string) {
        return prisma.rentedProperty.findMany({
            where: {
                propertyId: id,
                isDeleted: false
            },
            include: {
                tenant: true,
            },
            orderBy: {
                startDate: 'desc'
            }
        })
    }

    // Get list of properties not currently rented
    async getVacantProperties() {
        const today = new Date()

        return prisma.property.findMany({
            where: {
                isDeleted: false,
                // No active rentals
                rentals: {
                    none: {
                        isDeleted: false,
                        OR: [
                            { endDate: null },
                            { endDate: { gt: today } }
                        ]
                    }
                }
            },
            orderBy: {
                name: 'asc'
            }
        })
    }

    // When a property is rented
    async updatePropertyRentalStatus(
        propertyId: string,
        tenantId: string,
        rentAmount: number,
        isRented: boolean = true
    ) {
        await prisma.property.update({
            where: { id: propertyId },
            data: {
                isRented,
                currentRent: isRented ? rentAmount : null,
                currentTenantId: isRented ? tenantId : null,
            }
        });
    }

    // Reset property rental status (called when rental ends/cancels)
    async resetPropertyRentalStatus(propertyId: string) {
        await prisma.property.update({
            where: { id: propertyId },
            data: {
                isRented: false,
                currentRent: null,
                currentTenantId: null,
            }
        });
    }
}

export const propertyService = new PropertyService();
