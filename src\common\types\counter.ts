import { LedgerEntry, C<PERSON><PERSON>cy, User, Account } from '@prisma/client'

// Common types for both Small Counter and Cash Vault
export interface CounterBalances {
    pkrBalance: number
    usdBalance: number
    aedBalance: number
    afnBalance: number
    lastUpdated: Date
}

export interface TransactionWithRelations extends LedgerEntry {
    currency: Currency
    createdBy: { name: string }
    account?: { name: string } | null
}

export interface TransactionsResponse {
    transactions: TransactionWithRelations[]
    pagination: {
        total: number
        pages: number
        currentPage: number
        pageSize: number
    }
}

export interface BalanceReconciliation {
    isReconciled: boolean
    currentBalance: number
    calculatedBalance: number
    difference: number
}

export interface DailyBalance {
    date: Date
    amount: number
    description: string
    type: 'CREDIT' | 'DEBIT'
    createdBy: string
}

export interface TransferData {
    currency: string
    amount: number
    userId: string
}

export interface AdjustBalanceData {
    currency: string
    adjustment: number
    reason: string
    userId: string
}