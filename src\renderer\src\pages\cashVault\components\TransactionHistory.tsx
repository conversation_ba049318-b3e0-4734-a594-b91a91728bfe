import { Table, Select, DatePicker, Button, Tag, Radio, Tooltip } from 'antd'
import { useState, useEffect } from 'react'
import { useApi } from '@/renderer/hooks'
import { smallCounterApi, cashVaultApi } from '@/renderer/services'
import { formatDate, formatCurrency } from '@/renderer/utils'
import type { TransactionsResponse, TransactionWithRelations } from '@/common/types'
import type { RadioChangeEvent } from 'antd'
import dayjs from 'dayjs'

const { RangePicker } = DatePicker

type LocationType = 'SMALL_COUNTER' | 'CASH_VAULT'

export const TransactionHistory = () => {
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  // const [location, setLocation] = useState<LocationType>('CASH_VAULT')
  const [dateRange, setDateRange] = useState<[Date, Date] | null>(null)
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')

  const {
    data: transactionsData,
    isLoading,
    request: fetchTransactions
  } = useApi<
    TransactionsResponse,
    [number, number, Date | undefined, Date | undefined, 'asc' | 'desc']
  >(
    // location === 'SMALL_COUNTER'
    //   ? smallCounterApi.getAllTransactions
    cashVaultApi.getAllTransactions
  )

  useEffect(() => {
    fetchTransactions(page, pageSize, dateRange?.[0], dateRange?.[1], sortOrder)
  }, [page, pageSize, dateRange, sortOrder])

  const columns = [
    {
      title: 'Sr. No',
      key: 'serialNumber',
      width: 70,
      render: (_: any, __: any, index: number) => {
        // Calculate serial number based on current page and page size
        return (page - 1) * pageSize + index + 1
      }
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => formatDate(date)
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      width: 300,
      render: (description: string) => {
        const maxLength = 40 // You can adjust this number to show more/less characters
        const displayText =
          description.length > maxLength ? `${description.slice(0, maxLength)}...` : description

        return (
          <Tooltip title={description}>
            <span>{displayText}</span>
          </Tooltip>
        )
      }
    },
    {
      title: 'Amount',
      key: 'amount',
      render: (record: TransactionWithRelations) => (
        <span className={record.type === 'CREDIT' ? 'text-green-600' : 'text-red-600'}>
          {formatCurrency(record.amount, record.currency.code)}
        </span>
      )
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => <Tag color={type === 'CREDIT' ? 'green' : 'red'}>{type}</Tag>
    },
    {
      title: 'Source',
      dataIndex: 'sourceType',
      key: 'sourceType'
    },
    {
      title: 'Destination',
      dataIndex: 'destinationType',
      key: 'destinationType'
    },
    {
      title: 'Account',
      dataIndex: ['account', 'name'],
      key: 'account'
    },
    {
      title: 'Created By',
      dataIndex: ['createdBy', 'name'],
      key: 'createdBy'
    }
  ]

  return (
    <div className="rounded-lg">
      <div className="mb-4 flex gap-4">
        {/* <Radio.Group
          value={location}
          onChange={(e: RadioChangeEvent) => {
            setLocation(e.target.value)
            setPage(1)
            setDateRange(null)
          }}
        >
          <Radio.Button value="SMALL_COUNTER">Small Counter</Radio.Button>
          <Radio.Button value="CASH_VAULT">Cash Vault</Radio.Button>
        </Radio.Group> */}

        <RangePicker
          value={dateRange ? [dayjs(dateRange[0]), dayjs(dateRange[1])] : null}
          onChange={(_, dateStrings) => {
            if (dateStrings[0] && dateStrings[1]) {
              setDateRange([new Date(dateStrings[0]), new Date(dateStrings[1])])
              setPage(1)
            } else {
              setDateRange(null)
            }
          }}
        />

        <Select value={sortOrder} onChange={setSortOrder} style={{ width: 150 }}>
          <Select.Option value="desc">Newest First</Select.Option>
          <Select.Option value="asc">Oldest First</Select.Option>
        </Select>

        <Button
          onClick={() => {
            setPage(1)
            setDateRange(null)
          }}
        >
          Reset Filters
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={transactionsData?.transactions}
        rowKey="id"
        loading={isLoading}
        virtual
        sticky
        size="small"
        pagination={{
          position: ['topRight'],
          showSizeChanger: true,
          showQuickJumper: true,
          showPrevNextJumpers: true,
          current: page,
          pageSize,
          total: transactionsData?.pagination.total,
          onChange: (page, pageSize) => {
            setPage(page)
            setPageSize(pageSize)
          },
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
        }}
      />
    </div>
  )
}
