import { useState, useEffect } from 'react'
import { Card, Select, DatePicker, Table, Statistic, Row, Col, App } from 'antd'
import type { PaymentSummary as PaymentSummaryType } from '@/common/types'
import { paymentApi } from '@/renderer/services'
import { useAccountContext } from '@/renderer/contexts'

const { RangePicker } = DatePicker

export const PaymentSummary = () => {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<PaymentSummaryType | null>(null)
  const [selectedAccount, setSelectedAccount] = useState<string>()
  const [dateRange, setDateRange] = useState<[Date?, Date?]>([])

  const { message } = App.useApp()

  const { accounts } = useAccountContext()

  const fetchSummary = async () => {
    if (!selectedAccount) return

    setLoading(true)
    const response = await paymentApi.getPaymentSummaryByAccount(
      selectedAccount,
      dateRange[0],
      dateRange[1]
    )
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    setData(response.data.data)
  }

  useEffect(() => {
    if (selectedAccount) {
      fetchSummary()
    }
  }, [selectedAccount, dateRange])

  const columns = [
    {
      title: 'Currency',
      dataIndex: 'currency',
      key: 'currency'
    },
    {
      title: 'Paid',
      dataIndex: 'paid',
      key: 'paid',
      render: (value: number) => <span className="text-red-500">{value.toLocaleString()}</span>
    },
    {
      title: 'Received',
      dataIndex: 'received',
      key: 'received',
      render: (value: number) => <span className="text-green-500">{value.toLocaleString()}</span>
    },
    {
      title: 'Net',
      key: 'net',
      render: (_: any, record: any) => (
        <span className={record.received - record.paid >= 0 ? 'text-green-500' : 'text-red-500'}>
          {(record.received - record.paid).toLocaleString()}
        </span>
      )
    },
    {
      title: 'Transactions',
      dataIndex: 'totalTransactions',
      key: 'totalTransactions'
    }
  ]

  const tableData = data
    ? Object.entries(data).map(([currency, stats]) => ({
        key: currency,
        currency,
        ...stats
      }))
    : []

  const totalPaid = tableData.reduce((sum, row) => sum + row.paid, 0)
  const totalReceived = tableData.reduce((sum, row) => sum + row.received, 0)
  const totalTransactions = tableData.reduce((sum, row) => sum + row.totalTransactions, 0)

  return (
    <Card className="mt-4">
      <div className="mb-6 flex items-center gap-4">
        <Select
          showSearch
          placeholder="Select Account"
          className="w-64"
          value={selectedAccount}
          onChange={setSelectedAccount}
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
          options={accounts}
        />
        <RangePicker
          onChange={(_, dateStrings) => {
            setDateRange([
              dateStrings[0] ? new Date(dateStrings[0]) : undefined,
              dateStrings[1] ? new Date(dateStrings[1]) : undefined
            ])
          }}
          className="w-64"
        />
      </div>

      {selectedAccount && (
        <>
          <Row gutter={16} className="mb-6">
            <Col span={8}>
              <Card>
                <Statistic
                  title="Total Paid"
                  value={totalPaid}
                  precision={2}
                  valueStyle={{ color: '#cf1322' }}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic
                  title="Total Received"
                  value={totalReceived}
                  precision={2}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic title="Total Transactions" value={totalTransactions} />
              </Card>
            </Col>
          </Row>

          <Table columns={columns} dataSource={tableData} loading={loading} pagination={false} />
        </>
      )}
    </Card>
  )
}
