import { useEffect } from 'react'
import { Typography, Space, Button, Alert, Skeleton, Row, Col, Statistic, Table } from 'antd'
import { useApi } from '@/renderer/hooks'
import { dashboardApi } from '@/renderer/services'
import { BankAccountSummary } from '@/common/types'
import { FaUniversity, FaSync } from 'react-icons/fa'
import { formatCurrency } from '@/renderer/utils'
import { useTheme } from '@/renderer/contexts'

const { Title } = Typography

const BankAccountsSummary = () => {
  const { isDarkMode } = useTheme()

  const {
    data,
    isLoading,
    error,
    request: fetchBankSummary
  } = useApi<BankAccountSummary[], []>(dashboardApi.getBankAccountsSummary)

  useEffect(() => {
    fetchBankSummary()
  }, [])

  const columns = [
    {
      title: 'Bank',
      dataIndex: 'bankName',
      key: 'bankName'
    },
    {
      title: 'Account',
      dataIndex: 'accountNumber',
      key: 'accountN<PERSON>ber'
    },
    {
      title: 'Balance',
      dataIndex: 'balance',
      key: 'balance',
      align: 'right' as const,
      render: (value: number) => formatCurrency(value, 'PKR')
    }
  ]

  if (isLoading) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <FaUniversity className="text-2xl text-blue-600" />
            <Title level={5} className="!mb-0">
              Bank Accounts
            </Title>
          </Space>
          <Button icon={<FaSync />} loading />
        </Space>
        <Skeleton active paragraph={{ rows: 4 }} />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col gap-4">
        <Space className="flex justify-between">
          <Space>
            <FaUniversity className="text-2xl text-blue-600" />
            <Title level={5} className="!mb-0">
              Bank Accounts
            </Title>
          </Space>
          <Button icon={<FaSync />} onClick={fetchBankSummary} />
        </Space>
        <Alert
          message="Error"
          description="Failed to load bank accounts data"
          type="error"
          showIcon
        />
      </div>
    )
  }

  if (!data) return null

  const totalBalance = data.reduce((sum, account) => sum + account.balance, 0)

  return (
    <div className="flex flex-col gap-4">
      <Space className="flex justify-between">
        <Space>
          <FaUniversity className="text-2xl text-blue-600" />
          <Title level={5} className="!mb-0">
            Bank Accounts
          </Title>
        </Space>
        <Button icon={<FaSync />} onClick={fetchBankSummary} />
      </Space>

      <div className={`rounded-lg p-4 ${isDarkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Statistic
              title="Total Bank Balance"
              value={totalBalance}
              formatter={(value) => formatCurrency(value as number, 'PKR')}
            />
          </Col>
          <Col span={24}>
            <Table
              dataSource={data}
              columns={columns}
              pagination={false}
              size="small"
              className={isDarkMode ? 'dark-table' : ''}
            />
          </Col>
        </Row>
      </div>
    </div>
  )
}

export default BankAccountsSummary
