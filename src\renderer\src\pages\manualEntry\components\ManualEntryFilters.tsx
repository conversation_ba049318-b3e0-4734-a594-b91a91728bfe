import React, { useState, useEffect } from 'react'
import { Form, Row, Col, DatePicker, Input, Button, Space, Tag, Dropdown, Select, Card } from 'antd'
import { ClearOutlined, PlusOutlined, FilterOutlined, DownOutlined } from '@ant-design/icons'
import { useTheme } from '@/renderer/contexts'
import { ManualEntryStatus, ManualEntrySortOrder } from '@/renderer/services/manualEntry'
import { TransactionType } from '@prisma/client'
import type { ManualEntryFilters as FilterType } from '@/common/types/manualEntry'
import dayjs from 'dayjs'
import { useAccountContext, useBankContext } from '@/renderer/contexts'

const { RangePicker } = DatePicker
const { Option } = Select

interface ManualEntryFiltersProps {
  filters: FilterType
  onFiltersChange: (filters: FilterType) => void
  onCreateClick: () => void
}

export const ManualEntryFilters = ({
  filters,
  onFiltersChange,
  onCreateClick
}: ManualEntryFiltersProps) => {
  const [form] = Form.useForm()

  const { accounts } = useAccountContext()
  const { banks } = useBankContext()

  const { isDarkMode } = useTheme()

  // Available currencies
  const currencies = [
    { value: 'PKR', label: 'PKR' },
    { value: 'USD', label: 'USD' },
    { value: 'AED', label: 'AED' },
    { value: 'AFN', label: 'AFN' }
  ]

  const handleSearch = (searchValue?: string) => {
    const values = form.getFieldsValue()
    const newFilters: FilterType = {
      ...filters,
      search: searchValue !== undefined ? searchValue : values.search,
      dateRange: values.dateRange
        ? {
            startDate: values.dateRange[0].toDate(),
            endDate: values.dateRange[1].toDate()
          }
        : undefined
    }
    onFiltersChange(newFilters)
  }

  const handleRemoveFilter = (filterKey: keyof FilterType) => {
    const newFilters: FilterType = { ...filters }

    // Don't allow removing required filters, just reset them to defaults
    if (filterKey === 'status') {
      newFilters.status = ManualEntryStatus.ACTIVE
    } else if (filterKey === 'sortOrder') {
      newFilters.sortOrder = ManualEntrySortOrder.OLDEST_FIRST
    } else {
      delete newFilters[filterKey]
    }

    onFiltersChange(newFilters)
  }

  const handleClear = () => {
    form.resetFields()
    const clearedFilters: FilterType = {
      status: ManualEntryStatus.ACTIVE,
      sortOrder: ManualEntrySortOrder.OLDEST_FIRST
    }
    onFiltersChange(clearedFilters)
  }

  const getFilterLabel = (key: keyof FilterType, value: any): string => {
    switch (key) {
      case 'status':
        return value === ManualEntryStatus.ACTIVE
          ? 'Active'
          : value === ManualEntryStatus.DELETED
            ? 'Deleted'
            : 'All'
      case 'sortOrder':
        return value === ManualEntrySortOrder.OLDEST_FIRST ? 'Oldest First' : 'Newest First'
      case 'entryType':
        return value === TransactionType.CREDIT ? 'Credit' : 'Debit'
      case 'targetType':
        return value === 'CASH_VAULT' ? 'Cash Vault' : 'Unknown Target'
      case 'accountId':
        const account = accounts.find((a) => a.value === value)
        return account ? account.label : 'Unknown Account'
      case 'bankAccountId':
        const bankAccount = banks.find((b) => b.id === value)
        return bankAccount ? bankAccount.label : 'Unknown Bank Account'
      case 'currencyCode':
        const currency = currencies.find((c) => c.value === value)
        return currency ? currency.label : 'Unknown Currency'
      default:
        return String(value)
    }
  }

  const renderFilterTags = () => {
    const tags: React.ReactNode[] = []

    Object.entries(filters).forEach(([key, value]) => {
      if (key === 'search' || key === 'dateRange' || !value) return

      // Required filters (status, sortOrder) should not be closable if they are defaults
      const isRequiredDefault =
        (key === 'status' && value === ManualEntryStatus.ACTIVE) ||
        (key === 'sortOrder' && value === ManualEntrySortOrder.OLDEST_FIRST)

      tags.push(
        <Tag
          key={key}
          closable={!isRequiredDefault}
          onClose={
            !isRequiredDefault ? () => handleRemoveFilter(key as keyof FilterType) : undefined
          }
          color={isRequiredDefault ? 'default' : 'blue'}
        >
          {getFilterLabel(key as keyof FilterType, value)}
        </Tag>
      )
    })

    if (filters.dateRange) {
      tags.push(
        <Tag key="dateRange" closable onClose={() => handleRemoveFilter('dateRange')} color="green">
          {dayjs(filters.dateRange.startDate).format('DD/MM/YYYY')} -{' '}
          {dayjs(filters.dateRange.endDate).format('DD/MM/YYYY')}
        </Tag>
      )
    }

    return tags
  }

  const initialValues = {
    search: filters.search,
    dateRange: filters.dateRange
      ? [dayjs(filters.dateRange.startDate), dayjs(filters.dateRange.endDate)]
      : undefined
  }

  const [filtersForm] = Form.useForm()
  const [filtersVisible, setFiltersVisible] = useState(false)

  const handleFilterChange = (field: string, value: any) => {
    const newFilters: FilterType = {
      ...filters,
      [field]: value
    }

    // Handle mutual exclusion - only one target type at a time
    if (field === 'accountId' && value) {
      newFilters.bankAccountId = undefined
      newFilters.targetType = undefined
      filtersForm.setFieldsValue({ bankAccountId: undefined, targetType: undefined })
    } else if (field === 'bankAccountId' && value) {
      newFilters.accountId = undefined
      newFilters.targetType = undefined
      filtersForm.setFieldsValue({ accountId: undefined, targetType: undefined })
    } else if (field === 'targetType' && value) {
      newFilters.accountId = undefined
      newFilters.bankAccountId = undefined
      filtersForm.setFieldsValue({ accountId: undefined, bankAccountId: undefined })
    }

    onFiltersChange(newFilters)
  }

  const filtersInitialValues = {
    status: filters.status || ManualEntryStatus.ACTIVE,
    sortOrder: filters.sortOrder || ManualEntrySortOrder.OLDEST_FIRST,
    entryType: filters.entryType,
    targetType: filters.targetType,
    accountId: filters.accountId,
    bankAccountId: filters.bankAccountId,
    currencyCode: filters.currencyCode
  }

  // Sync form values with current filters
  React.useEffect(() => {
    filtersForm.setFieldsValue(filtersInitialValues)
  }, [filters, filtersForm, filtersInitialValues])

  const filtersDropdownContent = (
    <Card className={`w-96 p-4 ${isDarkMode ? 'bg-black' : 'bg-white'} shadow-lg`}>
      <Form form={filtersForm} layout="vertical" initialValues={filtersInitialValues}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="status" label="Status">
              <Select
                placeholder="Select status"
                onChange={(value) => handleFilterChange('status', value)}
              >
                <Option value={ManualEntryStatus.ACTIVE}>Active</Option>
                <Option value={ManualEntryStatus.DELETED}>Deleted</Option>
                <Option value={ManualEntryStatus.ALL}>All</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item name="sortOrder" label="Sort Order">
              <Select
                placeholder="Select sort order"
                onChange={(value) => handleFilterChange('sortOrder', value)}
              >
                <Option value={ManualEntrySortOrder.OLDEST_FIRST}>Oldest First</Option>
                <Option value={ManualEntrySortOrder.NEWEST_FIRST}>Newest First</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item name="entryType" label="Entry Type">
              <Select
                placeholder="Select entry type"
                allowClear
                onChange={(value) => handleFilterChange('entryType', value)}
              >
                <Option value={TransactionType.CREDIT}>Credit</Option>
                <Option value={TransactionType.DEBIT}>Debit</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item name="currencyCode" label="Currency">
              <Select
                placeholder="Select currency"
                allowClear
                onChange={(value) => handleFilterChange('currencyCode', value)}
              >
                {currencies.map((currency) => (
                  <Option key={currency.value} value={currency.value}>
                    {currency.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item name="targetType" label="Target Type">
              <Select
                placeholder="Select target type"
                allowClear
                onChange={(value) => handleFilterChange('targetType', value)}
              >
                <Option value="CASH_VAULT">Cash Vault</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col span={12}>
            <div></div> {/* Empty space for alignment */}
          </Col>

          <Col span={24}>
            <Form.Item name="accountId" label="Account">
              <Select
                placeholder="Select account"
                allowClear
                showSearch
                onChange={(value) => handleFilterChange('accountId', value)}
                filterOption={(input, option) =>
                  (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
                }
                options={accounts}
              />
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item name="bankAccountId" label="Bank Account">
              <Select
                placeholder="Select bank account"
                allowClear
                showSearch
                onChange={(value) => handleFilterChange('bankAccountId', value)}
                filterOption={(input, option) =>
                  (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
                }
                options={banks}
              />
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item className="mb-0">
              <Button onClick={() => setFiltersVisible(false)} block>
                Close
              </Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Card>
  )

  return (
    <div>
      {/* Main Filter Row */}
      <Form form={form} layout="vertical" initialValues={initialValues}>
        <Row gutter={16} align="middle">
          <Col xs={24} sm={8} md={6} lg={5}>
            <Form.Item name="search" label="Search" className="mb-0">
              <Input
                placeholder="Search description, account, bank account..."
                allowClear
                onChange={(e) => handleSearch(e.target.value)}
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={8} md={6} lg={5}>
            <Form.Item name="dateRange" label="Date Range" className="mb-0">
              <RangePicker
                className="w-full"
                onChange={() => {
                  setTimeout(() => handleSearch(), 100)
                }}
              />
            </Form.Item>
          </Col>

          <Col xs={24} sm={8} md={6} lg={4}>
            <Form.Item label=" " className="mb-0">
              <Space>
                <Dropdown
                  dropdownRender={() => filtersDropdownContent}
                  trigger={['click']}
                  open={filtersVisible}
                  onOpenChange={setFiltersVisible}
                >
                  <Button icon={<FilterOutlined />}>
                    Filters <DownOutlined />
                  </Button>
                </Dropdown>

                <Button icon={<ClearOutlined />} onClick={handleClear}>
                  Clear All
                </Button>

                <Button type="primary" icon={<PlusOutlined />} onClick={onCreateClick}>
                  Create Entry
                </Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>

      {/* Filter Tags */}
      {renderFilterTags().length > 0 && (
        <div className="mt-3">
          <Space wrap>{renderFilterTags()}</Space>
        </div>
      )}
    </div>
  )
}
