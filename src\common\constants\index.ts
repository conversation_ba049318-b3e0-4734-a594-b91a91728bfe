export enum Channels {
    PING = "ping",
    USER = "user",

    // Party channels
    CREATE_PARTY = "create_party",
    GET_PARTY = "get_party",
    UPDATE_PARTY = "update_party",
    DELETE_PARTY = "delete_party",
    GET_PARTIES = "get_parties",
    GET_VENDORS_SELECT = "get_vendors_select",
    GET_CUSTOMERS_SELECT = "get_customers_select",
    GET_CREDITORS_SELECT = "get_creditors_select",
    GET_ALL_PARTIES_SELECT = "get_all_parties_select",
    GET_DEBTORS = "get_debtors",
    GET_CREDITORS = "get_creditors",
    GET_SETTLED = "get_settled",

    // Property channels
    CREATE_PROPERTY = "create_property",
    UPDATE_PROPERTY = "update_property",
    DELETE_PROPERTY = "delete_property",
    GET_PROPERTY = "get_property",
    GET_PROPERTIES = "get_properties",
    IS_PROPERTY_AVAILABLE = "is_property_available",
    GET_PROPERTY_HISTORY = "get_property_history",
    GET_VACANT_PROPERTIES = "get_vacant_properties",

    // Rented Property channels
    CREATE_RENTED_PROPERTY = "create_rented_property",
    UPDATE_RENTED_PROPERTY = "update_rented_property",
    TERMINATE_RENTAL = "terminate_rental",
    GET_RENTED_PROPERTY = "get_rented_property",
    GET_RENTED_PROPERTIES = "get_rented_properties",
    EXTEND_RENTAL = "extend_rental",
    ADJUST_RENT = "adjust_rent",
    END_RENTAL = "end_rental",
    RECORD_RENT_PAYMENT = "record_rent_payment",
    ADJUST_RENT_PAYMENT = "adjust_rent_payment",

    // Container channels
    CREATE_CONTAINER = "create_container",
    DELETE_CONTAINER = "delete_container",
    GET_CONTAINERS = "get_containers",
    GET_CONTAINER = "get_container",
    GET_CONTAINER_STOCK = "get_container_stock",
    GET_CONTAINER_SUMMARY = "get_container_summary",
    CHECK_CAR_WITH_SAME_CHASSIS_NUMBER_EXISTS = "check_car_with_same_chassis_number_exists",
    GET_CONTAINERS_FOR_PDF = "get_containers_for_pdf",

    // User channels
    CREATE_USER = "create_user",
    UPDATE_PASSWORD = "update_password",
    GET_CURRENT_USER = "get_current_user",
    RESET_PASSWORD = "reset_password",
    GET_USERS = "get_users",
    DEACTIVATE_USER = "deactivate_user",
    LOGIN = "login",

    // Partner channels
    CREATE_PARTNER = "create_partner",
    DELETE_PARTNER = "delete_partner",
    GET_PARTNERS = "get_partners",
    GET_PARTNER = "get_partner",
    GET_PARTNERS_SELECT = "get_partners_select",  // For dropdown lists

    // Stock channels
    GET_CARS = "get_cars",
    GET_CAR_PARTS = "get_car_parts",
    GET_ELECTRONICS = "get_electronics",
    GET_SCRAPS = "get_scraps",
    GET_STOCK_SUMMARY = "get_stock_summary",

    // Account channels
    CREATE_ACCOUNT = "create_account",
    DELETE_ACCOUNT = "delete_account",
    UPDATE_ACCOUNT = "update_account",
    GET_ACCOUNTS = "get_accounts",
    GET_ACCOUNTS_SELECT = "get_accounts_select",
    GET_CUSTOMER_ACCOUNTS_SELECT = "get_customer_accounts_select",
    GET_TENANT_ACCOUNTS_SELECT = "get_tenant_accounts_select",
    GET_ACCOUNT_STATEMENT = "get_account_statement",
    GET_BALANCE_SUMMARY = "get_balance_summary",
    GET_ACCOUNT_STATISTICS = "get_account_statistics",
    GET_ACCOUNT_BY_ID = "get_account_by_id",

    // Exchange channels
    CREATE_EXCHANGE = "create_exchange",
    GET_EXCHANGE_BY_ID = "get_exchange_by_id",
    GET_EXCHANGE_HISTORY = "get_exchange_history",
    GET_EXCHANGE_STATISTICS = "get_exchange_statistics",
    GET_DAILY_EXCHANGE_SUMMARY = "get_daily_exchange_summary",
    GET_MONTHLY_EXCHANGE_REPORT = "get_monthly_exchange_report",
    GET_MOST_EXCHANGED_CURRENCIES = "get_most_exchanged_currencies",
    EXCHANGE_BETWEEN_LOCATIONS = "exchange_between_locations",
    GET_LOCATION_EXCHANGE_HISTORY = "get_location_exchange_history",
    CREATE_STRUCTURED_EXCHANGE = 'create_structured_exchange',
    GET_LOCATION_BALANCE = 'get_location_balance',

    // Small counter channels
    INITIALIZE_SMALL_COUNTER = 'initialize_small_counter',
    GET_SMALL_COUNTER_BALANCES = 'get_small_counter_balances',
    GET_SMALL_COUNTER_BALANCE_BY_CURRENCY = 'get_small_counter_balance_by_currency',
    ADJUST_SMALL_COUNTER_BALANCE = 'adjust_small_counter_balance',
    GET_SMALL_COUNTER_BALANCE_HISTORY = 'get_small_counter_balance_history',
    TRANSFER_TO_VAULT = 'transfer_to_vault',
    RECONCILE_SMALL_COUNTER_BALANCE = 'reconcile_small_counter_balance',
    GET_SMALL_COUNTER_DAILY_TRANSACTIONS = 'get_small_counter_daily_transactions',
    GET_SMALL_COUNTER_BALANCE_AT_DATE = 'get_small_counter_balance_at_date',
    GET_ALL_SMALL_COUNTER_TRANSACTIONS = 'get_all_small_counter_transactions',

    // Cash vault channels
    INITIALIZE_CASH_VAULT = 'initialize_cash_vault',
    GET_CASH_VAULT_BALANCES = 'get_cash_vault_balances',
    GET_CASH_VAULT_BALANCE_BY_CURRENCY = 'get_cash_vault_balance_by_currency',
    ADJUST_CASH_VAULT_BALANCE = 'adjust_cash_vault_balance',
    GET_ALL_CASH_VAULT_TRANSACTIONS = 'get_all_cash_vault_transactions',
    RECONCILE_CASH_VAULT_BALANCE = 'reconcile_cash_vault_balance',
    GENERATE_CASH_VAULT_STATEMENT = 'generate_cash_vault_statement',


    // Currency channels
    GET_ALL_CURRENCIES = 'get_all_currencies',
    GET_CURRENCY_TRANSACTIONS = 'get_currency_transactions',
    GET_CURRENCY_SUMMARY = 'get_currency_summary',
    GET_CURRENCY_BALANCES = 'get_currency_balances',

    // Bank account channels
    CREATE_BANK_ACCOUNT = 'create_bank_account',
    GET_ALL_BANK_ACCOUNTS = 'get_all_bank_accounts',
    GET_BANK_ACCOUNT_BY_ID = 'get_bank_account_by_id',
    ADJUST_BANK_ACCOUNT_BALANCE = 'adjust_bank_account_balance',
    DEACTIVATE_BANK_ACCOUNT = 'deactivate_bank_account',
    GET_BANK_ACCOUNT_TRANSACTIONS = 'get_bank_account_transactions',
    DELETE_BANK_ACCOUNT = 'delete_bank_account',
    GENERATE_BANK_STATEMENT = 'generate_bank_statement',
    RECONCILE_BANK_ACCOUNT = 'reconcile_bank_account',
    GET_BANK_ACCOUNTS_FOR_SELECT = 'get_bank_accounts_for_select',

    // Sale channels
    CREATE_WALK_IN_SALE = 'create_walk_in_sale',
    CREATE_ACCOUNT_SALE = 'create_account_sale',
    DELETE_SALE = 'delete_sale',
    GET_SALES = 'get_sales',
    GET_AVAILABLE_ITEMS = 'get_available_items',
    GET_SALE_BY_ID = 'get_sale_by_id',
    GET_CONTAINER_BY_STOCK_ID = 'get_container_by_stock_id',

    // Expense channels
    CREATE_EXPENSE = 'create_expense',
    DELETE_EXPENSE = 'delete_expense',
    GET_EXPENSES = 'get_expenses',

    // Ledger channels
    GET_ACCOUNT_STATEMENT_LEDGER = 'get_account_statement_ledger',
    RECONCILE_BALANCE = 'reconcile_balance',
    GET_DAILY_LEDGER = 'get_daily_ledger',
    GET_TRANSACTIONS_BY_CATEGORY = 'get_transactions_by_category',
    GET_TRANSACTIONS_BY_LOCATION = 'get_transactions_by_location',
    GET_BALANCE_SHEET = 'get_balance_sheet',
    GET_PROFIT_LOSS_STATEMENT = 'get_profit_loss_statement',
    GET_PAGINATED_LEDGER = 'get_paginated_ledger',
    GET_ACCOUNT_LEDGER_ENTRIES_FOR_PDF = 'get_account_ledger_entries_for_pdf',

    // License channels
    GET_LICENSE = 'get_license',
    VERIFY_LICENSE = 'verify_license',

    // Payment channels
    CREATE_PAYMENT = 'create_payment',
    DELETE_PAYMENT = 'delete_payment',
    GET_PAYMENTS = 'get_payments',
    GET_PAYMENT_SUMMARY_BY_ACCOUNT = 'get_payment_summary_by_account',
    GET_DAILY_PAYMENT_REPORT = 'get_daily_payment_report',
    GET_OUTSTANDING_PAYMENTS = 'get_outstanding_payments',
    GET_PAYMENT_STATISTICS = 'get_payment_statistics',
    TRANSFER_BETWEEN_LOCATIONS = 'transfer_between_locations',
    TRANSFER_BETWEEN_ACCOUNTS = 'transfer-between-accounts',
    GET_TRANSFERS = 'get-transfers',
    DELETE_TRANSFER = 'delete-transfer',

    // Reset channels
    CLEAR_ACCOUNTS = 'clear_accounts',
    CLEAR_ACCOUNT_BALANCES = 'clear_account_balances',
    CLEAR_CONTAINERS = 'clear_containers',
    CLEAR_CARS = 'clear_cars',
    CLEAR_CAR_PARTS = 'clear_car_parts',
    CLEAR_ELECTRONICS = 'clear_electronics',
    CLEAR_SCRAPS = 'clear_scraps',
    CLEAR_PARTNERS = 'clear_partners',
    CLEAR_SALES = 'clear_sales',
    CLEAR_PAYMENTS = 'clear_payments',
    CLEAR_SMALL_COUNTER = 'clear_small_counter',
    CLEAR_CASH_VAULT = 'clear_cash_vault',
    CLEAR_BANK_ACCOUNTS = 'clear_bank_accounts',
    CLEAR_CURRENCY_EXCHANGES = 'clear_currency_exchanges',
    CLEAR_EXPENSES = 'clear_expenses',
    CLEAR_PROPERTIES = 'clear_properties',
    CLEAR_RENTED_PROPERTIES = 'clear_rented_properties',
    CLEAR_RENT_PAYMENTS = 'clear_rent_payments',
    CLEAR_LEDGER_ENTRIES = 'clear_ledger_entries',
    CLEAR_ALL_DATA = 'clear_all_data',

    // Backup channels
    CREATE_BACKUP = 'backup/create',
    RESTORE_BACKUP = 'backup/restore',

    // Dashboard channels
    GET_DASHBOARD_CASH_OVERVIEW = 'get_dashboard_cash_overview',
    GET_DASHBOARD_BANK_SUMMARY = 'get_dashboard_bank_summary',
    GET_DASHBOARD_TRANSACTION_STATS = 'get_dashboard_transaction_stats',
    GET_DASHBOARD_ACCOUNT_STATS = 'get_dashboard_account_stats',
    GET_DASHBOARD_INVENTORY_SUMMARY = 'get_dashboard_inventory_summary',
    GET_DASHBOARD_CURRENCY_TRANSACTIONS = 'get_dashboard_currency_transactions',
    GET_DASHBOARD_ACCOUNT_BALANCES = 'get_dashboard_account_balances',
    GET_DASHBOARD_LOCATION_TRANSFERS = 'get_dashboard_location_transfers',
    GET_DASHBOARD_SALES_TRENDS = 'get_dashboard_sales_trends',

    // Manual Entry Channels
    CREATE_MANUAL_ENTRY = 'manual-entry/create',
    GET_MANUAL_ENTRIES = 'manual-entry/get-all',
    GET_MANUAL_ENTRY_BY_ID = 'manual-entry/get-by-id',
    VOID_MANUAL_ENTRY = 'manual-entry/void',
}


export * from './roles'
export * from './routes'
export * from './localStorage'
export * from './menu'
export * from './rules'
