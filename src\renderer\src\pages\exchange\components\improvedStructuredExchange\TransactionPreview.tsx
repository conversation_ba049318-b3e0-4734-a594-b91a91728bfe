import { Card, Row, Col, Typography } from 'antd'
import { InfoCircleOutlined, ArrowRightOutlined } from '@ant-design/icons'
import { formatCurrency } from '@/renderer/utils'

const { Text } = Typography

interface TransactionPreviewProps {
  previewData: any
  exchangeRate: number
}

export const TransactionPreview = ({ previewData, exchangeRate }: TransactionPreviewProps) => {
  if (!previewData) return null

  return (
    <Card 
      className="mt-4" 
      size="small" 
      title={
        <div className="flex items-center">
          <InfoCircleOutlined className="mr-2" />
          <span>Transaction Preview</span>
        </div>
      }
    >
      <Row gutter={16}>
        <Col span={12}>
          <div className="mb-2">
            <Text strong>Exchange:</Text>
            <div>
              {formatCurrency(previewData.inputAmount, previewData.inputCurrency)} →{' '}
              {formatCurrency(previewData.outputAmount, previewData.outputCurrency)}
            </div>
          </div>
          <div>
            <Text strong>Rate:</Text> 1 {previewData.inputCurrency} = {exchangeRate} {previewData.outputCurrency}
          </div>
        </Col>
        <Col span={12}>
          <div>
            <Text strong>Account Balance Changes:</Text>
            {Object.keys(previewData.accountBalances.current).map((currency) => (
              <div key={currency} className="flex justify-between">
                <span>{currency}:</span>
                <div>
                  <span>{formatCurrency(previewData.accountBalances.current[currency], currency)}</span>
                  <ArrowRightOutlined className="mx-2" />
                  <span className={
                    previewData.accountBalances.new[currency] >= previewData.accountBalances.current[currency] 
                      ? 'text-green-600' 
                      : 'text-red-600'
                  }>
                    {formatCurrency(previewData.accountBalances.new[currency], currency)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </Col>
      </Row>
    </Card>
  )
}
