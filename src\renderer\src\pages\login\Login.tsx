import { useState } from 'react'
import { userActions } from '@/renderer/redux'
import { Button, Form, Input, message } from 'antd'
import { useDispatch } from 'react-redux'
import { userApi } from '@/renderer/services'
import './login.scss'
import { App_Routes, RequiredRule } from '@/common/constants'
import type { LoginData } from '@/common/types'
import { useNavigate } from 'react-router-dom'

const Login = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const [loading, setLoading] = useState(false)

  const handleLogin = async (values: LoginData) => {
    setLoading(true)
    const response = await userApi.login(values)
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    const userData = response.data.data
    dispatch(userActions.init(userData))

    // Navigate to dashboard after successful verification
    navigate(App_Routes.DASHBOARD)
  }

  return (
    <div className="container">
      <h3>Login</h3>
      <Form layout="vertical" className="form" onFinish={handleLogin}>
        <Form.Item name="username" label="Username" rules={RequiredRule}>
          <Input placeholder="Enter your username" size="large" />
        </Form.Item>
        <Form.Item name="password" label="Password" rules={RequiredRule}>
          <Input.Password placeholder="Enter your password" size="large" />
        </Form.Item>
        <div className="form-actions">
          <Button
            type="primary"
            htmlType="submit"
            className="w-full"
            size="large"
            loading={loading}
          >
            Login
          </Button>
        </div>
      </Form>
    </div>
  )
}

export default Login
