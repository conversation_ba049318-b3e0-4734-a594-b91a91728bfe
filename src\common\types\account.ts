import { Account, AccountBalance, LedgerEntry } from "@prisma/client";

import { Currency } from "@prisma/client";

export interface CreateAccountData {
    name: string;
    phoneNumber: string;
    address: string;
    type: 'CUSTOMER' | 'TENANT' | 'BOTH';
    userId: string;
    openingBalances?: {
        PKR?: number;
        USD?: number;
        AED?: number;
        AFN?: number;
    };
}

export interface UpdateAccountData {
    phoneNumber?: string;
    address?: string;
}

export interface GetAccountsParams {
    page: number;
    limit: number;
    type?: 'CUSTOMER' | 'TENANT' | 'BOTH';
    search?: string;
    includeDeleted?: boolean;
}

export interface GetAccountsResponse {
    accounts: Account[];
    total: number;
    page: number;
    totalPages: number;
}

export interface AccountSelectOption {
    value: string;
    label: string;
    type?: string;
}

export interface AccountStatementParams {
    accountId: string;
    startDate: Date;
    endDate: Date;
}

export interface AccountStatementResponse {
    account: Account & {
        balances: (AccountBalance & {
            currency: Currency;
        })[];
    };
    entries: LedgerEntry[];
    startDate: Date;
    endDate: Date;
}

export interface BalanceSummaryResponse {
    balances: (AccountBalance & {
        currency: Currency;
    })[];
    totalTransactions: number;
}

export interface AccountStatisticsResponse {
    totalSales: number;
    totalProperties: number;
    transactions: Record<string, {
        count: number;
        total: number;
    }>;
}

export interface GetAccountBalanceSummaryResponse {
    account: Account & {
        balances: (AccountBalance & {
            currency: Currency;
        })[];
    };
    totalTransactions: number;
}

export interface GetAccountStatementResponse {
    account: Account & {
        balances: (AccountBalance & {
            currency: Currency;
        })[];
    };
    entries: LedgerEntry[];
}

export interface GetAccountStatisticsResponse {
    totalSales: number;
    totalProperties: number;
    transactions: Record<string, {
        count: number;
        total: number;
    }>;
}
