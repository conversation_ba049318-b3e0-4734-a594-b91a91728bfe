import { IRequest } from '@/common/types';
import { dashboardService } from '../services'

class DashboardController {
    async getCashOverview(_event: Electron.IpcMainInvokeEvent, _req: IRequest) {
        return await dashboardService.getCashOverview()
    }

    async getBankAccountsSummary(_event: Electron.IpcMainInvokeEvent, _req: IRequest) {
        return await dashboardService.getBankAccountsSummary()
    }

    async getTransactionStats(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { timeRange } = req.query ?? {}
        if (!timeRange) {
            throw new Error('Time range is required')
        }
        return await dashboardService.getTransactionStats(timeRange)
    }

    async getAccountStats(_event: Electron.IpcMainInvokeEvent, _req: IRequest) {
        return await dashboardService.getAccountStats()
    }

    async getInventorySummary(_event: Electron.IpcMainInvokeEvent, _req: IRequest) {
        return await dashboardService.getInventorySummary()
    }

    async getCurrencyTransactions(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { timeRange } = req.query ?? {}
        if (!timeRange) {
            throw new Error('Time range is required')
        }
        return await dashboardService.getCurrencyTransactions(timeRange)
    }

    async getAccountBalances(_event: Electron.IpcMainInvokeEvent, _req: IRequest) {
        return await dashboardService.getAccountBalances()
    }

    async getLocationTransfers(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { timeRange } = req.query ?? {}
        if (!timeRange) {
            throw new Error('Time range is required')
        }
        return await dashboardService.getLocationTransfers(timeRange)
    }

    async getSalesTrends(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { timeRange } = req.query ?? {}
        if (!timeRange) {
            throw new Error('Time range is required')
        }
        return await dashboardService.getSalesTrends(timeRange)
    }
}

export const dashboardController = new DashboardController()