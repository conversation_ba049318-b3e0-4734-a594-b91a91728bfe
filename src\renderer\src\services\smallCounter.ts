import { http } from './http';
import { Channels } from '@/common/constants';
import { InitializeSmallCounterData } from '@/common/types';

export const initializeSmallCounter = async (data: InitializeSmallCounterData) => {
    return await http.post(Channels.INITIALIZE_SMALL_COUNTER, { body: data });
};

export const getBalances = async () => {
    return await http.get(Channels.GET_SMALL_COUNTER_BALANCES);
};

export const getBalanceByCurrency = async (currency: string) => {
    return await http.get(Channels.GET_SMALL_COUNTER_BALANCE_BY_CURRENCY, {
        params: { currency }
    });
};

export const adjustBalance = async (currency: string, adjustment: number, reason: string, userId: string) => {
    return await http.post(Channels.ADJUST_SMALL_COUNTER_BALANCE, {
        body: { currency, adjustment, reason, userId }
    });
};

export const getBalanceHistory = async (currency: string, startDate: Date, endDate: Date) => {
    return await http.get(Channels.GET_SMALL_COUNTER_BALANCE_HISTORY, {
        params: { currency, startDate: startDate.toISOString(), endDate: endDate.toISOString() }
    });
};

export const transferToVault = async (currency: string, amount: number, userId: string) => {
    return await http.post(Channels.TRANSFER_TO_VAULT, {
        body: { currency, amount, userId }
    });
};

export const reconcileBalance = async (currency: string) => {
    return await http.get(Channels.RECONCILE_SMALL_COUNTER_BALANCE, {
        params: { currency }
    });
};

export const getDailyTransactions = async (date: Date) => {
    return await http.get(Channels.GET_SMALL_COUNTER_DAILY_TRANSACTIONS, {
        params: { date: date.toISOString() }
    });
};

export const getBalanceAtDate = async (currency: string, date: Date) => {
    return await http.get(Channels.GET_SMALL_COUNTER_BALANCE_AT_DATE, {
        params: { currency, date: date.toISOString() }
    });
};

export const getAllTransactions = async (page: number = 1, pageSize: number = 20, startDate?: Date, endDate?: Date) => {
    return await http.get(Channels.GET_ALL_SMALL_COUNTER_TRANSACTIONS, {
        query: { page, pageSize, startDate, endDate }
    });
};