import { Form, Input, InputNumber } from 'antd'
import type { FormInstance } from 'antd'

interface ElectronicFormProps {
  form: FormInstance
}

export const ElectronicForm = ({ form }: ElectronicFormProps) => {
  return (
    <>
      <Form.Item
        name="name"
        label="Electronic Item Name"
        rules={[{ required: true, message: 'Please enter item name' }]}
      >
        <Input />
      </Form.Item>

      <Form.Item
        name="quantity"
        label="Quantity"
        rules={[{ required: true, message: 'Please enter quantity' }]}
        initialValue={1}
      >
        <InputNumber min={1} className="w-full" />
      </Form.Item>
    </>
  )
}
