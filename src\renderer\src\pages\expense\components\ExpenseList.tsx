import { useEffect, useState } from 'react'
import { App, But<PERSON>, Popconfirm, Table, Tag, Input, Form, Tooltip } from 'antd'
import { DeleteOutlined } from '@ant-design/icons'
import type { GetExpensesParams, GetExpensesResponse } from '@/common/types'
import { expenseApi } from '@/renderer/services'
import { useApi } from '@/renderer/hooks'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { title } from 'process'

dayjs.extend(utc)
dayjs.extend(timezone)

interface ExpenseListProps {
  filters: GetExpensesParams
  onFiltersChange: (filters: GetExpensesParams) => void
}

export const ExpenseList = ({ filters, onFiltersChange }: ExpenseListProps) => {
  const [loading, setLoading] = useState(false)
  const [deleteReason, setDeleteReason] = useState('')
  const [form] = Form.useForm()

  const user = useSelector((state: IRootState) => state.user.data)

  const { message } = App.useApp()

  const {
    data,
    error,
    request: fetchExpenses
  } = useApi<GetExpensesResponse, [GetExpensesParams]>(expenseApi.getExpenses)

  useEffect(() => {
    fetchExpenses(filters)
  }, [filters])

  const handleDelete = async (id: string) => {
    if (!deleteReason.trim()) {
      message.error('Please enter a reason for deletion')
      return
    }

    setLoading(true)
    const response = await expenseApi.deleteExpense({
      id,
      userId: user?.id || '',
      reason: deleteReason.trim()
    })
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Expense deleted successfully')
    setDeleteReason('')
    fetchExpenses(filters)
  }

  const handleTableChange = (pagination: any) => {
    onFiltersChange({
      ...filters,
      page: pagination.current,
      pageSize: pagination.pageSize
    })
  }

  const columns = [
    {
      title: 'Sr. No',
      key: 'serialNumber',
      width: 70,
      render: (_: any, __: any, index: number) => {
        // Calculate serial number based on current page and page size
        return ((filters.page ?? 1) - 1) * (filters.pageSize ?? 10) + index + 1
      }
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number, record: any) => (
        <span className={`font-medium ${record.isDeleted ? 'text-gray-400 line-through' : ''}`}>
          PKR {amount.toLocaleString()}
        </span>
      )
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      render: (category: string, record: any) => (
        <Tag color={record.isDeleted ? 'default' : 'blue'}>{category}</Tag>
      )
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      // width: '30%',
      render: (description: string, record: any) => (
        <span className={record.isDeleted ? 'text-gray-400 line-through' : ''}>
          {description}
          {record.isDeleted && (
            <Tooltip
              title={
                <div>
                  <p>Deleted on: {dayjs(record.deletedAt).format('DD/MM/YYYY HH:mm')}</p>
                  <p>Reason: {record.deleteReason}</p>
                </div>
              }
            >
              <Tag color="red" className="ml-2">
                Deleted
              </Tag>
            </Tooltip>
          )}
        </span>
      )
    },
    {
      title: 'Payment Source',
      dataIndex: 'ledgerEntry',
      key: 'paymentSource',
      render: (ledgerEntry: any) => {
        const sourceType = ledgerEntry?.sourceType
        let color = 'default'
        switch (sourceType) {
          case 'SMALL_COUNTER':
            color = 'green'
            break
          case 'CASH_VAULT':
            color = 'gold'
            break
          case 'BANK_ACCOUNT':
            color = 'purple'
            break
        }
        return <Tag color={color}>{sourceType?.replace('_', ' ')}</Tag>
      }
    },
    {
      title: 'Created By',
      dataIndex: 'ledgerEntry',
      key: 'createdBy',
      render: (ledgerEntry: any) => ledgerEntry?.createdBy?.name
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => dayjs(date).format('DD/MM/YYYY')
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 100,
      render: (_: any, record: any) => (
        <Popconfirm
          title="Delete Expense"
          description={
            <div className="flex flex-col gap-2">
              <p>Are you sure you want to delete this expense?</p>
              <Form form={form}>
                <Form.Item
                  style={{ marginBottom: 0 }}
                  name="reason"
                  rules={[{ required: true, message: 'Please enter a reason' }]}
                >
                  <Input
                    placeholder="Enter reason for deletion"
                    value={deleteReason}
                    onChange={(e) => setDeleteReason(e.target.value)}
                  />
                </Form.Item>
              </Form>
            </div>
          }
          onConfirm={() => handleDelete(record.id)}
          onCancel={() => {
            setDeleteReason('')
            form.resetFields()
          }}
          okText="Yes"
          cancelText="No"
        >
          <Button danger type="link" icon={<DeleteOutlined />}>
            Delete
          </Button>
        </Popconfirm>
      )
    }
  ]

  return (
    <Table
      columns={columns}
      dataSource={data?.expenses || []}
      rowKey="id"
      loading={loading || !data}
      onChange={handleTableChange}
      virtual
      sticky
      size="small"
      pagination={{
        position: ['topRight'],
        total: data?.pagination.total || 0,
        current: filters.page,
        pageSize: filters.pageSize,
        showSizeChanger: true,
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
        pageSizeOptions: ['10', '20', '50', '100']
      }}
      // scroll={{ x: 'max-content' }}
    />
  )
}
