import { useState } from 'react'
import { Form, Input, Select, DatePicker, Button, message, InputNumber, Tooltip } from 'antd'
import { paymentApi } from '@/renderer/services'
import { useApi } from '@/renderer/hooks'
import { GetAccountsParams } from '@/common/types'
import { useAccountContext } from '@/renderer/contexts'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { handleDatePickerValue } from '@/renderer/utils'
import { AccountDetailsCard } from '@/renderer/components'

interface AccountsTransferProps {
  onSuccess: () => void
}

export const AccountsTransfer = ({ onSuccess }: AccountsTransferProps) => {
  const [form] = Form.useForm()
  const [isLoading, setIsLoading] = useState(false)
  const [sourceAccountId, setSourceAccountId] = useState<string | null>(null)
  const [destinationAccountId, setDestinationAccountId] = useState<string | null>(null)

  const user = useSelector((state: IRootState) => state.user.data)

  const { accounts, tenants } = useAccountContext()

  // Account fetching is now handled by the AccountDetailsCard component

  const handleSubmit = async (values: any) => {
    if (values.sourceAccountId === values.destinationAccountId) {
      message.error('Source and destination accounts cannot be the same')
      return
    }

    setIsLoading(true)
    const response = await paymentApi.transferBetweenAccounts({
      ...values,
      date: handleDatePickerValue(values.date?.toDate()),
      userId: user?.id
    })
    setIsLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Transfer created successfully')
    form.resetFields()
    setSourceAccountId(null)
    setDestinationAccountId(null)
    onSuccess()
  }

  // Handle account selection changes
  const handleSourceAccountChange = (value: string) => {
    setSourceAccountId(value)
  }

  const handleDestinationAccountChange = (value: string) => {
    setDestinationAccountId(value)
  }

  return (
    <div className="mt-4 flex w-full flex-col gap-4 md:flex-row">
      {/* Left side - Form */}
      <div className="flex-1">
        <Form form={form} layout="vertical" onFinish={handleSubmit} className="space-y-4">
          <Form.Item
            name="sourceAccountId"
            label="From Account"
            rules={[{ required: true, message: 'Please select source account' }]}
          >
            <Select
              showSearch
              placeholder="Select source account"
              optionFilterProp="children"
              options={[...accounts, ...tenants]}
              onChange={handleSourceAccountChange}
            />
          </Form.Item>

          <Form.Item
            name="destinationAccountId"
            label="To Account"
            rules={[{ required: true, message: 'Please select destination account' }]}
          >
            <Select
              showSearch
              placeholder="Select destination account"
              optionFilterProp="children"
              options={[...accounts, ...tenants]}
              onChange={handleDestinationAccountChange}
            />
          </Form.Item>

          <Form.Item
            name="amount"
            label="Amount"
            rules={[
              { required: true, message: 'Please enter amount' },
              { type: 'number', min: 1, message: 'Amount must be greater than 0' }
            ]}
          >
            <InputNumber
              className="w-full"
              min={Number(1)}
              precision={2}
              placeholder="Enter amount"
              formatter={(value) => `Rs ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={(value) => Number(value!.replace(/Rs\s?|(,*)/g, ''))}
            />
          </Form.Item>

          <Form.Item
            name="currencyCode"
            label="Currency"
            rules={[{ required: true, message: 'Please select currency' }]}
          >
            <Select placeholder="Select currency">
              <Select.Option value="USD">USD</Select.Option>
              <Select.Option value="PKR">PKR</Select.Option>
              <Select.Option value="AED">AED</Select.Option>
              <Select.Option value="AFN">AFN</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item name="description" label="Description">
            <Input.TextArea rows={4} />
          </Form.Item>

          <Form.Item name="date" label="Date" rules={[{ required: true }]}>
            <DatePicker className="w-full" />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={isLoading} block>
              Create Transfer
            </Button>
          </Form.Item>
        </Form>
      </div>

      {/* Right side - Account Details Cards */}
      <div className="flex flex-1 flex-col gap-4">
        <Tooltip title="Source Account Details">
          <AccountDetailsCard accountId={sourceAccountId || undefined} />
        </Tooltip>
        <Tooltip title="Destination Account Details">
          <AccountDetailsCard accountId={destinationAccountId || undefined} />
        </Tooltip>
      </div>
    </div>
  )
}
