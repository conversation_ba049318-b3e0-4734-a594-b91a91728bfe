import { useEffect, useState } from 'react'
import { App, Card, Col, Row, Statistic, Table, Tag } from 'antd'
import { ArrowDownOutlined, ArrowUpOutlined } from '@ant-design/icons'
import { exchangeApi } from '@/renderer/services'
import type { ExchangeStatistics as ExchangeStatsType } from '@/common/types'

export const ExchangeStatistics = () => {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<ExchangeStatsType>({
    totalVolume: [],
    totalTransactions: 0
  })

  const { message } = App.useApp()

  const fetchStats = async () => {
    setLoading(true)
    const response = await exchangeApi.getExchangeStatistics()
    setLoading(false)
    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.message)
      return
    }
    setData(response.data.data)
  }

  useEffect(() => {
    fetchStats()
  }, [])

  const columns = [
    {
      title: 'Currency',
      dataIndex: 'currency',
      key: 'currency',
      render: (currency: string) => <Tag color="blue">{currency}</Tag>
    },
    {
      title: 'Inflow',
      dataIndex: 'inflow',
      key: 'inflow',
      render: (inflow: number, record: any) => (
        <span className="flex items-center gap-2 font-medium text-green-600">
          <ArrowUpOutlined />
          {inflow.toLocaleString()} {record.currency}
        </span>
      )
    },
    {
      title: 'Outflow',
      dataIndex: 'outflow',
      key: 'outflow',
      render: (outflow: number, record: any) => (
        <span className="flex items-center gap-2 font-medium text-red-600">
          <ArrowDownOutlined />
          {outflow.toLocaleString()} {record.currency}
        </span>
      )
    },
    {
      title: 'Net Flow',
      key: 'netFlow',
      render: (record: any) => {
        const netFlow = record.inflow - record.outflow
        const isPositive = netFlow > 0
        return (
          <span
            className={`flex items-center gap-2 font-medium ${
              isPositive ? 'text-green-600' : 'text-red-600'
            }`}
          >
            {isPositive ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
            {Math.abs(netFlow).toLocaleString()} {record.currency}
          </span>
        )
      }
    }
  ]

  return (
    <div className="flex flex-col gap-4">
      {/* Summary Cards */}
      <Row gutter={16}>
        <Col span={8}>
          <Card>
            <Statistic
              title="Total Transactions"
              value={data.totalTransactions}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic title="Total Currencies" value={data.totalVolume.length} loading={loading} />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="Active Today"
              value={data.totalVolume.filter((v) => v.inflow > 0 || v.outflow > 0).length}
              loading={loading}
              suffix={`/ ${data.totalVolume.length}`}
            />
          </Card>
        </Col>
      </Row>

      {/* Volume Table */}
      <Card title="Currency Volume">
        <Table
          columns={columns}
          dataSource={data.totalVolume}
          rowKey="currency"
          loading={loading}
          pagination={false}
        />
      </Card>
    </div>
  )
}
