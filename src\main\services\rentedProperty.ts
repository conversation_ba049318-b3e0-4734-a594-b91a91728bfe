import { prisma } from '../db'
import type { CreateRentedPropertyData, GetRentedPropertiesParams, RecordRentPaymentData, UpdateRentedPropertyData, AdjustRentPaymentData } from '@/common/types'
import { TransactionCategory, TransactionLocation, TransactionType } from '@prisma/client'

class RentedPropertyService {
    async createRentedProperty(data: CreateRentedPropertyData, userId: string) {
        return prisma.$transaction(async (tx) => {
            // Check if property is available
            const existingRental = await tx.rentedProperty.findFirst({
                where: {
                    propertyId: data.propertyId,
                    isDeleted: false,
                    OR: [
                        { endDate: null },
                        { endDate: { gt: new Date() } }
                    ]
                }
            })

            if (existingRental) {
                throw new Error('Property is already rented')
            }

            // Create rented property record
            const rentedProperty = await tx.rentedProperty.create({
                data,
                include: {
                    property: true,
                    tenant: true
                }
            })

            // Update property status - using existing fields
            await tx.property.update({
                where: { id: data.propertyId },
                data: {
                    isRented: true,
                    currentRent: data.monthlyRent,
                    currentTenantId: data.tenantId,
                }
            })

            return rentedProperty
        })
    }

    async updateRentedProperty(id: string, data: UpdateRentedPropertyData) {
        return prisma.rentedProperty.update({
            where: { id },
            data,
            include: {
                property: true,
                tenant: true
            }
        })
    }

    async terminateRental(id: string, reason: string, endDate: Date) {
        return prisma.$transaction(async (tx) => {
            // Check payment status
            const paymentStatus = await this.checkRentalPaymentStatus(tx, id);

            // If there are any debits, we should end the rental instead of terminating
            if (paymentStatus.hasDebits) {
                throw new Error('RENTAL_HAS_DEBITS');
            }

            const rental = await tx.rentedProperty.update({
                where: { id },
                data: {
                    isDeleted: true,
                    deleteReason: reason,
                    deletedAt: new Date(),
                    endDate
                }
            });

            // Reset property status
            await tx.property.update({
                where: { id: rental.propertyId },
                data: {
                    isRented: false,
                    currentRent: null,
                    currentTenantId: null,
                }
            });

            return rental;
        });
    }

    // Update balance on receival
    async recordRentPayment(data: RecordRentPaymentData) {
        return prisma.$transaction(async (tx) => {
            const rental = await tx.rentedProperty.findFirst({
                where: {
                    id: data.rentedPropertyId,
                    isDeleted: false
                },
                include: {
                    property: true,
                    tenant: true
                }
            })

            if (!rental) {
                throw new Error('Rental not found')
            }

            // Get PKR currency
            const pkr = await tx.currency.findFirst({
                where: { code: 'PKR' }
            })

            if (!pkr) {
                throw new Error('PKR currency not found')
            }

            // Update tenant's balance
            await tx.accountBalance.upsert({
                where: {
                    accountId_currencyId: {
                        accountId: rental.tenantId,
                        currencyId: pkr.id
                    }
                },
                create: {
                    accountId: rental.tenantId,
                    currencyId: pkr.id,
                    balance: data.amount
                },
                update: {
                    balance: {
                        increment: data.amount
                    }
                }
            })

            // Update destination balance based on type
            switch (data.destinationType) {
                case 'SMALL_COUNTER':
                    await tx.smallCounter.update({
                        where: { id: (await tx.smallCounter.findFirst())?.id },
                        data: { pkrBalance: { increment: data.amount } }
                    })
                    break
                case 'CASH_VAULT':
                    await tx.cashVault.update({
                        where: { id: (await tx.cashVault.findFirst())?.id },
                        data: { pkrBalance: { increment: data.amount } }
                    })
                    break
                case 'BANK_ACCOUNT':
                    if (!data.bankAccountId) throw new Error('Bank account ID is required')
                    await tx.bankAccount.update({
                        where: { id: data.bankAccountId },
                        data: { balance: { increment: data.amount } }
                    })
                    break
            }

            // Create credit ledger entry for tenant (reducing their debt)
            await tx.ledgerEntry.create({
                data: {
                    date: new Date(),
                    amount: data.amount,
                    type: TransactionType.CREDIT,
                    description: `Rent payment for ${rental.property.name}`,
                    accountId: rental.tenantId,
                    sourceType: TransactionLocation.ACCOUNT,
                    destinationType: data.destinationType as TransactionLocation,
                    transactionType: TransactionCategory.RENT,
                    createdById: data.userId,
                    currencyId: pkr.id,
                    bankAccountId: data.destinationType === 'BANK_ACCOUNT' ? data.bankAccountId : undefined
                }
            })

            return rental
        })
    }

    // Function to adjust wrong rent payment entries
    async adjustRentPayment(data: AdjustRentPaymentData) {
        return prisma.$transaction(async (tx) => {
            const rental = await tx.rentedProperty.findFirst({
                where: {
                    id: data.rentedPropertyId,
                    isDeleted: false
                },
                include: {
                    property: true,
                    tenant: true
                }
            })

            if (!rental) {
                throw new Error('Rental not found')
            }

            const pkr = await tx.currency.findFirst({ where: { code: 'PKR' } })
            if (!pkr) throw new Error('PKR currency not found')

            // Update tenant's balance (reverse of the original entry)
            const balanceAdjustment = data.type === 'CREDIT' ? data.amount : -data.amount
            await tx.accountBalance.update({
                where: {
                    accountId_currencyId: {
                        accountId: rental.tenantId,
                        currencyId: pkr.id
                    }
                },
                data: {
                    balance: {
                        increment: balanceAdjustment
                    }
                }
            })

            // Update destination balance based on type
            switch (data.destinationType) {
                case 'SMALL_COUNTER':
                    await tx.smallCounter.update({
                        where: { id: (await tx.smallCounter.findFirst())?.id },
                        data: { pkrBalance: { increment: balanceAdjustment } }
                    })
                    break
                case 'CASH_VAULT':
                    await tx.cashVault.update({
                        where: { id: (await tx.cashVault.findFirst())?.id },
                        data: { pkrBalance: { increment: balanceAdjustment } }
                    })
                    break
                case 'BANK_ACCOUNT':
                    if (!data.bankAccountId) throw new Error('Bank account ID is required')
                    await tx.bankAccount.update({
                        where: { id: data.bankAccountId },
                        data: { balance: { increment: balanceAdjustment } }
                    })
                    break
            }

            // Create adjustment ledger entry
            await tx.ledgerEntry.create({
                data: {
                    date: new Date(),
                    amount: data.amount,
                    type: data.type === 'CREDIT' ? TransactionType.CREDIT : TransactionType.DEBIT,
                    description: `Adjustment: ${data.reason} for ${rental.property.name}`,
                    accountId: rental.tenantId,
                    sourceType: TransactionLocation.ACCOUNT,
                    destinationType: data.destinationType as TransactionLocation,
                    transactionType: TransactionCategory.RENT,
                    createdById: data.userId,
                    currencyId: pkr.id,
                    bankAccountId: data.destinationType === 'BANK_ACCOUNT' ? data.bankAccountId : undefined
                }
            })

            return rental
        })
    }

    async getRentedProperty(id: string) {
        return prisma.rentedProperty.findFirst({
            where: {
                id,
                isDeleted: false
            },
            include: {
                property: true,
                tenant: true,
            }
        })
    }

    async getRentedProperties({
        page = 1,
        limit = 20,
        tenantId,
        propertyId,
        isActive,
        search
    }: GetRentedPropertiesParams) {
        const where: any = {
            isDeleted: false,
            ...(tenantId && { tenantId }),
            ...(propertyId && { propertyId }),
            ...(isActive && {
                OR: [
                    { endDate: null },
                    { endDate: { gt: new Date() } }
                ]
            })
        }

        // Add search conditions if search string is provided
        if (search) {
            where.OR = [
                {
                    property: {
                        is: {
                            name: {
                                contains: search,
                                mode: 'insensitive'
                            }
                        }
                    }
                },
                {
                    tenant: {
                        is: {
                            name: {
                                contains: search,
                                mode: 'insensitive'
                            }
                        }
                    }
                }
            ]
        }

        const [total, rentedProperties] = await Promise.all([
            prisma.rentedProperty.count({ where }),
            prisma.rentedProperty.findMany({
                where,
                include: {
                    property: true,
                    tenant: true,
                },
                skip: (page - 1) * limit,
                take: limit,
                orderBy: { startDate: 'desc' }
            })
        ])

        return {
            rentedProperties,
            pagination: {
                total,
                page,
                limit
            }
        }
    }

    // Extend an existing rental agreement
    async extendRental(id: string, newEndDate: Date) {
        return prisma.rentedProperty.update({
            where: { id },
            data: { endDate: newEndDate },
            include: {
                property: true,
                tenant: true
            }
        })
    }

    // Adjust monthly rent amount
    async adjustRent(id: string, newAmount: number) {
        return prisma.rentedProperty.update({
            where: { id },
            data: { monthlyRent: newAmount },
            include: {
                property: true,
                tenant: true
            }
        })
    }

    // Helper function to check rental payment status
    private async checkRentalPaymentStatus(tx: any, rentalId: string) {
        // Get all debit entries for rent
        const debitEntries = await tx.ledgerEntry.findMany({
            where: {
                rentedPropertyId: rentalId,
                type: TransactionType.DEBIT,
                transactionType: TransactionCategory.RENT,
                isDeleted: false
            },
            select: {
                amount: true
            }
        });

        // Get all credit entries for rent
        const creditEntries = await tx.ledgerEntry.findMany({
            where: {
                rentedPropertyId: rentalId,
                type: TransactionType.CREDIT,
                transactionType: TransactionCategory.RENT,
                isDeleted: false
            },
            select: {
                amount: true
            }
        });

        const totalDebits = debitEntries.reduce((sum, entry) => sum + entry.amount, 0);
        const totalCredits = creditEntries.reduce((sum, entry) => sum + entry.amount, 0);

        return {
            hasDebits: debitEntries.length > 0,
            hasCredits: creditEntries.length > 0,
            balance: totalDebits - totalCredits
        };
    }

    async endRental(rentalId: string) {
        return await prisma.$transaction(async (tx) => {
            // Check payment status
            const paymentStatus = await this.checkRentalPaymentStatus(tx, rentalId);

            // If there are debits but no payments, this might be a mistake
            if (paymentStatus.hasDebits && !paymentStatus.hasCredits) {
                throw new Error('RENTAL_NO_PAYMENTS');
            }

            // If there is an outstanding balance
            if (paymentStatus.balance > 0) {
                throw new Error(`RENTAL_OUTSTANDING_BALANCE:${paymentStatus.balance}`);
            }

            const rental = await tx.rentedProperty.update({
                where: { id: rentalId },
                data: {
                    endDate: new Date()
                }
            });

            // Reset property status
            await tx.property.update({
                where: { id: rental.propertyId },
                data: {
                    isRented: false,
                    currentRent: null,
                    currentTenantId: null,
                }
            });

            return rental;
        });
    }
}

export const rentedPropertyService = new RentedPropertyService();


