import { A5_CONFIG, PDF_STYLES, savePDF, printPDF } from '@/renderer/utils/pdfUtils'
import { cashVaultApi } from '@/renderer/services'
import jsPDF from 'jspdf'
import 'jspdf-autotable'
import dayjs from 'dayjs'
import type { CashVaultStatement, GetCashVaultStatementParams } from '@/common/types'

export const generateCashVaultStatementPDF = async (
  currencyCode: string,
  startDate: Date,
  endDate: Date
): Promise<jsPDF> => {
  // Fetch all statement entries for PDF (no pagination)
  const response = await cashVaultApi.generateCashVaultStatement(currencyCode, {
    startDate,
    endDate,
    page: 1,
    pageSize: 100000 // Large page size to get all entries
  })

  if (response.error.error || response.data.error) {
    throw new Error(response.error.message || response.data.error.message)
  }

  const statement: CashVaultStatement = response.data.data

  // Create PDF document
  const doc = new jsPDF(A5_CONFIG)
  const pageWidth = doc.internal.pageSize.width

  // Colors will be defined directly where needed

  // Define header content function
  const drawHeader = () => {
    // Add header
    doc.setFontSize(PDF_STYLES.header.fontSize)
    doc.text('Cash Vault Statement', pageWidth / 2, 10, { align: 'center' })

    // Add details
    doc.setFontSize(PDF_STYLES.header.titleFontSize)
    doc.text([
      `Currency: ${statement.currencyCode}`,
      `Period: ${dayjs(statement.startDate).format('DD/MM/YYYY')} - ${dayjs(statement.endDate).format('DD/MM/YYYY')}`,
    ], 10, 20)

    // Add summary with colors
    doc.setFontSize(8) // Set subtitle font size manually to 8pt

    // Format numbers safely
    const formatNumber = (num: number | null | undefined): string => {
      if (num === null || num === undefined || isNaN(num)) return '0';
      return num.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 });
    };

    // Position summary on the right side
    let xPos = pageWidth - 10
    let yPos = 20

    // Opening Balance
    doc.setTextColor(0, 0, 0)
    doc.text('Opening Balance:', xPos, yPos, { align: 'right' })
    yPos += 5
    doc.text(`${formatNumber(statement.openingBalance)} ${statement.currencyCode}`, xPos, yPos, { align: 'right' })

    // Total Credits
    yPos += 8
    doc.text('Total Credits:', xPos, yPos, { align: 'right' })
    yPos += 5
    doc.setTextColor(0, 128, 0) // Green for credits
    doc.text(`${formatNumber(statement.summary.totalCredits)} ${statement.currencyCode}`, xPos, yPos, { align: 'right' })

    // Total Debits
    yPos += 8
    doc.setTextColor(0, 0, 0)
    doc.text('Total Debits:', xPos, yPos, { align: 'right' })
    yPos += 5
    doc.setTextColor(255, 0, 0) // Red for debits
    doc.text(`${formatNumber(statement.summary.totalDebits)} ${statement.currencyCode}`, xPos, yPos, { align: 'right' })

    // Closing Balance
    yPos += 8
    doc.setTextColor(0, 0, 0)
    doc.text('Closing Balance:', xPos, yPos, { align: 'right' })
    yPos += 5
    doc.setTextColor(statement.closingBalance >= 0 ? 0 : 255, statement.closingBalance >= 0 ? 128 : 0, 0)
    doc.text(`${formatNumber(statement.closingBalance)} ${statement.currencyCode}`, xPos, yPos, { align: 'right' })

    // Reset text color to black for the rest of the document
    doc.setTextColor(0, 0, 0)
  }

  // Draw header
  drawHeader()

  // Add table with column widths
  const tableColumn = [
    { header: 'S.No', dataKey: 'serialNumber', width: 10 },
    { header: 'Date', dataKey: 'date', width: 17 },
    { header: 'Description', dataKey: 'description', width: 50 },
    { header: 'Credit', dataKey: 'credit', width: 17 },
    { header: 'Debit', dataKey: 'debit', width: 17 },
    { header: 'Balance', dataKey: 'balance', width: 20 }
  ]

  // Format numbers safely
  const formatNumber = (num: number | null | undefined): string => {
    if (num === null || num === undefined || isNaN(num)) return '0';
    return num.toLocaleString();
  };

  const tableRows = statement.entries.map((entry, index) => ({
    serialNumber: index + 1,
    date: entry.date ? dayjs(entry.date).format('DD/MM/YYYY') : '-',
    description: entry.description || '',
    credit: entry.credit ? formatNumber(entry.credit) : '-',
    debit: entry.debit ? formatNumber(entry.debit) : '-',
    balance: formatNumber(entry.runningBalance),
    // Store raw values for conditional styling
    _creditValue: entry.credit || 0,
    _debitValue: entry.debit || 0,
    _balanceValue: entry.runningBalance
  }))

  // Calculate margins to center the table
  const leftMargin = 10; // Start from the same point as the header

  // We'll use the colors defined at the top of the file

  // @ts-ignore - jspdf-autotable types are not complete
  doc.autoTable({
    startY: 70, // Adjusted to account for the header and summary
    columns: tableColumn,
    body: tableRows,
    headStyles: {
      ...PDF_STYLES.table.headStyles,
      textColor: [0, 0, 0], // Black text for header
      fillColor: [240, 240, 240] // Light gray background for header
    },
    styles: {
      ...PDF_STYLES.table.styles,
      fontSize: 7, // Smaller font size for A5
      fillColor: [255, 255, 255] // Ensure all cells have white background
    },
    margin: { left: leftMargin, right: leftMargin },
    tableWidth: 'auto',
    columnStyles: {
      0: { cellWidth: tableColumn[0].width }, // S.No
      1: { cellWidth: tableColumn[1].width }, // Date
      2: { cellWidth: tableColumn[2].width }, // Description
      3: { cellWidth: tableColumn[3].width, halign: 'right' }, // Credit
      4: { cellWidth: tableColumn[4].width, halign: 'right' }, // Debit
      5: { cellWidth: tableColumn[5].width, halign: 'right' }  // Balance
    },
    // Apply conditional styling to cells
    didParseCell: function (data: any) {
      if (data.section === 'body') {
        const row = data.row.raw as any;

        // Credit column - Green text for values > 0
        if (data.column.dataKey === 'credit' && row._creditValue > 0) {
          data.cell.styles.textColor = [0, 128, 0]; // Green
        }

        // Debit column - Red text for values > 0
        if (data.column.dataKey === 'debit' && row._debitValue > 0) {
          data.cell.styles.textColor = [255, 0, 0]; // Red
        }

        // Balance column - Green for positive, Red for negative
        if (data.column.dataKey === 'balance') {
          data.cell.styles.textColor = row._balanceValue >= 0 ? [0, 128, 0] : [255, 0, 0];
        }
      }
    },
    // No didDrawPage callback to prevent header from appearing on every page
  })

  // Add footer with page numbers
  const pageCount = (doc as any).internal.getNumberOfPages()
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i)
    doc.setFontSize(PDF_STYLES.footer.fontSize)
    doc.setTextColor(0, 0, 0) // Reset to black for footer
    doc.text(
      `Page ${i} of ${pageCount}`,
      pageWidth / 2,
      doc.internal.pageSize.height - PDF_STYLES.footer.margin,
      { align: 'center' }
    )
  }

  return doc
}

export const handleCashVaultStatementPDF = async (
  currencyCode: string,
  startDate: Date,
  endDate: Date,
  action: 'save' | 'print'
): Promise<void> => {
  try {
    const doc = await generateCashVaultStatementPDF(currencyCode, startDate, endDate)
    const fileName = `CashVault_${currencyCode}_Statement_${dayjs(startDate).format('YYYY-MM-DD')}_to_${dayjs(endDate).format('YYYY-MM-DD')}`

    if (action === 'save') {
      await savePDF(doc, fileName)
    } else {
      await printPDF(doc)
    }
  } catch (error) {
    console.error('Failed to generate PDF:', error)
    throw error
  }
}
