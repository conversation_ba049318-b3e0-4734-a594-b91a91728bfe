import { IRequest } from '@/common/types';
import { bankAccountService } from '../services';

class BankAccountController {
    async createBankAccount(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { accountNumber, bankName, branchCode, openingBalance, userId } = req.body ?? {};

        if (!accountNumber || !bankName || !userId) {
            throw new Error('Account number, bank name and user ID are required');
        }

        return await bankAccountService.createBankAccount({
            accountNumber,
            bankName,
            branchCode,
            openingBalance: openingBalance ? Number(openingBalance) : undefined,
            userId
        });
    }


    async deleteBankAccount(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        if (!id) {
            throw new Error('Bank account ID is required');
        }
        return await bankAccountService.deleteBankAccount(id);
    }

    async getAllBankAccounts() {
        return await bankAccountService.getAllBankAccounts();
    }

    async getBankAccountsForSelect() {
        return await bankAccountService.getBankAccountsForSelect();
    }

    async getBankAccountById(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        if (!id) {
            throw new Error('Bank account ID is required');
        }
        return await bankAccountService.getBankAccountById(id);
    }

    async adjustBalance(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        const { adjustment, reason, userId } = req.body ?? {};

        if (!id || !adjustment || !reason || !userId) {
            throw new Error('Missing required fields');
        }

        return await bankAccountService.adjustBalance(
            id,
            Number(adjustment),
            reason,
            userId
        );
    }

    async deactivateAccount(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        if (!id) {
            throw new Error('Bank account ID is required');
        }
        return await bankAccountService.deactivateAccount(id);
    }

    async getTransactionHistory(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        const { page, pageSize, sortOrder, startDate, endDate } = req.query ?? {};

        if (!id) {
            throw new Error('Bank account ID is required');
        }

        return await bankAccountService.getTransactionHistory(id, {
            page: page ? Number(page) : undefined,
            pageSize: pageSize ? Number(pageSize) : undefined,
            sortOrder: sortOrder as 'asc' | 'desc' | undefined,
            startDate: startDate ? new Date(startDate) : undefined,
            endDate: endDate ? new Date(endDate) : undefined
        });
    }

    async generateBankStatement(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        const { startDate, endDate, page, pageSize } = req.query ?? {};

        if (!id || !startDate || !endDate) {
            throw new Error('Bank account ID, start date, and end date are required');
        }

        return await bankAccountService.generateBankStatement(id, {
            startDate: new Date(startDate),
            endDate: new Date(endDate),
            page: page ? Number(page) : undefined,
            pageSize: pageSize ? Number(pageSize) : undefined
        });
    }

    async reconcileBalance(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        if (!id) {
            throw new Error('Bank account ID is required');
        }
        return await bankAccountService.reconcileBalance(id);
    }

}

export const bankAccountController = new BankAccountController();