import { Table, DatePicker, Card } from 'antd'
import { useState, useEffect } from 'react'
import { useApi } from '@/renderer/hooks'
import { smallCounterApi } from '@/renderer/services'
import { formatDate, formatCurrency } from '@/renderer/utils'
import { TransactionsResponse } from '@/common/types'

const { RangePicker } = DatePicker

export const SmallCounterTransactionHistory = () => {
  const [dateRange, setDateRange] = useState<[Date, Date] | null>(null)
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  const {
    data: transactions,
    isLoading,
    request: fetchTransactions
  } = useApi<TransactionsResponse, [number, number, Date | undefined, Date | undefined]>(
    smallCounterApi.getAllTransactions
  )

  useEffect(() => {
    fetchTransactions(page, pageSize, dateRange?.[0], dateRange?.[1])
  }, [dateRange, page, pageSize])

  const columns = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => formatDate(date)
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'Amount',
      key: 'amount',
      render: (record: any) => (
        <span className={record.type === 'CREDIT' ? 'text-green-600' : 'text-red-600'}>
          {formatCurrency(record.amount, record.currency.code)}
        </span>
      )
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type'
    },
    {
      title: 'Created By',
      dataIndex: ['createdBy', 'name'],
      key: 'createdBy'
    }
  ]

  return (
    <Card className="rounded-lg">
      <div className="mb-4">
        <RangePicker
          onChange={(_, dateStrings) => {
            if (dateStrings[0] && dateStrings[1]) {
              setDateRange([new Date(dateStrings[0]), new Date(dateStrings[1])])
            } else {
              setDateRange(null)
            }
          }}
        />
      </div>

      <Table
        columns={columns}
        dataSource={transactions?.transactions}
        rowKey="id"
        loading={isLoading}
        pagination={{
          current: page,
          showSizeChanger: true,
          showQuickJumper: true,
          showPrevNextJumpers: true,
          position: ['topRight'],
          pageSize,
          total: transactions?.pagination.total,
          onChange: (page, pageSize) => {
            setPage(page)
            setPageSize(pageSize)
          }
        }}
      />
    </Card>
  )
}
