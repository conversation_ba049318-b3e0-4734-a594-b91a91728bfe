import { prisma } from '../db';
import { CreateExpenseData, DeleteExpenseData, GetExpensesParams } from '@/common/types';
import { Prisma, TransactionLocation } from '@prisma/client';

class ExpenseService {
    async createExpense(data: CreateExpenseData) {
        return await prisma.$transaction(async (tx) => {
            // Create the expense record
            const expense = await tx.expense.create({
                data: {
                    amount: data.amount,
                    category: data.category,
                    description: data.description,
                    date: data.date
                }
            });

            // Create ledger entry for the expense
            await tx.ledgerEntry.create({
                data: {
                    amount: data.amount,
                    type: 'DEBIT',
                    description: `Expense: ${data.category} - ${data.description}`,
                    sourceType: data.paymentSource,
                    destinationType: 'OTHER',
                    transactionType: 'EXPENSE',
                    currency: { connect: { code: 'PKR' } },
                    expense: { connect: { id: expense.id } },
                    bankAccount: data.paymentSource === 'BANK_ACCOUNT' ?
                        { connect: { id: data.bankAccountId } } : undefined,
                    createdBy: { connect: { id: data.userId } }
                }
            });

            // Update source balance
            switch (data.paymentSource) {
                case 'SMALL_COUNTER':
                    const smallCounter = await tx.smallCounter.findFirst()
                    if (!smallCounter) throw new Error('Small counter not initialized')
                    if (smallCounter.pkrBalance < data.amount) throw new Error('Small counter does not have sufficient balance')
                    await tx.smallCounter.updateMany({
                        data: { pkrBalance: { decrement: data.amount } }
                    });
                    break;
                case 'CASH_VAULT':
                    const cashVault = await tx.cashVault.findFirst()
                    if (!cashVault) throw new Error('Cash vault not initialized')
                    if (cashVault.pkrBalance < data.amount) throw new Error('Cash vault does not have sufficient balance')
                    await tx.cashVault.updateMany({
                        data: { pkrBalance: { decrement: data.amount } }
                    });
                    break;
                case 'BANK_ACCOUNT':
                    if (!data.bankAccountId) throw new Error('Bank account ID required');
                    const bankAccount = await tx.bankAccount.findUnique({
                        where: { id: data.bankAccountId }
                    })
                    if (!bankAccount) throw new Error('Bank account not found');
                    if (bankAccount.balance < data.amount) throw new Error('Bank account does not have sufficient balance');
                    await tx.bankAccount.update({
                        where: { id: data.bankAccountId },
                        data: { balance: { decrement: data.amount } }
                    });
                    break;
            }

            return expense;
        });
    }

    async deleteExpense(data: DeleteExpenseData) {

        const { id, reason, userId } = data

        return await prisma.$transaction(async (tx) => {
            const expense = await tx.expense.findUnique({
                where: { id: id },
                include: { ledgerEntry: true }
            });

            if (!expense) throw new Error('Expense not found');
            if (expense.isDeleted) throw new Error('Expense already deleted');

            const ledgerEntry = expense.ledgerEntry;
            if (!ledgerEntry) throw new Error('Ledger entry not found');

            // Reverse the balance update
            switch (ledgerEntry.sourceType) {
                case 'SMALL_COUNTER':
                    await tx.smallCounter.updateMany({
                        data: { pkrBalance: { increment: expense.amount } }
                    });
                    break;
                case 'CASH_VAULT':
                    await tx.cashVault.updateMany({
                        data: { pkrBalance: { increment: expense.amount } }
                    });
                    break;
                case 'BANK_ACCOUNT':
                    if (!ledgerEntry.bankAccountId) throw new Error('Bank account ID not found');
                    await tx.bankAccount.update({
                        where: { id: ledgerEntry.bankAccountId },
                        data: { balance: { increment: expense.amount } }
                    });
                    break;
            }

            // Soft delete expense and ledger entry
            await Promise.all([
                tx.expense.update({
                    where: { id },
                    data: {
                        isDeleted: true,
                        deleteReason: reason,
                        deletedAt: new Date()
                    }
                }),
                tx.ledgerEntry.update({
                    where: { id: ledgerEntry.id },
                    data: {
                        isDeleted: true,
                        deleteReason: reason,
                        deletedAt: new Date(),
                        deletedById: userId
                    }
                })
            ]);

            return expense;
        });
    }

    async getExpenses({
        page = 1,
        pageSize = 20,
        category,
        startDate,
        endDate,
        includeDeleted = false,
        orderBy = 'desc'
    }: GetExpensesParams) {
        // Handle date filtering
        let dateFilter = {}
        if (startDate && endDate) {
            if (startDate.toDateString() === endDate.toDateString()) {
                // Same day - filter for entire day
                const start = new Date(startDate)
                start.setHours(0, 0, 0, 0)
                const end = new Date(startDate)
                end.setHours(23, 59, 59, 999)
                dateFilter = {
                    date: {
                        gte: start,
                        lte: end
                    }
                }
            } else {
                // Date range
                dateFilter = {
                    date: {
                        gte: startDate,
                        lte: endDate
                    }
                }
            }
        }

        const where: Prisma.ExpenseWhereInput = {
            ...(category && { category }),
            ...dateFilter,
            ...(!includeDeleted && { isDeleted: false })
        };

        const [expenses, total] = await Promise.all([
            prisma.expense.findMany({
                where,
                include: {
                    ledgerEntry: {
                        include: {
                            bankAccount: true,
                            createdBy: {
                                select: {
                                    id: true,
                                    name: true
                                }
                            }
                        }
                    }
                },
                orderBy: { date: orderBy },
                skip: (page - 1) * pageSize,
                take: pageSize
            }),
            prisma.expense.count({ where })
        ]);

        return {
            expenses,
            pagination: {
                total,
                pages: Math.ceil(total / pageSize),
                currentPage: page,
                pageSize
            }
        };
    }
}

export const expenseService = new ExpenseService();