import { http } from './http';
import { Channels } from '@/common/constants';
import { CreatePartnerData, GetPartnersParams } from '@/common/types';

export const createPartner = async (data: CreatePartnerData) => {
    return await http.post(Channels.CREATE_PARTNER, { body: data });
};

export const deletePartner = async (id: string, deleteReason: string) => {
    return await http.delete(Channels.DELETE_PARTNER, {
        params: { id },
        body: { deleteReason }
    });
};

export const getPartners = async (params: Partial<GetPartnersParams> = {}) => {
    const defaultParams: GetPartnersParams = {
        page: params.page ?? 1,
        limit: params.limit ?? 10,
        includeDeleted: params.includeDeleted,
        search: params.search
    };

    return await http.get(Channels.GET_PARTNERS, { query: defaultParams });
};

export const getPartnerById = async (id: string) => {
    return await http.get(Channels.GET_PARTNER, { params: { id } });
};

// Utility function for dropdowns/select components
export const getPartnersForSelect = async () => {
    return await http.get(Channels.GET_PARTNERS_SELECT);
};