import { http } from './http'
import { Channels } from '@/common/constants'

// Account Management
export async function clearAccounts() {
    return await http.post(Channels.CLEAR_ACCOUNTS)
}

export async function clearAccountBalances() {
    return await http.post(Channels.CLEAR_ACCOUNT_BALANCES)
}

// Container & Items
export async function clearContainers() {
    return await http.post(Channels.CLEAR_CONTAINERS)
}

export async function clearCars() {
    return await http.post(Channels.CLEAR_CARS)
}

export async function clearCarParts() {
    return await http.post(Channels.CLEAR_CAR_PARTS)
}

export async function clearElectronics() {
    return await http.post(Channels.CLEAR_ELECTRONICS)
}

export async function clearScraps() {
    return await http.post(Channels.CLEAR_SCRAPS)
}

// Partners
export async function clearPartners() {
    return await http.post(Channels.CLEAR_PARTNERS)
}

// Sales & Payments
export async function clearSales() {
    return await http.post(Channels.CLEAR_SALES)
}

export async function clearPayments() {
    return await http.post(Channels.CLEAR_PAYMENTS)
}

// Cash Management
export async function clearSmallCounter() {
    return await http.post(Channels.CLEAR_SMALL_COUNTER)
}

export async function clearCashVault() {
    return await http.post(Channels.CLEAR_CASH_VAULT)
}

export async function clearBankAccounts() {
    return await http.post(Channels.CLEAR_BANK_ACCOUNTS)
}

// Currency & Exchange
export async function clearCurrencyExchanges() {
    return await http.post(Channels.CLEAR_CURRENCY_EXCHANGES)
}

// Expenses
export async function clearExpenses() {
    return await http.post(Channels.CLEAR_EXPENSES)
}

// Properties & Rent
export async function clearProperties() {
    return await http.post(Channels.CLEAR_PROPERTIES)
}

export async function clearRentedProperties() {
    return await http.post(Channels.CLEAR_RENTED_PROPERTIES)
}


// Ledger
export async function clearLedgerEntries() {
    return await http.post(Channels.CLEAR_LEDGER_ENTRIES)
}

// Clear All Data
export async function clearAllData() {
    return await http.post(Channels.CLEAR_ALL_DATA)
}
