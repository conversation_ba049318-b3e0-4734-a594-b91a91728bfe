import { Table, Button, Popconfirm, message, Input } from 'antd'
import { FiTrash2, FiEye } from 'react-icons/fi'
import { useEffect, useState } from 'react'
import { useApi } from '@/renderer/hooks'
import { containerApi } from '@/renderer/services'
import { ContainersDetailModal } from './ContainersDetailModal'
import { formatDate, formatCurrency } from '@/renderer/utils'
import { GetContainersParams, GetContainersResponse } from '@/common/types'
import { Dayjs } from 'dayjs'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'

type Container = {
  id: string
  containerNumber: string
  openedAt: Date
  driverExpense: number
  taxes: number
  containerCost: number
  fieldRent: number
  isDeleted: boolean
  deleteReason: string | null
  deletedAt: Date | null
  partnerId: string | null
  routeExpense: number
}

interface ContainerListProps {
  searchQuery: string
  refreshTrigger: number
  startDate: Dayjs | null
  endDate: Dayjs | null
  partnerId: string | undefined
  orderBy: 'asc' | 'desc'
}

export const ContainerList = ({
  searchQuery,
  refreshTrigger,
  startDate,
  endDate,
  partnerId,
  orderBy
}: ContainerListProps) => {
  const [selectedContainer, setSelectedContainer] = useState<string | null>(null)
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [deleteReason, setDeleteReason] = useState('')
  const [deleteContainerId, setDeleteContainerId] = useState<string | null>(null)

  const user = useSelector((state: IRootState) => state.user.data)

  const {
    data,
    isLoading,
    request: fetchContainers,
    error: fetchContainersError
  } = useApi<GetContainersResponse, [GetContainersParams]>(containerApi.getContainers)

  useEffect(() => {
    fetchContainers({
      page,
      limit: pageSize,
      search: searchQuery,
      startDate: startDate?.startOf('day').toDate(),
      endDate: endDate?.endOf('day').toDate(),
      partnerId,
      orderBy
    })
  }, [page, pageSize, searchQuery, refreshTrigger, startDate, endDate, partnerId, orderBy])

  const handleDelete = async () => {
    if (!deleteContainerId) return

    const trimmedReason = deleteReason.trim()
    if (!trimmedReason) {
      message.error('Please provide a reason for deletion')
      return
    }

    const response = await containerApi.deleteContainer(
      deleteContainerId,
      trimmedReason,
      user?.id || ''
    )

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Container deleted successfully')
    setDeleteReason('')
    setDeleteContainerId(null)
    fetchContainers({
      page,
      limit: pageSize,
      search: searchQuery,
      startDate: startDate?.startOf('day').toDate(),
      endDate: endDate?.endOf('day').toDate(),
      partnerId,
      orderBy
    })
  }

  const columns = [
    {
      title: 'Sr. No',
      key: 'serialNumber',
      render: (_: any, __: any, index: number) => index + 1
    },
    {
      title: 'Container Number',
      dataIndex: 'containerNumber',
      key: 'containerNumber'
    },
    {
      title: 'Opened Date',
      dataIndex: 'openedAt',
      key: 'openedAt',
      render: (date: string) => formatDate(date)
    },
    {
      title: 'Total Cost',
      key: 'totalCost',
      render: (record: any) =>
        formatCurrency(
          record.containerCost +
            record.driverExpense +
            record.taxes +
            record.fieldRent +
            record.routeExpense,
          'PKR'
        )
    },
    {
      title: 'Sales - Expenses',
      key: 'salesMinusExpenses',
      render: (record: any) => {
        const value = record.salesMinusExpenses || 0
        return (
          <span className={value >= 0 ? 'text-green-600' : 'text-red-600'}>
            {formatCurrency(value, 'PKR')}
          </span>
        )
      }
    },
    {
      title: 'Partner',
      dataIndex: ['partner', 'name'],
      key: 'partner'
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: any) => (
        <div className="flex gap-3">
          <Button type="text" icon={<FiEye />} onClick={() => setSelectedContainer(record.id)} />
          <Popconfirm
            title="Delete Container"
            description={
              <div>
                <p className="mb-2">Are you sure you want to delete this container?</p>
                <Input
                  placeholder="Enter reason for deletion"
                  value={deleteReason}
                  onChange={(e) => setDeleteReason(e.target.value)}
                  onKeyDown={(e) => e.stopPropagation()}
                />
              </div>
            }
            onConfirm={handleDelete}
            onCancel={() => {
              setDeleteReason('')
              setDeleteContainerId(null)
            }}
            okButtonProps={{ danger: true }}
            open={deleteContainerId === record.id}
            onOpenChange={(visible) => {
              if (visible) {
                setDeleteContainerId(record.id)
              } else {
                setDeleteContainerId(null)
                setDeleteReason('')
              }
            }}
          >
            <Button type="text" danger icon={<FiTrash2 />} />
          </Popconfirm>
        </div>
      )
    }
  ]

  return (
    <>
      <Table
        columns={columns}
        dataSource={data?.containers}
        loading={isLoading}
        rowKey="id"
        virtual
        sticky
        size="small"
        pagination={{
          current: page,
          pageSize,
          position: ['topRight'],
          total: data?.total,
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '50', '100'],
          showQuickJumper: true,
          onChange: (page, pageSize) => {
            setPage(page)
            setPageSize(pageSize)
          },
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
        }}
      />

      <ContainersDetailModal
        containerId={selectedContainer}
        onClose={() => setSelectedContainer(null)}
      />
    </>
  )
}
