import { Tabs, Card } from 'antd'
import { CurrencyList } from './components/CurrencyList'
import { CurrencyTransactions } from './components/CurrencyTransactions'
import { CurrencyBalances } from './components/CurrencyBalances'

const Currency = () => {
  return (
    <div className="flex h-full flex-col gap-4">
      {/* <Card className="mx-6 mt-6 rounded-lg shadow-sm">
        <CurrencyList />
      </Card> */}

      <Tabs
        className="m-6 rounded-lg shadow-sm"
        type="card"
        items={[
          {
            key: 'transactions',
            label: 'Transactions',
            children: <CurrencyTransactions />
          },
          {
            key: 'balances',
            label: 'Balances',
            children: <CurrencyBalances />
          }
        ]}
      />
    </div>
  )
}

export default Currency
