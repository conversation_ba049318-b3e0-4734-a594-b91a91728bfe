import { useState, useEffect } from 'react'
import {
  Drawer,
  Form,
  Input,
  InputNumber,
  DatePicker,
  Select,
  Button,
  App,
  Radio,
  Divider
} from 'antd'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { manualEntryApi, accountsApi, bankAccount<PERSON>pi } from '@/renderer/services'
import { TransactionType } from '@prisma/client'
import type { CreateManualEntryData } from '@/common/types/manualEntry'
import dayjs from 'dayjs'
import { useAccountContext, useBankContext } from '@/renderer/contexts'

const { Option } = Select
const { TextArea } = Input

interface CreateManualEntryDrawerProps {
  open: boolean
  onClose: () => void
  onSuccess: () => void
}

export const CreateManualEntryDrawer = ({
  open,
  onClose,
  onSuccess
}: CreateManualEntryDrawerProps) => {
  const [form] = Form.useForm()
  const { message } = App.useApp()
  const [loading, setLoading] = useState(false)
  const currentUser = useSelector((state: IRootState) => state.user.data)

  const { accounts } = useAccountContext()
  const { banks } = useBankContext()

  // Available currencies
  const currencies = [
    { value: 'PKR', label: 'PKR' },
    { value: 'USD', label: 'USD' },
    { value: 'AED', label: 'AED' },
    { value: 'AFN', label: 'AFN' }
  ]

  const handleSubmit = async (values: any) => {
    if (!currentUser?.id) {
      message.error('User not authenticated')
      return
    }

    setLoading(true)

    // Validate that exactly one target is selected
    const targetCount = [values.accountId, values.bankAccountId, values.targetType].filter(
      (x) => x !== undefined && x !== null
    ).length
    if (targetCount !== 1) {
      message.error('Please select exactly one target (account, bank account, or target type)')
      setLoading(false)

      return
    }

    // Validate currency for bank accounts
    if (values.bankAccountId && values.currencyCode !== 'PKR') {
      message.error('Bank accounts only support PKR currency')
      setLoading(false)
      return
    }

    const data: CreateManualEntryData = {
      amount: values.amount,
      description: values.description,
      transactionDate: values.transactionDate.toDate(),
      entryType: values.entryType,
      currencyCode: values.currencyCode,
      accountId: values.accountId,
      bankAccountId: values.bankAccountId,
      targetType: values.targetType,
      createdById: currentUser.id
    }

    const response = await manualEntryApi.createManualEntry(data)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      setLoading(false)
      return
    }

    message.success('Manual entry created successfully')
    form.resetFields()
    onSuccess()
    setLoading(false)
  }

  const handleClose = () => {
    form.resetFields()
    onClose()
  }

  const handleTargetTypeChange = () => {
    // Clear other target fields when target type changes
    form.setFieldsValue({
      accountId: undefined,
      bankAccountId: undefined
    })
  }

  const handleAccountChange = () => {
    // Clear other target fields when account changes
    form.setFieldsValue({
      bankAccountId: undefined,
      targetType: undefined
    })
  }

  const handleBankAccountChange = (value: any) => {
    // Clear other target fields when bank account changes
    form.setFieldsValue({
      accountId: undefined,
      targetType: undefined
    })

    // If bank account is selected, automatically set currency to PKR
    if (value) {
      form.setFieldsValue({
        currencyCode: 'PKR'
      })
    }
  }

  return (
    <Drawer
      title="Create Manual Entry"
      open={open}
      onClose={handleClose}
      width={600}
      footer={
        <div className="flex justify-end gap-2">
          <Button onClick={handleClose}>Cancel</Button>
          <Button type="primary" loading={loading} onClick={() => form.submit()}>
            Create Entry
          </Button>
        </div>
      }
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          transactionDate: dayjs(),
          entryType: TransactionType.DEBIT,
          currencyCode: 'PKR'
        }}
      >
        <Form.Item
          name="amount"
          label="Amount"
          rules={[
            { required: true, message: 'Please enter amount' },
            { type: 'number', min: 0.01, message: 'Amount must be greater than 0' }
          ]}
        >
          <InputNumber
            className="w-full"
            placeholder="Enter amount"
            min={0.01}
            step={0.01}
            precision={2}
          />
        </Form.Item>

        <Form.Item
          name="entryType"
          label="Entry Type"
          rules={[{ required: true, message: 'Please select entry type' }]}
        >
          <Radio.Group>
            <Radio.Button value={TransactionType.DEBIT}>Debit (-)</Radio.Button>
            <Radio.Button value={TransactionType.CREDIT}>Credit (+)</Radio.Button>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          name="currencyCode"
          label="Currency"
          rules={[{ required: true, message: 'Please select currency' }]}
        >
          <Select placeholder="Select currency">
            {currencies.map((currency) => (
              <Option key={currency.value} value={currency.value}>
                {currency.label}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="transactionDate"
          label="Transaction Date"
          rules={[{ required: true, message: 'Please select transaction date' }]}
        >
          <DatePicker className="w-full" />
        </Form.Item>

        <Form.Item name="description" label="Description">
          <TextArea rows={3} placeholder="Enter description (optional)" maxLength={500} showCount />
        </Form.Item>

        <Divider>Target Selection</Divider>

        <Form.Item name="targetType" label="Cash Vault">
          <Select placeholder="Select cash vault" allowClear onChange={handleTargetTypeChange}>
            <Option value="CASH_VAULT">Cash Vault</Option>
          </Select>
        </Form.Item>

        <Form.Item name="accountId" label="Account">
          <Select
            placeholder="Select account"
            allowClear
            showSearch
            onChange={handleAccountChange}
            filterOption={(input, option) =>
              (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
            }
            options={accounts}
          />
        </Form.Item>

        <Form.Item name="bankAccountId" label="Bank Account">
          <Select
            placeholder="Select bank account"
            allowClear
            showSearch
            onChange={handleBankAccountChange}
            filterOption={(input, option) =>
              (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
            }
            options={banks}
          />
        </Form.Item>

        <div className="mt-4 rounded bg-gray-50 p-3 text-sm text-gray-500">
          <strong>Note:</strong> Please select exactly one target (Cash Vault, Account, or Bank
          Account).
          <br />
          <strong>Currency Note:</strong> Bank accounts only support PKR currency. Cash Vault and
          Accounts support all currencies.
        </div>
      </Form>
    </Drawer>
  )
}
