import { useState, useEffect } from 'react'
import {
  Drawer,
  Form,
  Input,
  InputNumber,
  DatePicker,
  Select,
  Button,
  App,
  Radio,
  Divider
} from 'antd'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { manualEntryApi, accountApi, bankAccountApi } from '@/renderer/services'
import { TransactionType } from '@prisma/client'
import type { CreateManualEntryData } from '@/common/types/manualEntry'
import dayjs from 'dayjs'

const { Option } = Select
const { TextArea } = Input

interface CreateManualEntryDrawerProps {
  open: boolean
  onClose: () => void
  onSuccess: () => void
}

export const CreateManualEntryDrawer = ({
  open,
  onClose,
  onSuccess
}: CreateManualEntryDrawerProps) => {
  const [form] = Form.useForm()
  const { message } = App.useApp()
  const [loading, setLoading] = useState(false)
  const [accounts, setAccounts] = useState<any[]>([])
  const [bankAccounts, setBankAccounts] = useState<any[]>([])
  const currentUser = useSelector((state: IRootState) => state.user.data)

  // Available currencies
  const currencies = [
    { value: 'PKR', label: 'PKR' },
    { value: 'USD', label: 'USD' },
    { value: 'AED', label: 'AED' },
    { value: 'AFN', label: 'AFN' }
  ]

  // Fetch accounts and bank accounts
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [accountsRes, bankAccountsRes] = await Promise.all([
          accountApi.getAccounts({ page: 1, pageSize: 1000 }),
          bankAccountApi.getBankAccounts()
        ])

        if (accountsRes.data?.data?.accounts) {
          setAccounts(
            accountsRes.data.data.accounts.map((acc: any) => ({
              value: acc.id,
              label: `${acc.name} (${acc.type})`,
              type: acc.type
            }))
          )
        }

        if (bankAccountsRes.data?.data) {
          setBankAccounts(
            bankAccountsRes.data.data.map((bank: any) => ({
              value: bank.id,
              label: `${bank.bankName} - ${bank.accountNumber}`,
              bankName: bank.bankName
            }))
          )
        }
      } catch (error) {
        console.error('Failed to fetch accounts or bank accounts:', error)
      }
    }

    if (open) {
      fetchData()
    }
  }, [open])

  const handleSubmit = async (values: any) => {
    if (!currentUser?.id) {
      message.error('User not authenticated')
      return
    }

    try {
      setLoading(true)

      // Validate that exactly one target is selected
      const targetCount = [values.accountId, values.bankAccountId, values.targetType].filter(
        (x) => x !== undefined && x !== null
      ).length
      if (targetCount !== 1) {
        message.error('Please select exactly one target (account, bank account, or target type)')
        return
      }

      // Validate currency for bank accounts
      if (values.bankAccountId && values.currencyCode !== 'PKR') {
        message.error('Bank accounts only support PKR currency')
        return
      }

      const data: CreateManualEntryData = {
        amount: values.amount,
        description: values.description,
        transactionDate: values.transactionDate.toDate(),
        entryType: values.entryType,
        currencyCode: values.currencyCode,
        accountId: values.accountId,
        bankAccountId: values.bankAccountId,
        targetType: values.targetType,
        createdById: currentUser.id
      }

      await manualEntryApi.createManualEntry(data)
      message.success('Manual entry created successfully')
      form.resetFields()
      onSuccess()
    } catch (error: any) {
      message.error(error.message || 'Failed to create manual entry')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    form.resetFields()
    onClose()
  }

  const handleTargetTypeChange = () => {
    // Clear other target fields when target type changes
    form.setFieldsValue({
      partyId: undefined,
      bankId: undefined
    })
  }

  const handlePartyChange = () => {
    // Clear other target fields when party changes
    form.setFieldsValue({
      bankId: undefined,
      targetType: undefined
    })
  }

  const handleBankChange = () => {
    // Clear other target fields when bank changes
    form.setFieldsValue({
      partyId: undefined,
      targetType: undefined
    })
  }

  return (
    <Drawer
      title="Create Manual Entry"
      open={open}
      onClose={handleClose}
      width={600}
      footer={
        <div className="flex justify-end gap-2">
          <Button onClick={handleClose}>Cancel</Button>
          <Button type="primary" loading={loading} onClick={() => form.submit()}>
            Create Entry
          </Button>
        </div>
      }
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          transactionDate: dayjs(),
          entryType: CreditDebit.DEBIT
        }}
      >
        <Form.Item
          name="amount"
          label="Amount"
          rules={[
            { required: true, message: 'Please enter amount' },
            { type: 'number', min: 0.01, message: 'Amount must be greater than 0' }
          ]}
        >
          <InputNumber
            className="w-full"
            placeholder="Enter amount"
            min={0.01}
            step={0.01}
            precision={2}
          />
        </Form.Item>

        <Form.Item
          name="entryType"
          label="Entry Type"
          rules={[{ required: true, message: 'Please select entry type' }]}
        >
          <Radio.Group>
            <Radio.Button value={CreditDebit.DEBIT}>Debit (-)</Radio.Button>
            <Radio.Button value={CreditDebit.CREDIT}>Credit (+)</Radio.Button>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          name="transactionDate"
          label="Transaction Date"
          rules={[{ required: true, message: 'Please select transaction date' }]}
        >
          <DatePicker className="w-full" />
        </Form.Item>

        <Form.Item name="description" label="Description">
          <TextArea rows={3} placeholder="Enter description (optional)" maxLength={500} showCount />
        </Form.Item>

        <Divider>Target Selection</Divider>

        <Form.Item name="targetType" label="Cash Location">
          <Select
            placeholder="Select cash location"
            showSearch
            filterOption={(input, option) =>
              (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
            }
            allowClear
            onChange={handleTargetTypeChange}
          >
            <Option value="CASH_VAULT">Cash Vault</Option>
            <Option value="SMALL_COUNTER">Small Counter</Option>
          </Select>
        </Form.Item>

        <Form.Item name="partyId" label="Party">
          <Select
            placeholder="Select party"
            allowClear
            showSearch
            onChange={handlePartyChange}
            filterOption={(input, option) =>
              (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
            }
            options={allParties}
          />
        </Form.Item>

        <Form.Item name="bankId" label="Bank">
          <Select
            placeholder="Select bank"
            allowClear
            showSearch
            onChange={handleBankChange}
            filterOption={(input, option) =>
              (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
            }
            options={banks}
          />
        </Form.Item>

        <div className="mt-4 rounded bg-gray-50 p-3 text-sm text-gray-500">
          <strong>Note:</strong> Please select exactly one target (Cash Location, Party, or Bank).
          The entry will adjust the balance of the selected target.
        </div>
      </Form>
    </Drawer>
  )
}
