import { useEffect, useState } from 'react'
import {
  Card,
  Select,
  DatePicker,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  message,
  Dropdown,
  Checkbox
} from 'antd'
import { FaSearch, FaFilePdf, FaEllipsisV } from 'react-icons/fa'
import { ledgerApi } from '@/renderer/services'
import { formatCurrencyWithoutSymbol } from '@/renderer/utils'
import { PDFConfirmationModal } from '@/renderer/components'
import { handleAccountLedgerPDF } from '../utils'
import dayjs from 'dayjs'
import { useAccountContext, useTheme } from '@/renderer/contexts'
import type { TablePaginationConfig } from 'antd/es/table'

const { RangePicker } = DatePicker

// Available currencies
const CURRENCIES = [
  { value: 'PKR', label: 'PKR - Pakistani Rupee' },
  { value: 'USD', label: 'USD - US Dollar' },
  { value: 'AED', label: 'AED - UAE Dirham' },
  { value: 'AFN', label: 'AFN - Afghan Afghani' }
]

// Available page sizes
const PAGE_SIZE_OPTIONS = ['10', '20', '50', '100', '1000', '5000', '10000']

// Table size options
const TABLE_SIZE_OPTIONS = [
  { label: 'Small', value: 'small' },
  { label: 'Middle', value: 'middle' },
  { label: 'Large', value: 'large' }
]

type CustomPageSize = number

export const AccountLedger = () => {
  const [loading, setLoading] = useState(false)
  const [selectedAccount, setSelectedAccount] = useState<string>()
  const [selectedCurrency, setSelectedCurrency] = useState<string>()
  const [selectedAccountType, setSelectedAccountType] = useState('All')
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | undefined>()
  const [tableSize, setTableSize] = useState<'small' | 'middle' | 'large'>('small')
  const [data, setData] = useState<any[]>([])
  const [customPageSize, setCustomPageSize] = useState<CustomPageSize>(10)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalItems, setTotalItems] = useState(0)
  const [isPDFModalOpen, setIsPDFModalOpen] = useState(false)
  // State for PDF generation
  const [isPDFGenerating, setIsPDFGenerating] = useState(false)

  const [windowWidth, setWindowWidth] = useState(window.innerWidth)

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth)
    window.addEventListener('resize', handleResize)

    // Clean up the event listener on component unmount
    return () => window.removeEventListener('resize', handleResize)
  }, []) // Empty dependency array ensures this only runs once on mount

  // Column visibility state
  const [visibleColumns, setVisibleColumns] = useState({
    transactionType: false,
    createdBy: false
  })

  const { accounts, tenants } = useAccountContext()
  const { isDarkMode } = useTheme()

  const handleSearch = async (page = 1, pageSize: CustomPageSize = customPageSize) => {
    if (!selectedAccount || !selectedCurrency) return

    setLoading(true)
    const response = await ledgerApi.getPaginatedLedgerEntries(selectedAccount, {
      page,
      limit: pageSize,
      currencyId: selectedCurrency,
      startDate: dateRange?.[0] ? dateRange[0].toISOString() : undefined,
      endDate: dateRange?.[1] ? dateRange[1].toISOString() : undefined
    })
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    setData(response.data.data.entries)
    setCurrentPage(response.data.data.pagination.page)
    setCustomPageSize(response.data.data.pagination.limit)
    setTotalItems(response.data.data.pagination.total)

    message.success('Ledger loaded successfully')
  }

  console.log(data)

  const handleTableChange = (pagination: TablePaginationConfig) => {
    const newPageSize = pagination.pageSize || customPageSize

    if (newPageSize !== customPageSize) {
      handleSearch(1, newPageSize)
    } else {
      handleSearch(pagination.current || 1, customPageSize)
    }
  }

  const getColorOnTransactionType = (transactionType: string) => {
    let color = '#6B7280' // default neutral color (gray-500)
    if (transactionType === 'SALE') color = 'green' // positive income (emerald-500)
    if (transactionType === 'PAYMENT') color = 'magenta' // incoming payment (sky-500)
    if (transactionType === 'EXPENSE') color = 'red' // money going out (rose-600)
    if (transactionType === 'CURRENCY_EXCHANGE') color = 'purple' // currency conversion (violet-500)
    if (transactionType === 'TRANSFER') color = 'gold' // internal movement (amber-500)
    if (transactionType === 'RENT') color = 'yellow' // recurring income (yellow-600)
    if (transactionType === 'DEPOSIT') color = 'geekblue' // money coming in (teal-500)
    if (transactionType === 'CURRENCY_DEPOSIT') color = 'lime' // foreign currency in (teal-600)
    if (transactionType === 'WITHDRAWAL') color = 'red' // money going out (red-500)
    if (transactionType === 'CURRENCY_WITHDRAWAL') color = 'magenta' // foreign currency out (red-600)
    if (transactionType === 'OPENING_BALANCE') color = 'cyan' // initial entry (blue-600)
    if (transactionType === 'OTHER') color = 'gray' // miscellaneous (gray-400)
    return color
  }

  const columns = [
    {
      title: 'S.#',
      dataIndex: 'serialNumber',
      key: 'serialNumber',
      width: 70,
      render: (_: any, __: any, index: number) => {
        // Calculate serial number based on current page and page size
        return (currentPage - 1) * Number(customPageSize) + index + 1
      }
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => dayjs(date).format('DD-MM-YYYY')
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      width: windowWidth > 1300 ? 550 : windowWidth > 900 ? 350 : 200,
      ellipsis: {
        showTitle: false
      },
      render: (description: string) => (
        <Typography.Text
          ellipsis={{
            tooltip: {
              title: description,
              mouseEnterDelay: 0.3
            }
          }}
        >
          {description}
        </Typography.Text>
      )
    },
    {
      title: 'Credit',
      dataIndex: 'type',
      key: 'credit',
      align: 'center' as const,
      // width: 120,
      render: (type: string, record: any) =>
        type === 'CREDIT' ? (
          <Typography.Text
            type="success"
            className={`flex flex-1 justify-center font-bold ${
              isDarkMode ? 'bg-green-900/30 dark:text-green-400' : 'bg-green-100'
            }`}
          >
            {formatCurrencyWithoutSymbol(record.amount)}
          </Typography.Text>
        ) : (
          '-'
        )
    },
    {
      title: 'Debit',
      dataIndex: 'type',
      key: 'debit',
      align: 'center' as const,
      // width: 120,
      render: (type: string, record: any) =>
        type === 'DEBIT' ? (
          <Typography.Text
            type="danger"
            className={`flex flex-1 justify-center font-bold ${
              isDarkMode ? 'bg-red-900/30 dark:text-red-400' : 'bg-red-100'
            }`}
          >
            {formatCurrencyWithoutSymbol(record.amount)}
          </Typography.Text>
        ) : (
          '-'
        )
    },
    {
      title: 'Balance',
      dataIndex: 'balance',
      key: 'balance',
      align: 'center' as const,
      // width: 120,
      render: (balance: number) => (
        <Typography.Text
          type={balance > 0 ? 'success' : balance < 0 ? 'danger' : 'secondary'}
          className={`flex flex-1 justify-center font-bold ${
            isDarkMode
              ? balance > 0
                ? 'bg-green-900/20 dark:text-green-400'
                : balance < 0
                  ? 'bg-red-900/20 dark:text-red-400'
                  : 'bg-gray-800/50'
              : balance > 0
                ? 'bg-green-50'
                : balance < 0
                  ? 'bg-red-50'
                  : 'bg-gray-50'
          }`}
        >
          {formatCurrencyWithoutSymbol(balance)}
        </Typography.Text>
      )
    },
    {
      title: 'Transaction Type',
      dataIndex: 'transactionType',
      key: 'transactionType',
      // width: 120,
      hidden: !visibleColumns.transactionType,
      ellipsis: {
        showTitle: false
      },
      render: (transactionType: string) => (
        <Typography.Text
          ellipsis={{
            tooltip: {
              title: transactionType,
              mouseEnterDelay: 0.3
            }
          }}
        >
          <Tag color={getColorOnTransactionType(transactionType)}>{transactionType}</Tag>
        </Typography.Text>
      )
    },
    {
      title: 'Created By',
      dataIndex: 'createdBy',
      key: 'createdBy',
      ellipsis: {
        showTitle: true
      },
      hidden: !visibleColumns.createdBy,
      render: (createdBy: any) => createdBy?.name || '-'
    }
  ]

  const resetState = () => {
    setData([])
    setCurrentPage(1)
  }

  const handlePDFAction = async (action: 'save' | 'print') => {
    if (!selectedAccount || !selectedCurrency) return

    try {
      setIsPDFGenerating(true)
      const account = [...accounts, ...tenants].find((acc) => acc.value === selectedAccount)
      if (!account) {
        throw new Error('Account not found')
      }

      await handleAccountLedgerPDF(selectedAccount, selectedCurrency, account.label, action)
      message.success(`PDF ${action === 'save' ? 'saved' : 'opened for printing'} successfully`)
    } catch (error) {
      console.error(`Failed to ${action} PDF:`, error)
      message.error(`Failed to ${action} PDF. Please try again.`)
    } finally {
      setIsPDFGenerating(false)
      setIsPDFModalOpen(false)
    }
  }

  // Create pagination config for Table
  const paginationConfig: TablePaginationConfig = {
    current: currentPage,
    pageSize: Number(customPageSize),
    total: totalItems,
    pageSizeOptions: PAGE_SIZE_OPTIONS,
    showSizeChanger: true,
    showQuickJumper: true,
    position: ['topRight'],
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  const columnVisibilityMenu = (
    <div
      className={`flex flex-col gap-2 rounded-lg p-3 shadow-md ${
        isDarkMode
          ? 'bg-gradient-to-br from-blue-950 via-slate-950 to-blue-900'
          : 'bg-gradient-to-br from-sky-200 via-white to-blue-200'
      }`}
    >
      <Typography.Title level={5} className="mb-2">
        Show/Hide Columns
      </Typography.Title>
      <Checkbox
        checked={visibleColumns.transactionType}
        onChange={(e) =>
          setVisibleColumns({ ...visibleColumns, transactionType: e.target.checked })
        }
      >
        Transaction Type
      </Checkbox>
      <Checkbox
        checked={visibleColumns.createdBy}
        onChange={(e) => setVisibleColumns({ ...visibleColumns, createdBy: e.target.checked })}
      >
        Created By
      </Checkbox>
    </div>
  )

  return (
    <Card>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Space wrap align="center" style={{ justifyContent: 'space-between', width: '100%' }}>
          <Space wrap>
            <Dropdown
              menu={{ items: [] }}
              dropdownRender={() => columnVisibilityMenu}
              trigger={['click']}
            >
              <Button icon={<FaEllipsisV />} />
            </Dropdown>
            <Select
              placeholder="Select Accounts to show"
              value={selectedAccountType}
              style={{ width: 150 }}
              options={[
                { value: 'All', label: 'All' },
                { value: 'Customers', label: 'Customers' },
                { value: 'Tenants', label: 'Tenants' }
              ]}
              onChange={setSelectedAccountType}
            />

            <Select
              placeholder="Select Account"
              className="w-96"
              options={
                selectedAccountType === 'All'
                  ? [...tenants, ...accounts]
                  : selectedAccountType === 'Customers'
                    ? accounts
                    : tenants
              }
              onChange={(value) => {
                setSelectedAccount(value)
                resetState()
              }}
              showSearch
              filterOption={(input, option) =>
                (option?.label as string).toLowerCase().includes(input.toLowerCase())
              }
            />
            <Select
              placeholder="Select Currency"
              className="w-52"
              options={CURRENCIES}
              value={selectedCurrency}
              onChange={(value) => {
                setSelectedCurrency(value)
                resetState()
              }}
            />
            <RangePicker
              onChange={(dates) => {
                setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])
                resetState()
              }}
              value={dateRange}
              style={{ width: 240 }}
            />
            <Select
              placeholder="Table Size"
              style={{ width: 120 }}
              options={TABLE_SIZE_OPTIONS}
              value={tableSize}
              onChange={setTableSize}
            />
            <Button
              type="primary"
              icon={<FaSearch />}
              onClick={() => handleSearch(1, customPageSize)}
              disabled={!selectedAccount || !selectedCurrency}
            >
              Search
            </Button>
            <Button
              icon={<FaFilePdf />}
              onClick={() => setIsPDFModalOpen(true)}
              disabled={!selectedAccount || !selectedCurrency || !data.length}
              loading={isPDFGenerating}
            >
              Generate PDF
            </Button>
          </Space>
        </Space>

        <Table
          bordered
          columns={columns}
          dataSource={data}
          loading={loading}
          rowKey="id"
          size={tableSize}
          pagination={paginationConfig}
          onChange={handleTableChange}
          sticky
          virtual
          tableLayout="auto"
        />
      </Space>

      <PDFConfirmationModal
        isOpen={isPDFModalOpen}
        onClose={() => setIsPDFModalOpen(false)}
        onSave={() => handlePDFAction('save')}
        onPrint={() => handlePDFAction('print')}
        title="Account Statement PDF"
      />
    </Card>
  )
}
