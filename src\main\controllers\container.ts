import { CreateContainerData, GetContainersParams, IRequest } from '../../common';
import { containerService } from '../services';

class ContainerController {
    async createContainer(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const data = req.body as CreateContainerData;

        if (!data.containerNumber) {
            throw new Error('Container number is required');
        }

        return await containerService.createContainer(data);
    }

    async deleteContainer(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        const { deleteReason, deletedById } = req.body ?? {};

        if (!id) throw new Error('Container ID is required');
        if (!deleteReason) throw new Error('Delete reason is required');
        if (!deletedById) throw new Error('Deleted by ID is required');

        return await containerService.deleteContainer(id, deleteReason, deletedById);
    }

    async getContainers(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { page = 1, limit = 10, includeDeleted, partnerId, search, startDate, endDate, orderBy } = req.query as GetContainersParams;
        return await containerService.getContainers({
            page: Number(page),
            limit: Number(limit),
            includeDeleted: Boolean(includeDeleted),
            partnerId,
            search,
            startDate,
            endDate,
            orderBy: orderBy as 'asc' | 'desc' || 'asc' // Default to ascending order if not specified
        });
    }

    async getContainerStock(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { containerId } = req.params ?? {};
        const { page = 1, limit = 10, itemType } = req.query ?? {};

        if (!containerId) throw new Error('Container ID is required');

        return await containerService.getContainerStock({
            containerId,
            page: Number(page),
            limit: Number(limit),
            itemType: itemType as any
        });
    }

    async getContainerById(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        if (!id) throw new Error('Container ID is required');

        return await containerService.getContainerById(id);
    }

    async getContainerSummary(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        if (!id) throw new Error('Container ID is required');

        return await containerService.getContainerSummary(id);
    }

    async checkCarWithSameChassisNumberExists(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { chassisNumber } = req.body ?? {};
        if (!chassisNumber) throw new Error('Chassis number is required');

        return await containerService.checkCarWithSameChassisNumberExists(chassisNumber);
    }

    async getContainersForPDF(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { startDate, endDate, partnerId } = req.query ?? {};

        // Convert string dates to Date objects if provided
        const parsedStartDate = startDate ? new Date(startDate as string) : undefined;
        const parsedEndDate = endDate ? new Date(endDate as string) : undefined;

        return await containerService.getContainersForPDF(
            parsedStartDate,
            parsedEndDate,
            partnerId as string | undefined
        );
    }
}

export const containerController = new ContainerController();