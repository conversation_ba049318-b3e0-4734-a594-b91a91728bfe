import { useState, useEffect } from 'react'
import { Card, Form, Input, Select, DatePicker, InputNumber, Button, App, Divider } from 'antd'
import type { CreatePaymentData } from '@/common/types'
import { paymentApi } from '@/renderer/services'
import { useAccountContext, useBankContext } from '@/renderer/contexts'
import { ArrowLeftOutlined } from '@ant-design/icons'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { handleDatePickerValue } from '@/renderer/utils'

interface CreatePaymentProps {
  onClose: () => void
  setRefreshTrigger: (value: any) => void
}

export const CreatePayment = ({ onClose, setRefreshTrigger }: CreatePaymentProps) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [paymentType, setPaymentType] = useState<'PAID' | 'RECEIVED'>('PAID')

  const { message } = App.useApp()
  const { accounts } = useAccountContext()
  const { banks } = useBankContext()

  const user = useSelector((state: IRootState) => state.user.data)

  const sourceSelection = Form.useWatch('sourceLocation', form)
  const destinationSelection = Form.useWatch('destinationType', form)

  // Reset locations when payment type changes
  useEffect(() => {
    if (paymentType === 'PAID') {
      form.setFieldsValue({
        sourceLocation: undefined,
        destinationType: 'ACCOUNT',
        sourceBankId: undefined,
        destinationBankId: undefined
      })
    } else {
      form.setFieldsValue({
        sourceLocation: 'ACCOUNT',
        destinationType: undefined,
        sourceBankId: undefined,
        destinationBankId: undefined
      })
    }
  }, [paymentType])

  const handleSubmit = async (values: any) => {
    setLoading(true)

    const data: CreatePaymentData = {
      ...values,
      date: handleDatePickerValue(values.date?.toDate()),
      userId: user?.id
    }

    const response = await paymentApi.createPayment(data)
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Payment created successfully')
    setRefreshTrigger((prev) => prev + 1)
    form.resetFields()
    onClose()
  }

  const CURRENCIES = ['PKR', 'USD', 'AED', 'AFN']

  const locationOptions = [
    { label: 'Cash Vault', value: 'CASH_VAULT' },
    { label: 'Bank Account', value: 'BANK_ACCOUNT' }
  ]

  return (
    <Card className="mt-4">
      <Button
        icon={<ArrowLeftOutlined />}
        onClick={() => {
          form.resetFields()
          setPaymentType('PAID')
          onClose()
        }}
      />
      <Divider />
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        className="max-w-2xl"
        initialValues={{
          paymentType: 'PAID',
          destinationType: 'ACCOUNT',
          date: null
        }}
      >
        <div className="grid grid-cols-2 gap-4">
          <Form.Item label="Payment Type" name="paymentType" rules={[{ required: true }]}>
            <Select
              onChange={(value) => setPaymentType(value)}
              options={[
                { label: 'Paid', value: 'PAID' },
                { label: 'Received', value: 'RECEIVED' }
              ]}
            />
          </Form.Item>

          <Form.Item label="Account" name="accountId" rules={[{ required: true }]}>
            <Select
              showSearch
              placeholder="Select account"
              optionFilterProp="children"
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
              options={accounts}
            />
          </Form.Item>

          <Form.Item label="Amount" name="amount" rules={[{ required: true }]}>
            <InputNumber
              className="w-full"
              min={Number(1)}
              precision={2}
              placeholder="Enter amount"
              formatter={(value) => `Rs ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={(value) => Number(value!.replace(/Rs\s?|(,*)/g, ''))}
            />
          </Form.Item>

          <Form.Item label="Currency" name="currencyCode" rules={[{ required: true }]}>
            <Select
              showSearch
              placeholder="Select currency"
              optionFilterProp="children"
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
              options={CURRENCIES.map((currency) => ({ label: currency, value: currency }))}
            />
          </Form.Item>

          <Form.Item label="Source Location" name="sourceLocation" rules={[{ required: true }]}>
            <Select
              disabled={paymentType === 'RECEIVED'}
              placeholder={paymentType === 'PAID' ? 'Select source location' : 'Account'}
              options={locationOptions}
            />
          </Form.Item>

          <Form.Item
            label="Destination Location"
            name="destinationType"
            rules={[{ required: true }]}
          >
            <Select
              disabled={paymentType === 'PAID'}
              placeholder={paymentType === 'RECEIVED' ? 'Select destination location' : 'Account'}
              options={locationOptions}
            />
          </Form.Item>

          {sourceSelection === 'BANK_ACCOUNT' && (
            <Form.Item label="Source Bank Account" name="sourceBankId" rules={[{ required: true }]}>
              <Select
                showSearch
                placeholder="Select bank account"
                optionFilterProp="children"
                options={banks}
              />
            </Form.Item>
          )}

          {destinationSelection === 'BANK_ACCOUNT' && (
            <Form.Item
              label="Destination Bank Account"
              name="destinationBankId"
              rules={[{ required: true }]}
            >
              <Select
                showSearch
                placeholder="Select bank account"
                optionFilterProp="children"
                options={banks}
              />
            </Form.Item>
          )}

          <Form.Item label="Date" name="date" className="col-span-2" rules={[{ required: true }]}>
            <DatePicker className="w-full" format="YYYY-MM-DD" placeholder="Select date" />
          </Form.Item>

          <Form.Item label="Description" name="description" className="col-span-2">
            <Input.TextArea rows={3} placeholder="Enter payment description" />
          </Form.Item>
        </div>

        <Form.Item className="mb-0">
          <Button type="primary" htmlType="submit" loading={loading}>
            Create Payment
          </Button>
        </Form.Item>
      </Form>
    </Card>
  )
}
