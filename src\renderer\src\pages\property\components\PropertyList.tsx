import { useEffect, useState } from 'react'
import {
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Tooltip,
  App,
  Popconfirm,
  InputNumber
} from 'antd'
import { propertyApi } from '@/renderer/services'
import type { GetPropertiesParams, Property, GetPropertiesResponse } from '@/common/types'
import { formatCurrency } from '@/renderer/utils'
import { useApi } from '@/renderer/hooks'
import { FaTrash } from 'react-icons/fa'
import { FiTrash2 } from 'react-icons/fi'

interface PropertyListProps {
  refreshTrigger: number
  onRentClick: (propertyId: string) => void
}

export const PropertyList = ({ refreshTrigger, onRentClick }: PropertyListProps) => {
  const [propertyDeleteReason, setPropertyDeleteReason] = useState('')

  const [filters, setFilters] = useState<Partial<GetPropertiesParams>>({
    page: 1,
    limit: 20
  })

  const { message } = App.useApp()

  const {
    data: response,
    isLoading,
    request: fetchProperties
  } = useApi<GetPropertiesResponse, [Partial<GetPropertiesParams>]>(propertyApi.getProperties)

  useEffect(() => {
    fetchProperties(filters)
  }, [filters, refreshTrigger])

  const handleFiltersChange = (key: keyof GetPropertiesParams, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value }))
  }

  const handleDeleteProperty = async (id: string) => {
    const response = await propertyApi.deleteProperty(id, propertyDeleteReason)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('property deleted successfully')
  }

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: Property) => (
        <div>
          <div className="font-medium">{name}</div>
          <div className="text-sm text-gray-500">{record.address}</div>
        </div>
      )
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => <Tag color="blue">{type}</Tag>
    },
    {
      title: 'Status',
      key: 'status',
      render: (record: Property) => (
        <Tag color={record.isRented ? 'red' : 'green'}>
          {record.isRented ? 'Rented' : 'Available'}
        </Tag>
      )
    },
    {
      title: 'Current Tenant',
      key: 'tenant',
      render: (record: Property) => {
        if (!record.isRented || !record.currentTenant) return '-'
        return (
          <Tooltip title={record.currentTenant.phoneNumber}>{record.currentTenant.name}</Tooltip>
        )
      }
    },
    {
      title: 'Current Rent',
      key: 'rent',
      render: (record: Property) => {
        if (!record.isRented || !record.currentRent) return '-'
        return formatCurrency(record.currentRent, 'PKR')
      }
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (record: Property) => (
        <Space>
          {!record.isRented && (
            <Button type="primary" size="small" onClick={() => onRentClick(record.id)}>
              Rent
            </Button>
          )}
          <Popconfirm
            title="Delete Property"
            description={
              <div className="flex flex-col gap-2">
                <p>Are you sure you want to delete this property?</p>
                <Input
                  placeholder="Enter reason for deletion"
                  value={propertyDeleteReason}
                  onChange={(e) => setPropertyDeleteReason(e.target.value)}
                />
              </div>
            }
            onConfirm={() => {
              if (!propertyDeleteReason.trim()) {
                message.error('Please provide a reason for deletion')
                return
              }
              handleDeleteProperty(record.id)
              setPropertyDeleteReason('')
            }}
            onCancel={() => setPropertyDeleteReason('')}
            okButtonProps={{ disabled: !propertyDeleteReason.trim() }}
          >
            <Button type="text" danger icon={<FiTrash2 />} />
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div className="flex flex-col gap-4">
      <div className="flex gap-4">
        <Input.Search
          placeholder="Search properties"
          allowClear
          className="max-w-xs"
          value={filters.search}
          onChange={(e) => handleFiltersChange('search', e.target.value)}
        />

        <Select
          placeholder="Property Type"
          allowClear
          className="min-w-[150px]"
          value={filters.type}
          onChange={(value) => handleFiltersChange('type', value)}
          options={[
            { value: 'SHOP', label: 'Shop' },
            { value: 'BUILDING', label: 'Building' },
            { value: 'LAND', label: 'Land' },
            { value: 'YARD', label: 'Yard' },
            { value: 'OTHER', label: 'Other' }
          ]}
        />

        <Select
          placeholder="Availability"
          allowClear
          className="min-w-[150px]"
          value={filters.isAvailable}
          onChange={(value) => handleFiltersChange('isAvailable', value)}
          options={[
            { value: true, label: 'Available' },
            { value: false, label: 'Rented' }
          ]}
        />
      </div>

      <Table
        columns={columns}
        dataSource={response?.properties}
        rowKey="id"
        loading={isLoading}
        pagination={{
          current: filters.page,
          pageSize: filters.limit,
          total: response?.pagination.total,
          onChange: (page, pageSize) => {
            setFilters((prev) => ({ ...prev, page, limit: pageSize }))
          }
        }}
      />
    </div>
  )
}
