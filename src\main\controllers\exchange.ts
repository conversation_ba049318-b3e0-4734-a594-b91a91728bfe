import { CreateStructuredExchangeData, IRequest } from '@/common/types';
import { exchangeService } from '../services';
import { TransactionLocation } from '@prisma/client';

class ExchangeController {


    async createStructuredExchange(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const {
            accountId,
            inputCurrency,
            inputAmount,
            inputLocation,
            inputBankId,
            exchangeRate,
            description,
            outputCurrency,
            keepInAccount,
            outputLocation,
            outputBankId,
            userId,
            inputSource,
            allowLoan,
            isReverseRate,
            displayRate
        } = req.body as CreateStructuredExchangeData;

        console.log('create structured exchange req', req.body)

        // Validate required fields
        if (!accountId || !inputCurrency || !inputAmount || !inputSource ||
            !exchangeRate || !outputCurrency || !userId) {
            throw new Error('Missing required fields');
        }

        // Validate conditional fields
        if (inputLocation === 'BANK_ACCOUNT' && !inputBankId) {
            throw new Error('Bank account ID required for bank input location');
        }

        if (inputSource === 'NEW_DEPOSIT' && !inputLocation) {
            throw new Error('Input location required for new deposits');
        }

        if (!keepInAccount) {
            if (!outputLocation) {
                throw new Error('Output location required when not keeping in account');
            }
            if (outputLocation === 'BANK_ACCOUNT' && !outputBankId) {
                throw new Error('Bank account ID required for bank output location');
            }
        }

        return await exchangeService.createStructuredExchange({
            accountId,
            inputCurrency,
            inputAmount: Number(inputAmount),
            inputLocation: inputLocation as TransactionLocation,
            inputBankId,
            exchangeRate: Number(exchangeRate),
            description,
            outputCurrency,
            keepInAccount: Boolean(keepInAccount),
            outputLocation: outputLocation as TransactionLocation,
            outputBankId,
            userId,
            inputSource: inputSource as 'NEW_DEPOSIT' | 'ACCOUNT_BALANCE',
            allowLoan: Boolean(allowLoan),
            isReverseRate: Boolean(isReverseRate),
            displayRate: Number(displayRate)
        });
    }


    async getExchangeById(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        if (!id) throw new Error('Exchange ID is required');
        return await exchangeService.getExchangeById(id);
    }

    async getExchangeHistory(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const {
            page = 1,
            limit = 10,
            startDate,
            endDate,
            fromCurrency,
            toCurrency,
            location,
            sortOrder = 'asc' // Default to oldest first (ascending)
        } = req.query ?? {};

        return await exchangeService.getExchangeHistory({
            page: Number(page),
            limit: Number(limit),
            ...(startDate && { startDate: new Date(startDate) }),
            ...(endDate && { endDate: new Date(endDate) }),
            fromCurrency: fromCurrency as string,
            toCurrency: toCurrency as string,
            location: location as TransactionLocation,
            sortOrder: sortOrder as 'asc' | 'desc'
        });
    }

    async getExchangeStatistics(_event: Electron.IpcMainInvokeEvent) {
        return await exchangeService.getExchangeStatistics();
    }

    async getDailyExchangeSummary(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { date } = req.query ?? {};
        if (!date) throw new Error('Date is required');
        return await exchangeService.getDailyExchangeSummary(new Date(date));
    }

    async getMonthlyExchangeReport(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { month } = req.query ?? {};
        if (!month) throw new Error('Month is required');
        return await exchangeService.getMonthlyExchangeReport(new Date(month));
    }

    async getMostExchangedCurrencies(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { limit = 5 } = req.query ?? {};
        return await exchangeService.getMostExchangedCurrencies(Number(limit));
    }

    // async exchangeBetweenLocations(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
    //     const {
    //         fromAmount,
    //         fromCurrency,
    //         toAmount,
    //         toCurrency,
    //         exchangeRate,
    //         description,
    //         sourceLocation,
    //         destinationLocation,
    //         createdById
    //     } = req.body ?? {};

    //     if (!sourceLocation || !destinationLocation) {
    //         throw new Error('Source and destination locations are required');
    //     }

    //     return await exchangeService.exchangeBetweenLocations({
    //         fromAmount: Number(fromAmount),
    //         fromCurrency,
    //         toAmount: Number(toAmount),
    //         toCurrency,
    //         exchangeRate: Number(exchangeRate),
    //         description,
    //         sourceLocation: sourceLocation as TransactionLocation,
    //         destinationLocation: destinationLocation as TransactionLocation,
    //         createdById
    //     });
    // }

    async getLocationExchangeHistory(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { location } = req.params ?? {};
        if (!location) throw new Error('Location is required');
        return await exchangeService.getLocationExchangeHistory(location as TransactionLocation);
    }

    async getLocationBalance(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { location, currency, bankId } = req.query ?? {};

        if (!location || !currency) {
            throw new Error('Location and currency are required');
        }

        return await exchangeService.getLocationBalance(
            location as TransactionLocation,
            currency as string,
            bankId as string | undefined
        );
    }
}

export const exchangeController = new ExchangeController();