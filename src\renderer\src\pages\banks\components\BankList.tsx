import { Table, Button, Popconfirm, message, Tag, Card, Row, Col } from 'antd'
import { FiTrash2, <PERSON><PERSON><PERSON>, FiPower, FiFileText } from 'react-icons/fi'
import { useState, useEffect } from 'react'
import { useApi } from '@/renderer/hooks'
import { bankAccountApi } from '@/renderer/services'
import { BankDetailsModal } from './BankDetailsModal'
import { formatCurrency } from '@/renderer/utils'
import type { BankAccount } from '@/common/types'
import { useTheme } from '@/renderer/contexts'

interface BankListProps {
  searchQuery: string
  refreshTrigger: number
}

export const BankList = ({ searchQuery, refreshTrigger }: BankListProps) => {
  const [selectedBank, setSelectedBank] = useState<string | null>(null)
  const [totalBalance, setTotalBalance] = useState(0)
  const [activeAccounts, setActiveAccounts] = useState(0)

  const { isDarkMode } = useTheme()

  const {
    data: banks,
    isLoading,
    request: fetchBanks
  } = useApi<BankAccount[], []>(bankAccountApi.getAllBankAccounts)
  const { request: deleteBank } = useApi(bankAccountApi.deleteBankAccount)
  const { request: deactivateBank } = useApi(bankAccountApi.deactivateAccount)

  useEffect(() => {
    fetchBanks()
  }, [refreshTrigger])

  useEffect(() => {
    if (banks) {
      setTotalBalance(banks.reduce((sum: number, bank: BankAccount) => sum + bank.balance, 0))
      setActiveAccounts(banks.filter((bank: BankAccount) => bank.isActive).length)
    }
  }, [banks])

  const handleDelete = async (id: string) => {
    try {
      await deleteBank(id)
      message.success('Bank account deleted successfully')
      fetchBanks()
    } catch (error: any) {
      message.error(error.message)
    }
  }

  const handleDeactivate = async (id: string) => {
    try {
      await deactivateBank(id)
      message.success('Bank account deactivated successfully')
      fetchBanks()
    } catch (error: any) {
      message.error(error.message)
    }
  }

  const filteredBanks = banks?.filter(
    (bank: BankAccount) =>
      bank.bankName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      bank.accountNumber.includes(searchQuery)
  )

  const columns = [
    {
      title: 'Bank Name',
      dataIndex: 'bankName',
      key: 'bankName',
      render: (text: string) => <span className="font-medium">{text}</span>
    },
    {
      title: 'Account Number',
      dataIndex: 'accountNumber',
      key: 'accountNumber'
    },
    {
      title: 'Branch Code',
      dataIndex: 'branchCode',
      key: 'branchCode'
    },
    {
      title: 'Balance',
      dataIndex: 'balance',
      key: 'balance',
      render: (balance: number) => (
        <span className={`font-medium ${balance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
          {formatCurrency(balance, 'PKR')}
        </span>
      )
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>{isActive ? 'Active' : 'Inactive'}</Tag>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: BankAccount) => (
        <div className="flex gap-2">
          <Button
            type="text"
            icon={<FiEye />}
            onClick={() => setSelectedBank(record.id)}
            title="View Details"
          />

          {record.isActive && (
            <>
              <Popconfirm
                title="Deactivate Account"
                description="Are you sure you want to deactivate this account?"
                onConfirm={() => handleDeactivate(record.id)}
              >
                <Button
                  type="text"
                  icon={<FiPower />}
                  className="text-orange-500"
                  title="Deactivate Account"
                />
              </Popconfirm>
              <Popconfirm
                title="Delete Account"
                description="Are you sure you want to delete this account?"
                onConfirm={() => handleDelete(record.id)}
              >
                <Button type="text" danger icon={<FiTrash2 />} title="Delete Account" />
              </Popconfirm>
            </>
          )}
        </div>
      )
    }
  ]

  return (
    <>
      <Row gutter={16} className="mb-6">
        <Col span={12}>
          <Card
            className={`shadow-large bg-[length:200%_200%] bg-[10%_10%] transition-[background-position] duration-500 ease-in-out hover:bg-[90%_90%] ${
              isDarkMode
                ? 'bg-gradient-to-br from-blue-950 via-slate-950 to-blue-900'
                : 'bg-gradient-to-br from-sky-200 via-white to-blue-200'
            }`}
          >
            <div className="text-center">
              <p className="text-sm">Total Balance</p>
              <p
                className={`text-2xl font-semibold ${totalBalance >= 0 ? 'text-green-600' : 'text-red-600'}`}
              >
                {formatCurrency(totalBalance, 'PKR')}
              </p>
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card
            className={`shadow-large bg-[length:200%_200%] bg-[10%_10%] transition-[background-position] duration-500 ease-in-out hover:bg-[90%_90%] ${
              isDarkMode
                ? 'bg-gradient-to-br from-blue-950 via-slate-950 to-blue-900'
                : 'bg-gradient-to-br from-sky-200 via-white to-blue-200'
            }`}
          >
            <div className="text-center">
              <p className="text-sm">Active Accounts</p>
              <p className="text-2xl font-semibold text-blue-600">{activeAccounts}</p>
            </div>
          </Card>
        </Col>
      </Row>

      <Table
        className="ant-table-compact"
        columns={columns}
        dataSource={filteredBanks}
        loading={isLoading}
        rowKey="id"
        size="small"
        virtual
        sticky
        pagination={false}
      />

      <BankDetailsModal bankId={selectedBank} onClose={() => setSelectedBank(null)} />
    </>
  )
}
