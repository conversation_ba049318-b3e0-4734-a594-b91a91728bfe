import { Card, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Col, Spin, message } from 'antd'
import { useState, useEffect } from 'react'
import { useApi } from '@/renderer/hooks'
import { cashVaultApi } from '@/renderer/services'
import type { BalanceReconciliation } from '@/common/types'
import { formatCurrency } from '@/renderer/utils'

export const ReconciliationView = () => {
  const [selectedCurrency, setSelectedCurrency] = useState('PKR')
  const [reconciliationData, setReconciliationData] = useState<BalanceReconciliation | null>(null)

  const {
    data: reconciliation,
    isLoading,
    request: reconcileBalance,
    error,
    errorMessage,
    clearData
  } = useApi<BalanceReconciliation, [string]>(cashVaultApi.reconcileBalance)

  useEffect(() => {
    if (reconciliation) {
      setReconciliationData(reconciliation)
    }
  }, [reconciliation])

  const handleReconcile = async () => {
    await reconcileBalance(selectedCurrency)
  }

  const handleCurrencyChange = (value: string) => {
    setSelectedCurrency(value)
    // Clear reconciliation data when currency changes
    setReconciliationData(null)
    clearData()
  }

  useEffect(() => {
    if (error) {
      message.error(errorMessage)
      return
    }
    if (reconciliation) {
      message.success('Balance reconciliation completed')
    }
  }, [reconciliation, error, errorMessage])

  // Helper function to check if a number is effectively zero (handling floating point precision)
  const isEffectivelyZero = (num: number): boolean => {
    return Math.abs(num) < 0.000001 // Consider values less than 0.000001 as zero
  }

  if (isLoading) return <Spin />

  return (
    <Card className="rounded-lg p-6">
      <div className="mb-6 flex gap-4">
        {/* <Select value={selectedSource} onChange={setSelectedSource} style={{ width: 200 }}>
          <Select.Option value="CASH_VAULT">Cash Vault</Select.Option>
        </Select> */}

        <Select value={selectedCurrency} onChange={handleCurrencyChange} style={{ width: 200 }}>
          <Select.Option value="PKR">PKR</Select.Option>
          <Select.Option value="USD">USD</Select.Option>
          <Select.Option value="AED">AED</Select.Option>
          <Select.Option value="AFN">AFN</Select.Option>
        </Select>

        <Button type="primary" onClick={handleReconcile}>
          Reconcile Balance
        </Button>
      </div>

      {reconciliationData && (
        <Row gutter={16}>
          <Col span={8}>
            <Card>
              <Statistic
                title="Current Balance"
                value={reconciliationData.currentBalance}
                precision={2}
                formatter={(value) => formatCurrency(value as number, selectedCurrency)}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title="Calculated Balance"
                value={reconciliationData.calculatedBalance}
                precision={2}
                formatter={(value) => formatCurrency(value as number, selectedCurrency)}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title="Difference"
                value={reconciliationData.difference}
                precision={2}
                valueStyle={{
                  color: isEffectivelyZero(reconciliationData.difference) ? 'green' : 'red'
                }}
                formatter={(value) => formatCurrency(value as number, selectedCurrency)}
              />
            </Card>
          </Col>
        </Row>
      )}
    </Card>
  )
}
