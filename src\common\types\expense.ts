import { Expense, TransactionLocation } from "@prisma/client";

export interface CreateExpenseData {
    date: Date;
    amount: number;
    category: string;
    description: string;
    paymentSource: TransactionLocation; // SMALL_COUNTER, CASH_VAULT, or BANK_ACCOUNT
    bankAccountId?: string; // Required if paymentSource is BANK_ACCOUNT
    userId: string;
}

export interface GetExpensesParams {
    page?: number;
    pageSize?: number;
    category?: string;
    startDate?: Date;
    endDate?: Date;
    includeDeleted?: boolean;
    orderBy?: 'asc' | 'desc';
}

export interface DeleteExpenseData {
    id: string;
    reason: string;
    userId: string;
}

interface Pagination {
    page: number;
    pageSize: number;
    total: number;
}

export interface GetExpensesResponse {
    expenses: Expense[];
    pagination: Pagination;
}

