import { useState, useEffect } from 'react'
import { Card, Form, Select, InputNumber, DatePicker, Input, Button, message } from 'antd'
import type { TransferBetweenLocationsData } from '@/common/types'
import { paymentApi } from '@/renderer/services'
import { useBankContext } from '@/renderer/contexts'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { handleDatePickerValue } from '@/renderer/utils'
import { LocationDetailsCard } from '@/renderer/components'

export const TransferFunds = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [fromLocation, setFromLocation] = useState<'CASH_VAULT' | 'BANK_ACCOUNT'>('CASH_VAULT')
  const [toLocation, setToLocation] = useState<'CASH_VAULT' | 'BANK_ACCOUNT'>('BANK_ACCOUNT')
  const [fromBankId, setFromBankId] = useState<string | undefined>(undefined)
  const [toBankId, setToBankId] = useState<string | undefined>(undefined)
  const [selectedCurrency, setSelectedCurrency] = useState<string | undefined>(undefined)

  const user = useSelector((state: IRootState) => state.user.data)

  const CURRENCIES = ['PKR', 'USD', 'AED', 'AFN']

  const { banks } = useBankContext()

  // Ensure form and state are synchronized on initial load
  useEffect(() => {
    form.setFieldsValue({
      fromLocation: 'CASH_VAULT',
      toLocation: 'BANK_ACCOUNT',
      currencyCode: 'PKR'
    })
    setSelectedCurrency('PKR')
  }, [])

  const handleSubmit = async (values: any) => {
    console.log(values)
    if (
      fromLocation === toLocation &&
      (fromLocation !== 'BANK_ACCOUNT' || toLocation !== 'BANK_ACCOUNT')
    ) {
      message.error('Source and destination locations cannot be the same')
      return
    }

    setLoading(true)
    const data: TransferBetweenLocationsData = {
      ...values,
      fromLocation,
      toLocation,
      date: handleDatePickerValue(values.date?.toDate()),
      userId: user?.id || ''
    }

    const response = await paymentApi.transferBetweenLocations(data)
    setLoading(false)

    console.log('transfer response', response)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    message.success('Transfer completed successfully')
    form.resetFields()
    // Reset to initial state after form reset
    setFromLocation('CASH_VAULT')
    setToLocation('BANK_ACCOUNT')
    setFromBankId(undefined)
    setToBankId(undefined)
    setSelectedCurrency(undefined)
  }

  const handleFromBankChange = (value: string) => {
    setFromBankId(value)
  }

  const handleToBankChange = (value: string) => {
    setToBankId(value)
  }

  const handleCurrencyChange = (value: string) => {
    setSelectedCurrency(value)
  }

  return (
    <div className="flex w-full flex-col gap-4 md:flex-row">
      {/* Left side - Form */}
      <div className="flex-1">
        <Card className="mt-4">
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
              fromLocation: 'CASH_VAULT',
              toLocation: 'BANK_ACCOUNT'
            }}
          >
            <div className="grid grid-cols-2 gap-4">
              <Form.Item label="From Location" name="fromLocation" rules={[{ required: true }]}>
                <Select
                  value={fromLocation}
                  onChange={setFromLocation}
                  options={[
                    { label: 'Cash Vault', value: 'CASH_VAULT' },
                    { label: 'Bank Account', value: 'BANK_ACCOUNT' }
                  ]}
                />
              </Form.Item>

              <Form.Item label="To Location" name="toLocation" rules={[{ required: true }]}>
                <Select
                  value={toLocation}
                  onChange={setToLocation}
                  options={[
                    { label: 'Cash Vault', value: 'CASH_VAULT' },
                    { label: 'Bank Account', value: 'BANK_ACCOUNT' }
                  ]}
                />
              </Form.Item>

              {fromLocation === 'BANK_ACCOUNT' && (
                <Form.Item label="From Bank Account" name="fromBankId" rules={[{ required: true }]}>
                  <Select
                    showSearch
                    placeholder="Select bank account"
                    optionFilterProp="children"
                    options={banks}
                    onChange={handleFromBankChange}
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                  />
                </Form.Item>
              )}

              {toLocation === 'BANK_ACCOUNT' && (
                <Form.Item label="To Bank Account" name="toBankId" rules={[{ required: true }]}>
                  <Select
                    showSearch
                    placeholder="Select bank account"
                    optionFilterProp="children"
                    options={banks}
                    onChange={handleToBankChange}
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                  />
                </Form.Item>
              )}

              <Form.Item label="Amount" name="amount" rules={[{ required: true }]}>
                <InputNumber
                  className="w-full"
                  min={Number(1)}
                  precision={2}
                  placeholder="Enter amount"
                  formatter={(value) => `Rs ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => Number(value!.replace(/Rs\s?|(,*)/g, ''))}
                />
              </Form.Item>

              <Form.Item label="Currency" name="currencyCode" rules={[{ required: true }]}>
                <Select
                  showSearch
                  placeholder="Select currency"
                  optionFilterProp="children"
                  options={CURRENCIES.map((currency) => ({ label: currency, value: currency }))}
                  onChange={handleCurrencyChange}
                />
              </Form.Item>

              <Form.Item
                label="Date"
                name="date"
                className="col-span-2"
                rules={[{ required: true }]}
              >
                <DatePicker className="w-full" />
              </Form.Item>

              <Form.Item label="Description" name="description" className="col-span-2">
                <Input.TextArea rows={3} placeholder="Enter transfer description" />
              </Form.Item>
            </div>

            <Form.Item className="mb-0">
              <Button type="primary" htmlType="submit" loading={loading}>
                Transfer Funds
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>

      {/* Right side - Location Details Cards */}
      <div className="flex flex-1 flex-col gap-4">
        {/* Source Location Card */}
        {fromLocation === 'CASH_VAULT' && (
          <LocationDetailsCard locationType="CASH_VAULT" currencyCode={selectedCurrency} />
        )}
        {fromLocation === 'BANK_ACCOUNT' && fromBankId && (
          <LocationDetailsCard locationType="BANK_ACCOUNT" bankId={fromBankId} />
        )}

        {/* Destination Location Card */}
        {toLocation === 'CASH_VAULT' && (
          <LocationDetailsCard locationType="CASH_VAULT" currencyCode={selectedCurrency} />
        )}
        {toLocation === 'BANK_ACCOUNT' && toBankId && (
          <LocationDetailsCard locationType="BANK_ACCOUNT" bankId={toBankId} />
        )}
      </div>
    </div>
  )
}
