import { Card } from 'antd'
import { useTheme } from '@/renderer/contexts'
import {
  CashOverview,
  BankAccountsSummary,
  TransactionStats,
  AccountStats,
  InventorySummary,
  SalesTrends,
  CurrencyTransactions,
  LocationTransfers,
  AccountBalances
} from './components'

const Dashboard = () => {
  const { isDarkMode } = useTheme()

  return (
    <div className="m-6 grid grid-cols-1 gap-6 lg:grid-cols-1 xl:grid-cols-3">
      {/* Cash & Bank Overview Section */}
      <Card
        className={`col-span-1 bg-[length:200%_200%] bg-[center] shadow-lg ${
          isDarkMode
            ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
            : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
        }`}
      >
        <div className="grid gap-6 md:grid-cols-1">
          {/* <CashOverview /> */}
          <BankAccountsSummary />
        </div>
      </Card>

      {/* Transaction & Account Stats */}
      <Card
        className={`col-span-1 bg-[length:200%_200%] bg-[center] shadow-lg xl:col-span-2 ${
          isDarkMode
            ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
            : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
        }`}
      >
        <div className="grid gap-6 md:grid-cols-2">
          <TransactionStats />
          <AccountStats />
        </div>
      </Card>

      {/* Inventory & Sales Section */}
      <Card
        className={`col-span-1 bg-[length:200%_200%] bg-[center] shadow-lg xl:col-span-3 ${
          isDarkMode
            ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
            : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
        }`}
      >
        <div className="grid gap-6 md:grid-cols-2">
          <InventorySummary />
          <SalesTrends />
        </div>
      </Card>

      {/* Currency & Transfers Section */}
      <Card
        className={`col-span-1 bg-[length:200%_200%] bg-[center] shadow-lg xl:col-span-3 ${
          isDarkMode
            ? 'bg-gradient-to-bl from-indigo-950 via-black to-indigo-900 shadow-indigo-950'
            : 'bg-gradient-to-bl from-indigo-200 via-white to-indigo-100 shadow-indigo-200'
        }`}
      >
        <div className="grid gap-6 lg:grid-cols-1 xl:grid-cols-2">
          <CurrencyTransactions />
          <LocationTransfers />
          {/* <AccountBalances /> */}
        </div>
      </Card>
    </div>
  )
}

export default Dashboard
