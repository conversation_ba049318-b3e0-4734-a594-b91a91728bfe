import { CreateAccountData, IRequest } from '@/common/types';
import { accountService } from '../services';

class AccountController {
    async createAccount(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { name, phoneNumber, address, type, openingBalances, userId } = req.body as CreateAccountData;

        if (!name || !phoneNumber || !address || !type || !userId) {
            throw new Error('Name, phone number, address, type, and user id are required');
        }

        if (!['CUSTOMER', 'TENANT', 'BOTH'].includes(type)) {
            throw new Error('Invalid account type');
        }

        return await accountService.createAccount({
            ...req.body as CreateAccountData,

        });
    }


    async deleteAccount(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};

        if (!id) {
            throw new Error('Account ID is required');
        }

        return await accountService.deleteAccount(id);
    }

    async getAccountById(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};

        if (!id) {
            throw new Error('Account ID is required');
        }

        return await accountService.getAccountById(id);
    }

    async updateAccount(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { id } = req.params ?? {};
        const { phoneNumber, address } = req.body ?? {};

        if (!id) {
            throw new Error('Account ID is required');
        }

        if (!phoneNumber && !address) {
            throw new Error('At least one field (phone number or address) is required');
        }

        return await accountService.updateAccount(id, { phoneNumber, address });
    }

    async getAccounts(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { page = 1, limit = 10, type, search, includeDeleted } = req.query ?? {};

        return await accountService.getAccounts({
            page: Number(page),
            limit: Number(limit),
            type: type as any,
            search: search as string,
            includeDeleted: Boolean(includeDeleted)
        });
    }

    async getAccountsForSelect(_event: Electron.IpcMainInvokeEvent) {
        return await accountService.getAccountsForSelect();
    }

    async getCustomerAccountsForSelect(_event: Electron.IpcMainInvokeEvent) {
        return await accountService.getCustomerAccountsForSelect();
    }

    async getTenantAccountsForSelect(_event: Electron.IpcMainInvokeEvent) {
        return await accountService.getTenantAccountsForSelect();
    }

    async getAccountStatement(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { accountId } = req.params ?? {};
        const { startDate, endDate } = req.query ?? {};

        if (!accountId) throw new Error('Account ID is required');
        if (!startDate || !endDate) throw new Error('Start and end dates are required');

        return await accountService.getAccountStatement(
            accountId,
            new Date(startDate),
            new Date(endDate)
        );
    }

    async getBalanceSummary(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { accountId } = req.params ?? {};

        if (!accountId) throw new Error('Account ID is required');

        return await accountService.getBalanceSummary(accountId);
    }

    async getAccountStatistics(_event: Electron.IpcMainInvokeEvent, req: IRequest) {
        const { accountId } = req.params ?? {};

        if (!accountId) throw new Error('Account ID is required');

        return await accountService.getAccountStatistics(accountId);
    }

}

export const accountController = new AccountController();