import { ItemStatus, Prisma, TransactionLocation } from "@prisma/client";

import { ItemType } from "@prisma/client";
import { prisma } from "../db";
import { CreateWalkInSaleData, CreateAccountSaleData, GetSalesParams, GetContainerByStockIdParams } from "@/common/types";

class SaleService {
    async createWalkInSale(data: CreateWalkInSaleData) {
        return await prisma.$transaction(async (tx) => {
            // Validate quantity for non-car items
            if (data.itemType !== 'CAR') {
                const item = await this.getItemByType(tx, data.itemType, data.itemId);
                if (!item) throw new Error('Item not found');

                if (item.quantity < data.quantity) {
                    throw new Error(`Insufficient quantity available. Available: ${item.quantity}`);
                }
            }

            // Create the sale
            const sale = await tx.sale.create({
                data: {
                    itemType: data.itemType,
                    totalAmount: data.totalAmount,
                    quantity: data.quantity,
                    ...(data.itemType === 'CAR' && { car: { connect: { id: data.itemId } } }),
                    ...(data.itemType === 'CARPART' && { carPart: { connect: { id: data.itemId } } }),
                    ...(data.itemType === 'ELECTRONIC' && { electronic: { connect: { id: data.itemId } } }),
                    ...(data.itemType === 'SCRAP' && { scrap: { connect: { id: data.itemId } } }),
                    date: data.date,
                    paymentLocation: data.paymentDestination,
                    ...(data.bankAccountId && data.paymentDestination === 'BANK_ACCOUNT' && {
                        bankAccount: { connect: { id: data.bankAccountId } }
                    })
                }
            });

            // Update item quantity and status
            if (data.itemType === 'CAR') {
                await this.updateItemStatus(tx, data.itemType, data.itemId, 'SOLD');
            } else {
                await this.updateItemQuantity(tx, data.itemType, data.itemId, data.quantity);
            }

            // Get item details for description
            const item = await this.getItemByType(tx, data.itemType, data.itemId);
            const itemName = this.getItemName(item);

            await tx.ledgerEntry.create({
                data: {
                    amount: data.totalAmount,
                    type: 'DEBIT',
                    description: data.itemType === 'CAR' ?
                        `Walk-in sale of ${data.itemType} => ${itemName} (Model: ${item.modelNumber}) (Color: ${item.color}) (Chasis: ${item.chassisNumber})`
                        : `Walk-in sale of ${data.itemType} => ${itemName} (Qty: ${data.quantity})`,
                    sourceType: 'OTHER',
                    destinationType: "EXTERNAL",
                    transactionType: 'SALE',
                    currency: { connect: { code: 'PKR' } },
                    sale: { connect: { id: sale.id } },
                    createdBy: { connect: { id: data.userId } },
                    date: data.date,
                    // ...(data.bankAccountId && data.paymentDestination === 'BANK_ACCOUNT' && {
                    //     bankAccount: { connect: { id: data.bankAccountId } }
                    // })
                }
            });

            // Create payment for the sale
            await tx.payment.create({
                data: {
                    amount: data.totalAmount,
                    paymentType: 'RECEIVED',
                    description: data.itemType === 'CAR' ?
                        `Payment for, Walk-in sale of ${data.itemType} => ${itemName} (Model: ${item.modelNumber}) (Color: ${item.color}) (Chasis: ${item.chassisNumber})`
                        : `Payment for, Walk-in sale of ${data.itemType} => ${itemName} (Qty: ${data.quantity})`,
                    saleId: sale.id,
                    currencyId: await this.getPkrCurrencyId(),
                    date: data.date,
                }
            });

            // Create ledger entry for the payment
            await tx.ledgerEntry.create({
                data: {
                    amount: data.totalAmount,
                    type: 'CREDIT',
                    description: `Payment for walk-in sale of ${data.itemType} => ${itemName} (Qty: ${data.quantity})`,
                    sourceType: 'OTHER',
                    destinationType: data.paymentDestination,
                    transactionType: 'PAYMENT',
                    currency: { connect: { code: 'PKR' } },
                    sale: { connect: { id: sale.id } },
                    createdBy: { connect: { id: data.userId } },
                    date: data.date,
                    ...(data.bankAccountId && data.paymentDestination === 'BANK_ACCOUNT' && {
                        bankAccount: { connect: { id: data.bankAccountId } }
                    })
                }
            });

            // Update destination balance
            await this.updateDestinationBalance(
                tx,
                data.paymentDestination,
                data.totalAmount,
                data.bankAccountId
            );

            return sale;
        });
    }

    async createAccountSale(data: CreateAccountSaleData) {
        return await prisma.$transaction(async (tx) => {
            // Validate quantity for non-car items
            if (data.itemType !== 'CAR') {
                const item = await this.getItemByType(tx, data.itemType, data.itemId);
                if (!item) throw new Error('Item not found');

                if (item.quantity < data.quantity) {
                    throw new Error(`Insufficient quantity available. Available: ${item.quantity}`);
                }
            }

            // Create the sale
            const sale = await tx.sale.create({
                data: {
                    itemType: data.itemType,
                    totalAmount: data.totalAmount,
                    quantity: data.quantity,
                    account: { connect: { id: data.accountId } },
                    ...(data.itemType === 'CAR' && { car: { connect: { id: data.itemId } } }),
                    ...(data.itemType === 'CARPART' && { carPart: { connect: { id: data.itemId } } }),
                    ...(data.itemType === 'ELECTRONIC' && { electronic: { connect: { id: data.itemId } } }),
                    ...(data.itemType === 'SCRAP' && { scrap: { connect: { id: data.itemId } } }),
                    date: data.date
                }
            });

            // Update item quantity and status
            if (data.itemType === 'CAR') {
                await this.updateItemStatus(tx, data.itemType, data.itemId, 'SOLD');
            } else {
                await this.updateItemQuantity(tx, data.itemType, data.itemId, data.quantity);
            }

            // Get item details for description
            const item = await this.getItemByType(tx, data.itemType, data.itemId);
            const itemName = this.getItemName(item);

            const account = await tx.account.findUnique({ where: { id: data.accountId }, select: { name: true } });

            // Create ledger entry for the sale
            await tx.ledgerEntry.create({
                data: {
                    amount: data.totalAmount,
                    type: 'DEBIT', // Account owes us money
                    description: data.itemType === 'CAR' ?
                        `Sale of ${data.itemType} => ${itemName} (Model: ${item.modelNumber}) (Color: ${item.color}) (Chasis: ${item.chassisNumber}) to account ${account?.name}`
                        : `Sale of ${data.itemType} => ${itemName} (Qty: ${data.quantity}) to account ${account?.name}`,
                    sourceType: 'ACCOUNT',
                    destinationType: 'OTHER',
                    transactionType: 'SALE',
                    currency: { connect: { code: 'PKR' } },
                    account: { connect: { id: data.accountId } },
                    sale: { connect: { id: sale.id } },
                    createdBy: { connect: { id: data.userId } },
                    date: data.date
                }
            });

            // Update account's PKR balance
            await tx.accountBalance.update({
                where: {
                    accountId_currencyId: {
                        accountId: data.accountId,
                        currencyId: await this.getPkrCurrencyId()
                    }
                },
                data: {
                    balance: { decrement: data.totalAmount }
                }
            });

            return sale;
        });
    }

    private async getItemByType(tx: Prisma.TransactionClient, itemType: ItemType, itemId: string) {
        const modelMapping = {
            CAR: 'car',
            CARPART: 'carPart',
            ELECTRONIC: 'electronic',
            SCRAP: 'scrap'
        };

        const model = modelMapping[itemType];
        if (!model) throw new Error('Invalid item type');

        return await tx[model].findUnique({ where: { id: itemId } });
    }

    private getItemName(item: any): string {
        if (!item) return 'Unknown';
        return item.name || item.description || 'Unknown';
    }

    private async updateItemQuantity(
        tx: Prisma.TransactionClient,
        itemType: ItemType,
        itemId: string,
        soldQuantity: number
    ) {
        const modelMapping = {
            CARPART: 'carPart',
            ELECTRONIC: 'electronic',
            SCRAP: 'scrap'
        } as const;

        const model = modelMapping[itemType as keyof typeof modelMapping];
        if (!model) throw new Error('Invalid item type');

        const item = await (tx as any)[model].findUnique({ where: { id: itemId } });
        if (!item) throw new Error('Item not found');

        const remainingQuantity = item.quantity - soldQuantity;

        await (tx as any)[model].update({
            where: { id: itemId },
            data: {
                quantity: remainingQuantity,
                status: remainingQuantity > 0 ? 'AVAILABLE' : 'SOLD'
            }
        });
    }

    private async getPkrCurrencyId() {
        const currency = await prisma.currency.findFirst({ where: { code: 'PKR' } });
        if (!currency) throw new Error('PKR currency not found');
        return currency.id;
    }

    private async updateDestinationBalance(
        tx: Prisma.TransactionClient,
        destination: TransactionLocation,
        amount: number,
        bankAccountId?: string
    ) {
        switch (destination) {
            case 'SMALL_COUNTER':
                const smallCounter = await tx.smallCounter.findFirst();
                if (!smallCounter) throw new Error('Small counter not initialized');
                await tx.smallCounter.update({
                    where: { id: smallCounter.id },
                    data: { pkrBalance: { increment: amount } }
                });
                break;

            case 'CASH_VAULT':
                const cashVault = await tx.cashVault.findFirst();
                if (!cashVault) throw new Error('Cash vault not initialized');
                await tx.cashVault.update({
                    where: { id: cashVault.id },
                    data: { pkrBalance: { increment: amount } }
                });
                break;

            case 'BANK_ACCOUNT':
                if (!bankAccountId) throw new Error('Bank account ID required');
                await tx.bankAccount.update({
                    where: { id: bankAccountId },
                    data: { balance: { increment: amount } }
                });
                break;
        }
    }

    private async updateItemStatus(
        tx: Prisma.TransactionClient,
        itemType: ItemType,
        itemId: string,
        status: ItemStatus
    ) {
        const modelMapping = {
            CAR: 'car',
            CARPART: 'carPart',
            ELECTRONIC: 'electronic',
            SCRAP: 'scrap'
        }

        const model = modelMapping[itemType]
        if (!model) throw new Error('Invalid item type')

        await tx[model].update({
            where: { id: itemId },
            data: { status }
        });
    }


    async getSales({
        page = 1,
        pageSize = 20,
        accountId,
        includeDeleted = false,
        startDate,
        endDate,
        sortOrder = 'desc' // Default to newest first
    }: GetSalesParams) {
        const where: Prisma.SaleWhereInput = {
            ...(accountId && { accountId }),
            ...(startDate && endDate && {
                date: { gte: startDate, lte: endDate }
            }),
            ...(!includeDeleted && { isDeleted: false })
        };

        const [sales, total] = await Promise.all([
            prisma.sale.findMany({
                where,
                include: {
                    account: true,
                    payments: true,
                    car: true,
                    carPart: true,
                    electronic: true,
                    scrap: true,
                    bankAccount: true
                },
                orderBy: { date: sortOrder }, // Use the sortOrder parameter
                skip: (page - 1) * pageSize,
                take: pageSize
            }),
            prisma.sale.count({ where })
        ]);

        return {
            sales,
            pagination: {
                total,
                pages: Math.ceil(total / pageSize),
                currentPage: page,
                pageSize
            }
        };
    }

    async deleteSale(id: string, reason: string, userId: string) {
        return await prisma.$transaction(async (tx) => {
            // Get sale with its relations
            const sale = await tx.sale.findUnique({
                where: { id },
                include: {
                    account: true,
                    car: true,
                    carPart: true,
                    electronic: true,
                    scrap: true,
                    bankAccount: true,
                    LedgerEntry: {
                        include: {
                            payment: true
                        }
                    },
                    payments: true
                }
            });

            if (!sale) throw new Error('Sale not found');

            // Map itemType to correct model name
            const modelMapping = {
                CAR: 'car',
                CARPART: 'carPart',
                ELECTRONIC: 'electronic',
                SCRAP: 'scrap'
            };

            const itemModel = modelMapping[sale.itemType];
            if (!itemModel) {
                throw new Error('Invalid item type');
            }

            // Update item status back to AVAILABLE
            await tx[itemModel].update({
                where: { id: sale[`${itemModel}Id`] },
                data: { status: 'AVAILABLE' }
            });

            // If it was an account sale, reverse the account balance
            if (sale.account) {
                await tx.accountBalance.update({
                    where: {
                        accountId_currencyId: {
                            accountId: sale.account.id,
                            currencyId: await this.getPkrCurrencyId()
                        }
                    },
                    data: {
                        balance: { increment: sale.totalAmount } // Reverse the debit
                    }
                });
            } else {
                // For walk-in sale, find the payment ledger entry
                const paymentEntry = sale.LedgerEntry.find(entry =>
                    entry.transactionType === 'PAYMENT' &&
                    entry.type === 'CREDIT'
                );

                if (paymentEntry) {
                    // Reverse the destination balance
                    await this.updateDestinationBalance(
                        tx,
                        paymentEntry.destinationType as TransactionLocation,
                        -sale.totalAmount,
                        paymentEntry.bankAccountId ?? undefined
                    );
                }
            }

            // Soft delete the payments
            await tx.payment.updateMany({
                where: { saleId: id },
                data: {
                    isDeleted: true,
                    deleteReason: reason,
                    deletedAt: new Date()
                }
            });

            // Soft delete the ledger entries and their associated payments
            await Promise.all([
                tx.ledgerEntry.updateMany({
                    where: { saleId: id },
                    data: {
                        isDeleted: true,
                        deleteReason: reason,
                        deletedAt: new Date(),
                        deletedById: userId
                    }
                }),
                tx.sale.update({
                    where: { id },
                    data: {
                        isDeleted: true,
                        deleteReason: reason,
                        deletedAt: new Date()
                    }
                })
            ]);

            return sale;
        });
    }


    async getAvailableItems(itemType: ItemType) {
        const modelMapping = {
            CAR: 'car',
            CARPART: 'carPart',
            ELECTRONIC: 'electronic',
            SCRAP: 'scrap'
        }

        const baseSelect = {
            id: true,
            status: true
        }

        const selectByType = {
            CAR: {
                ...baseSelect,
                modelNumber: true,
                chassisNumber: true,
                name: true,
                color: true
            },
            CARPART: {
                ...baseSelect,
                name: true,
                quantity: true,
                // description: true
            },
            ELECTRONIC: {
                ...baseSelect,
                name: true,
                quantity: true,
                // description: true
            },
            SCRAP: {
                ...baseSelect,
                description: true,
                quantity: true
            }
        }

        const model = modelMapping[itemType]
        if (!model) throw new Error('Invalid item type')

        const items = await prisma[model].findMany({
            where: {
                status: 'AVAILABLE',
                isDeleted: false
            },
            select: selectByType[itemType]
        });

        // return an array for select options

        let options = []

        if (itemType === 'CAR') {
            options = items.map(item => ({
                label: `${item.name} - ${item.modelNumber} - ${item.chassisNumber} - ${item.color}`,
                value: item.id
            }))
        } else if (itemType === 'CARPART') {
            options = items.map(item => ({
                label: `${item.name} - ${item.quantity}`,
                value: item.id
            }))
        } else if (itemType === 'ELECTRONIC') {
            options = items.map(item => ({
                label: `${item.name} - ${item.quantity}`,
                value: item.id
            }))
        } else if (itemType === 'SCRAP') {
            options = items.map(item => ({
                label: `${item.description} - ${item.quantity}`,
                value: item.id
            }))
        }


        return { options };
    }

    async getSaleById(id: string) {
        const sale = await prisma.sale.findUnique({
            where: { id },
            include: {
                account: true,
                car: true,
                carPart: true,
                electronic: true,
                scrap: true,
                payments: true,
                bankAccount: true,
                // createdBy: {
                //     select: {
                //         id: true,
                //         name: true
                //     }
                // },
                // deletedBy: {
                //     select: {
                //         id: true,
                //         name: true
                //     }
                // }
            }
        });

        if (!sale) throw new Error('Sale not found');
        return sale;
    }

    async getContainerByStockId({ itemId, itemType }: GetContainerByStockIdParams) {
        const modelMapping = {
            CAR: 'car',
            CARPART: 'carPart',
            ELECTRONIC: 'electronic',
            SCRAP: 'scrap'
        };

        const model = modelMapping[itemType];
        if (!model) {
            throw new Error('Invalid item type');
        }

        const item = await prisma[model].findUnique({
            where: { id: itemId },
            select: {
                container: {
                    select: {
                        id: true,
                        containerNumber: true,
                        containerCost: true,
                        driverExpense: true,
                        fieldRent: true,
                        openedAt: true,
                        routeExpense: true,
                        taxes: true
                    }
                }
            }
        });

        if (!item) {
            throw new Error(`${itemType} not found`);
        }

        return {
            container: item.container
        }

    }
}


export const saleService = new SaleService();