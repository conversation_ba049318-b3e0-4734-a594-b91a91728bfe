import { useEffect, useState } from 'react'
import { App, Card, DatePicker, Table, Tag } from 'antd'
import { ArrowDownOutlined, ArrowUpOutlined } from '@ant-design/icons'
import { exchangeApi } from '@/renderer/services'
import type { DailyExchangeSummary as DailySummaryType } from '@/common/types'
import dayjs from 'dayjs'

export const DailyExchangeSummary = () => {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<DailySummaryType>({
    date: new Date(),
    exchanges: [],
    totalTransactions: 0
  })
  const [selectedDate, setSelectedDate] = useState(dayjs())

  const { message } = App.useApp()

  const fetchSummary = async (date: Date) => {
    setLoading(true)
    const response = await exchangeApi.getDailyExchangeSummary(date)
    setLoading(false)
    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }
    console.log(response)

    setData(response.data.data)
  }

  useEffect(() => {
    fetchSummary(selectedDate.toDate())
  }, [selectedDate])

  const columns = [
    {
      title: 'Currency',
      dataIndex: 'currency',
      key: 'currency',
      render: (currency: string) => <Tag color="blue">{currency}</Tag>
    },
    {
      title: 'Inflow',
      dataIndex: 'inflow',
      key: 'inflow',
      render: (inflow: number, record: any) => (
        <span className="flex items-center gap-2 font-medium text-green-600">
          <ArrowUpOutlined />
          {inflow.toLocaleString()} {record.currency}
        </span>
      )
    },
    {
      title: 'Outflow',
      dataIndex: 'outflow',
      key: 'outflow',
      render: (outflow: number, record: any) => (
        <span className="flex items-center gap-2 font-medium text-red-600">
          <ArrowDownOutlined />
          {outflow.toLocaleString()} {record.currency}
        </span>
      )
    },
    {
      title: 'Net Flow',
      key: 'netFlow',
      render: (record: any) => {
        const netFlow = record.inflow - record.outflow
        const isPositive = netFlow > 0
        return (
          <span
            className={`flex items-center gap-2 font-medium ${
              isPositive ? 'text-green-600' : 'text-red-600'
            }`}
          >
            {isPositive ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
            {Math.abs(netFlow).toLocaleString()} {record.currency}
          </span>
        )
      }
    },
    {
      title: 'Transactions',
      dataIndex: 'transactions',
      key: 'transactions'
    }
  ]

  return (
    <Card
      title="Daily Exchange Summary"
      extra={
        <DatePicker
          value={selectedDate}
          onChange={(date) => date && setSelectedDate(date)}
          allowClear={false}
          disabledDate={(date) => date.isAfter(dayjs())}
        />
      }
    >
      <div className="mb-4 text-gray-600">
        Total Transactions: <span className="font-medium">{data.totalTransactions}</span>
      </div>

      <Table
        columns={columns}
        dataSource={data.exchanges}
        rowKey="currency"
        loading={loading}
        pagination={false}
      />
    </Card>
  )
}
