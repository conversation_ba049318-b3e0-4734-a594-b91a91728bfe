import { http } from './http'
import { Channels } from '@/common'
import type { AdjustRentPaymentData, CreateRentedPropertyData, GetRentedPropertiesParams, RecordRentPaymentData, UpdateRentedPropertyData } from '@/common/types'

export const createRentedProperty = async (data: CreateRentedPropertyData, userId: string) => {
    return await http.post(Channels.CREATE_RENTED_PROPERTY, {
        body: { data, userId }
    })
}

export const updateRentedProperty = async (id: string, data: UpdateRentedPropertyData) => {
    return await http.put(Channels.UPDATE_RENTED_PROPERTY, {
        params: { id },
        body: data
    })
}

export const terminateRental = async (propertyId: string, reason: string) => {
    return await http.put(Channels.TERMINATE_RENTAL, {
        body: { propertyId, reason }
    })
}

export const recordRentPayment = async (data: RecordRentPaymentData) => {
    return await http.post(Channels.RECORD_RENT_PAYMENT, {
        body: data
    })
}

export const getRentedProperty = async (id: string) => {
    return await http.get(Channels.GET_RENTED_PROPERTY, {
        params: { id }
    })
}

export const getRentedProperties = async (params: Partial<GetRentedPropertiesParams> = {}) => {
    const defaultParams: GetRentedPropertiesParams = {
        page: params.page ?? 1,
        limit: params.limit ?? 20,
        tenantId: params.tenantId,
        propertyId: params.propertyId,
        isActive: params.isActive,
        search: params.search
    }

    return await http.get(Channels.GET_RENTED_PROPERTIES, { query: defaultParams })
}



export const extendRental = async (id: string, newEndDate: Date) => {
    return await http.put(Channels.EXTEND_RENTAL, {
        params: { id },
        body: { newEndDate }
    })
}

export const adjustRent = async (id: string, newAmount: number) => {
    return await http.put(Channels.ADJUST_RENT, {
        params: { id },
        body: { newAmount }
    })
}

export const endRental = async (rentalId: string) => {
    return await http.post(Channels.END_RENTAL, {
        body: { rentalId }
    })
}


export const adjustRentPayment = async (data: AdjustRentPaymentData) => {
    return await http.post(Channels.ADJUST_RENT_PAYMENT, {
        body: data
    })
}

