import { useEffect, useState } from 'react'
import { App, But<PERSON>, Card, Form, Steps, Badge, Row, Col, Typography, Divider } from 'antd'
import { ArrowLeftOutlined, ArrowRightOutlined, SwapOutlined } from '@ant-design/icons'
import type { CreateStructuredExchangeData } from '@/common/types'
import { exchangeApi, accountsApi } from '@/renderer/services'
import { useAccountContext, useBankContext } from '@/renderer/contexts'
import { useSelector } from 'react-redux'
import { IRootState } from '@/renderer/redux'
import { formatCurrency } from '@/renderer/utils'
import { LocationBalanceResponse } from '@/renderer/services/exchange'
import { TransactionLocation } from '@prisma/client'
import { StepContent } from './StepContent'
import { AccountCard } from './AccountCard'
import { TransactionPreview } from './TransactionPreview'

interface ImprovedStructuredExchangeProps {
  onClose: () => void
  setRefreshTrigger: (trigger: any) => void
}

const { Title, Text } = Typography
const { Step } = Steps

// Transaction location types
type LocationType = 'CASH_VAULT' | 'BANK_ACCOUNT' | 'ACCOUNT' | 'EXTERNAL' | 'OTHER'

const LocationTypes = {
  CASH_VAULT: 'CASH_VAULT',
  BANK_ACCOUNT: 'BANK_ACCOUNT',
  ACCOUNT: 'ACCOUNT',
  EXTERNAL: 'EXTERNAL',
  OTHER: 'OTHER'
}

const CURRENCIES = ['PKR', 'USD', 'AED', 'AFN']

export const ImprovedStructuredExchange = ({
  onClose,
  setRefreshTrigger
}: ImprovedStructuredExchangeProps) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [showReverseRate, setShowReverseRate] = useState(false)
  const { message } = App.useApp()

  // Account and location details
  const [accountDetails, setAccountDetails] = useState<any>(null)
  const [accountLoading, setAccountLoading] = useState(false)
  const [inputLocationBalance, setInputLocationBalance] = useState<LocationBalanceResponse | null>(
    null
  )
  const [outputLocationBalance, setOutputLocationBalance] =
    useState<LocationBalanceResponse | null>(null)
  const [locationLoading, setLocationLoading] = useState(false)

  // Preview calculation
  const [previewData, setPreviewData] = useState<any>(null)

  // Current step in the process
  const [currentStep, setCurrentStep] = useState(0)

  const { accounts } = useAccountContext()
  const { banks } = useBankContext()

  const user = useSelector((state: IRootState) => state.user.data)

  // Watch form values for dynamic validation and preview
  const accountId = Form.useWatch('accountId', form)
  const inputCurrency = Form.useWatch('inputCurrency', form)
  const outputCurrency = Form.useWatch('outputCurrency', form)
  const inputLocation = Form.useWatch('inputLocation', form)
  const keepInAccount = Form.useWatch('keepInAccount', form)
  const outputLocation = Form.useWatch('outputLocation', form)
  const exchangeRate = Form.useWatch('exchangeRate', form)
  const inputAmount = Form.useWatch('inputAmount', form)
  const inputSource = Form.useWatch('inputSource', form)
  const inputBankId = Form.useWatch('inputBankId', form)
  const outputBankId = Form.useWatch('outputBankId', form)

  // Get available locations based on currency
  const getAvailableLocations = (currency: string) => {
    const pkrLocations = [
      { value: LocationTypes.CASH_VAULT, label: 'Cash Vault' },
      { value: LocationTypes.BANK_ACCOUNT, label: 'Bank Account' }
    ]

    const otherLocations = [{ value: LocationTypes.CASH_VAULT, label: 'Cash Vault' }]

    if (currency === 'PKR') {
      return pkrLocations
    }

    return otherLocations
  }

  // Fetch account details when account is selected
  useEffect(() => {
    if (accountId) {
      fetchAccountDetails(accountId)
    } else {
      setAccountDetails(null)
    }
  }, [accountId])

  // Reset dependent fields when currency changes
  useEffect(() => {
    if (inputCurrency) {
      form.setFieldsValue({
        inputLocation: undefined,
        inputBankId: undefined,
        inputSource: undefined
      })
      setInputLocationBalance(null)
    }
  }, [inputCurrency])

  useEffect(() => {
    if (outputCurrency) {
      form.setFieldsValue({
        outputLocation: undefined,
        outputBankId: undefined
      })
      setOutputLocationBalance(null)
    }
  }, [outputCurrency])

  // Fetch location balances when locations are selected
  useEffect(() => {
    if (inputLocation && inputCurrency) {
      fetchLocationBalance(
        inputLocation as TransactionLocation,
        inputCurrency,
        inputLocation === LocationTypes.BANK_ACCOUNT ? inputBankId : undefined
      )
    }
  }, [inputLocation, inputCurrency, inputBankId])

  useEffect(() => {
    if (outputLocation && outputCurrency) {
      fetchLocationBalance(
        outputLocation as TransactionLocation,
        outputCurrency,
        outputLocation === LocationTypes.BANK_ACCOUNT ? outputBankId : undefined,
        true
      )
    }
  }, [outputLocation, outputCurrency, outputBankId])

  // Calculate preview data when relevant fields change
  useEffect(() => {
    calculatePreview()
  }, [
    inputAmount,
    exchangeRate,
    inputCurrency,
    outputCurrency,
    inputSource,
    keepInAccount,
    accountDetails
  ])

  // Fetch account details
  const fetchAccountDetails = async (accountId: string) => {
    setAccountLoading(true)
    try {
      const response = await accountsApi.getAccountById(accountId)
      if (response.error.error || response.data.error) {
        message.error('Failed to fetch account details')
        return
      }
      setAccountDetails(response.data.data)
    } catch (error) {
      console.error('Error fetching account details:', error)
      message.error('Failed to fetch account details')
    } finally {
      setAccountLoading(false)
    }
  }

  // Fetch location balance
  const fetchLocationBalance = async (
    location: TransactionLocation,
    currency: string,
    bankId?: string,
    isOutput = false
  ) => {
    setLocationLoading(true)
    try {
      const response = await exchangeApi.getLocationBalance(location, currency, bankId)
      if (response.error.error || response.data.error) {
        message.error('Failed to fetch location balance')
        return
      }

      if (isOutput) {
        setOutputLocationBalance(response.data.data)
      } else {
        setInputLocationBalance(response.data.data)
      }
    } catch (error) {
      console.error('Error fetching location balance:', error)
      message.error('Failed to fetch location balance')
    } finally {
      setLocationLoading(false)
    }
  }

  // Calculate preview data
  const calculatePreview = () => {
    if (!inputAmount || !exchangeRate || !inputCurrency || !outputCurrency || !accountDetails) {
      setPreviewData(null)
      return
    }

    const outputAmount = Number((inputAmount * exchangeRate).toFixed(2))

    // Find current balances
    const inputCurrencyBalance =
      accountDetails.balances.find((b: any) => b.currency.code === inputCurrency)?.balance || 0

    const outputCurrencyBalance =
      accountDetails.balances.find((b: any) => b.currency.code === outputCurrency)?.balance || 0

    // Calculate new balances
    const newInputBalance =
      inputSource === 'ACCOUNT_BALANCE' ? inputCurrencyBalance - inputAmount : inputCurrencyBalance

    const newOutputBalance = keepInAccount
      ? outputCurrencyBalance + outputAmount
      : outputCurrencyBalance

    setPreviewData({
      inputAmount,
      outputAmount,
      inputCurrency,
      outputCurrency,
      accountBalances: {
        current: {
          [inputCurrency]: inputCurrencyBalance,
          [outputCurrency]: outputCurrencyBalance
        },
        new: {
          [inputCurrency]: newInputBalance,
          [outputCurrency]: newOutputBalance
        }
      },
      locationBalances: {
        input: inputLocationBalance?.balance,
        output: outputLocationBalance?.balance
      }
    })
  }

  // Update exchange rate when reverse rate changes
  const handleReverseRateChange = (value: number | null) => {
    if (!value) {
      form.setFieldValue('exchangeRate', null)
      return
    }
    const actualRate = Number(1 / value)
    form.setFieldValue('exchangeRate', actualRate)
  }

  // Handle form submission
  const handleSubmit = async (values: any) => {
    setLoading(true)

    // Calculate the output amount with proper precision
    const outputAmount = Number((values.inputAmount * values.exchangeRate).toFixed(2))

    // If using reverse rate, we need to pass both rates
    const data = {
      ...values,
      outputAmount,
      userId: user?.id,
      isReverseRate: showReverseRate,
      displayRate: showReverseRate ? 1 / values.exchangeRate : values.exchangeRate
    }

    const response = await exchangeApi.createStructuredExchange(data)
    setLoading(false)

    if (response.error.error || response.data.error) {
      message.error(response.error.message || response.data.error.message)
      return
    }

    setRefreshTrigger((prev) => prev + 1)
    message.success('Exchange created successfully')
    form.resetFields()
    onClose()
  }

  // Render summary sidebar
  const renderSummary = () => {
    return (
      <div className="h-full">
        <Card className="mb-4" title="Exchange Summary">
          <div className="mb-4">
            <Text strong>Current Step:</Text>{' '}
            {currentStep === 0
              ? 'Account Selection'
              : currentStep === 1
                ? 'Input Currency'
                : currentStep === 2
                  ? 'Exchange Details'
                  : currentStep === 3
                    ? 'Output Currency'
                    : 'Review'}
          </div>

          {accountDetails && (
            <div className="mb-4">
              <Text strong>Account:</Text> {accountDetails.name}
            </div>
          )}

          {inputCurrency && (
            <div className="mb-4">
              <Text strong>Input Currency:</Text> {inputCurrency}
              {inputAmount && (
                <div>
                  <Text strong>Amount:</Text> {formatCurrency(inputAmount, inputCurrency)}
                </div>
              )}
              {inputSource && (
                <div>
                  <Text strong>Source:</Text>{' '}
                  {inputSource === 'NEW_DEPOSIT' ? 'New Deposit' : 'Account Balance'}
                </div>
              )}
              {inputLocation && (
                <div>
                  <Text strong>Location:</Text> {inputLocation.replace('_', ' ')}
                </div>
              )}
            </div>
          )}

          {exchangeRate && inputCurrency && outputCurrency && (
            <div className="mb-4">
              <Text strong>Exchange Rate:</Text> 1 {inputCurrency} = {exchangeRate} {outputCurrency}
            </div>
          )}

          {outputCurrency && (
            <div className="mb-4">
              <Text strong>Output Currency:</Text> {outputCurrency}
              {keepInAccount !== undefined && (
                <div>
                  <Text strong>Keep in Account:</Text> {keepInAccount ? 'Yes' : 'No'}
                </div>
              )}
              {outputLocation && (
                <div>
                  <Text strong>Location:</Text> {outputLocation.replace('_', ' ')}
                </div>
              )}
            </div>
          )}

          {previewData && (
            <div>
              <Divider />
              <Text strong>Output Amount:</Text>{' '}
              <span className="text-green-600">
                {formatCurrency(previewData.outputAmount, outputCurrency)}
              </span>
            </div>
          )}
        </Card>

        {accountDetails && <AccountCard accountDetails={accountDetails} loading={accountLoading} />}

        {previewData && (
          <TransactionPreview previewData={previewData} exchangeRate={exchangeRate} />
        )}
      </div>
    )
  }

  // Navigation buttons for steps
  const renderStepActions = () => {
    return (
      <div className="mt-6 flex justify-between">
        {currentStep > 0 && (
          <Button onClick={() => setCurrentStep(currentStep - 1)}>
            <ArrowLeftOutlined /> Previous
          </Button>
        )}

        <div className="flex-grow"></div>

        {currentStep < 4 ? (
          <Button
            type="primary"
            onClick={() => setCurrentStep(currentStep + 1)}
            disabled={
              (currentStep === 0 && !accountId) ||
              (currentStep === 1 &&
                (!inputCurrency ||
                  !inputAmount ||
                  !inputSource ||
                  (inputSource === 'NEW_DEPOSIT' && !inputLocation) ||
                  (inputLocation === LocationTypes.BANK_ACCOUNT && !inputBankId))) ||
              (currentStep === 2 && !exchangeRate) ||
              (currentStep === 3 &&
                (!outputCurrency ||
                  keepInAccount === undefined ||
                  (keepInAccount === false && !outputLocation) ||
                  (outputLocation === LocationTypes.BANK_ACCOUNT && !outputBankId)))
            }
          >
            Next <ArrowRightOutlined />
          </Button>
        ) : (
          <Button type="primary" onClick={() => form.submit()} loading={loading}>
            Create Exchange
          </Button>
        )}
      </div>
    )
  }

  return (
    <Card className="m-auto h-fit max-w-6xl shadow-sm">
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button icon={<ArrowLeftOutlined />} onClick={onClose} />
          <h2 className="text-xl font-semibold">New Exchange</h2>
        </div>
        <div>
          <Badge
            count={
              previewData?.outputAmount
                ? `${formatCurrency(previewData.outputAmount, outputCurrency)}`
                : '?'
            }
            style={{ backgroundColor: '#52c41a' }}
          />
        </div>
      </div>

      <Steps current={currentStep} className="mb-8">
        <Step title="Account" description="Select account" />
        <Step title="Input" description="Input currency" />
        <Step title="Exchange" description="Set rate" />
        <Step title="Output" description="Output currency" />
        <Step title="Review" description="Confirm details" />
      </Steps>

      <Row gutter={24}>
        <Col span={16}>
          <Form form={form} layout="vertical" onFinish={handleSubmit} requiredMark={false}>
            <StepContent
              currentStep={currentStep}
              form={form}
              accounts={accounts}
              banks={banks}
              accountDetails={accountDetails}
              accountLoading={accountLoading}
              inputLocationBalance={inputLocationBalance}
              outputLocationBalance={outputLocationBalance}
              locationLoading={locationLoading}
              previewData={previewData}
              exchangeRate={exchangeRate}
              showReverseRate={showReverseRate}
              setShowReverseRate={setShowReverseRate}
              handleReverseRateChange={handleReverseRateChange}
              getAvailableLocations={getAvailableLocations}
            />
            {renderStepActions()}
          </Form>
        </Col>
        <Col span={8}>{renderSummary()}</Col>
      </Row>
    </Card>
  )
}
