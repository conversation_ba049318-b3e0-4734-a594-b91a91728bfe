import { useState, useEffect } from 'react'
import { Card, Descriptions, Table, Tag, Spin } from 'antd'
import { cashVaultApi, bankAccountApi } from '@/renderer/services'
import { formatCurrency, formatCurrencyWithoutSymbol } from '@/renderer/utils'

interface LocationDetailsCardProps {
  subTitle?: string
  locationType: 'CASH_VAULT' | 'BANK_ACCOUNT'
  bankId?: string
  currencyCode?: string
}

export const LocationDetailsCard = ({
  subTitle = '',
  locationType,
  bankId,
  currencyCode
}: LocationDetailsCardProps) => {
  const [loading, setLoading] = useState(false)
  const [cashVaultData, setCashVaultData] = useState<any>(null)
  const [bankData, setBankData] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  // Fetch cash vault data
  useEffect(() => {
    const fetchCashVaultData = async () => {
      if (locationType !== 'CASH_VAULT') return

      setLoading(true)
      setError(null)
      try {
        if (currencyCode) {
          // Fetch specific currency balance
          const response = await cashVaultApi.getBalanceByCurrency(currencyCode)
          if (response.error?.error || response.data?.error) {
            const errorMsg =
              response.error?.message ||
              response.data?.error?.message ||
              'Error fetching cash vault balance'
            console.error(errorMsg)
            setError(errorMsg)
            return
          }

          if (response.data?.data) {
            setCashVaultData({
              balances: [
                {
                  currency: { code: response.data.data.currency || currencyCode },
                  balance: response.data.data.balance || 0
                }
              ]
            })
          }
        } else {
          // Fetch all balances
          const response = await cashVaultApi.getBalances()
          if (response.error?.error || response.data?.error) {
            const errorMsg =
              response.error?.message ||
              response.data?.error?.message ||
              'Error fetching cash vault balances'
            console.error(errorMsg)
            setError(errorMsg)
            return
          }

          if (response.data?.data) {
            // Transform the data to match the expected format
            const data = response.data.data
            const balances = [
              { currency: { code: 'PKR' }, balance: data.pkrBalance || 0 },
              { currency: { code: 'USD' }, balance: data.usdBalance || 0 },
              { currency: { code: 'AED' }, balance: data.aedBalance || 0 },
              { currency: { code: 'AFN' }, balance: data.afnBalance || 0 }
            ]

            setCashVaultData({
              balances,
              lastUpdated: data.lastUpdated
            })
          }
        }
      } catch (error) {
        console.error('Error fetching cash vault data:', error)
        setError('Failed to fetch cash vault data')
      } finally {
        setLoading(false)
      }
    }

    fetchCashVaultData()
  }, [locationType, currencyCode])

  // Fetch bank account data
  useEffect(() => {
    const fetchBankData = async () => {
      if (locationType !== 'BANK_ACCOUNT' || !bankId) return

      setLoading(true)
      setError(null)
      try {
        const response = await bankAccountApi.getBankAccountById(bankId)
        if (response.error?.error || response.data?.error) {
          const errorMsg =
            response.error?.message ||
            response.data?.error?.message ||
            'Error fetching bank account'
          console.error(errorMsg)
          setError(errorMsg)
          return
        }

        if (response.data?.data) {
          // Transform the data to match the expected format
          const bankAccount = response.data.data
          setBankData({
            ...bankAccount,
            balances: [{ currency: { code: 'PKR' }, balance: bankAccount.balance || 0 }]
          })
        }
      } catch (error) {
        console.error('Error fetching bank account data:', error)
        setError('Failed to fetch bank account data')
      } finally {
        setLoading(false)
      }
    }

    fetchBankData()
  }, [locationType, bankId])

  if (loading) {
    return (
      <Card title={`Location Details - ${subTitle}`} className="mb-4 shadow-lg">
        <div className="flex h-40 items-center justify-center">
          <Spin />
        </div>
      </Card>
    )
  }

  if (error) {
    return (
      <Card title={`Location Details - ${subTitle}`} className="mb-4 shadow-lg">
        <div className="flex h-40 items-center justify-center text-red-500">{error}</div>
      </Card>
    )
  }

  // Render cash vault details
  if (locationType === 'CASH_VAULT' && cashVaultData?.balances) {
    const balanceColumns = [
      {
        title: 'Currency',
        dataIndex: ['currency', 'code'],
        key: 'currency'
      },
      {
        title: 'Balance',
        dataIndex: 'balance',
        key: 'balance',
        render: (balance: number) => {
          const color = balance > 0 ? 'green' : balance < 0 ? 'red' : 'default'
          return <Tag color={color}>{formatCurrencyWithoutSymbol(balance || 0)}</Tag>
        }
      }
    ]

    return (
      <Card title={`Cash Vault Details - ${subTitle}`} className="mb-4 shadow-lg">
        {cashVaultData.lastUpdated && (
          <Descriptions column={1} className="mb-4">
            <Descriptions.Item label="Last Updated">
              {new Date(cashVaultData.lastUpdated).toLocaleString()}
            </Descriptions.Item>
          </Descriptions>
        )}

        <div>
          <h4 className="mb-2 font-medium">Cash Vault Balances</h4>
          <Table
            dataSource={cashVaultData.balances}
            columns={balanceColumns}
            pagination={false}
            size="small"
            rowKey={(record: any) => record.currency?.code || 'unknown'}
          />
        </div>
      </Card>
    )
  }

  // Render bank account details
  if (locationType === 'BANK_ACCOUNT' && bankData?.balances) {
    const balanceColumns = [
      {
        title: 'Currency',
        dataIndex: ['currency', 'code'],
        key: 'currency'
      },
      {
        title: 'Balance',
        dataIndex: 'balance',
        key: 'balance',
        render: (balance: number) => {
          const color = balance > 0 ? 'green' : balance < 0 ? 'red' : 'default'
          return <Tag color={color}>{formatCurrencyWithoutSymbol(balance || 0)}</Tag>
        }
      }
    ]

    return (
      <Card title={`Bank Account Details - ${subTitle}`} className="mb-4 shadow-lg">
        <Descriptions column={2} className="mb-4">
          <Descriptions.Item label="Bank Name">{bankData.bankName || 'N/A'}</Descriptions.Item>
          <Descriptions.Item label="Account Number">
            {bankData.accountNumber || 'N/A'}
          </Descriptions.Item>
          {bankData.branchCode && (
            <Descriptions.Item label="Branch Code">{bankData.branchCode}</Descriptions.Item>
          )}
          <Descriptions.Item label="Status">
            {bankData.isActive ? <Tag color="green">Active</Tag> : <Tag color="red">Inactive</Tag>}
          </Descriptions.Item>
        </Descriptions>

        <div>
          <h4 className="mb-2 font-medium">Account Balance</h4>
          <Table
            dataSource={bankData.balances}
            columns={balanceColumns}
            pagination={false}
            size="small"
            rowKey={(record: any) => record.currency?.code || 'unknown'}
          />
        </div>
      </Card>
    )
  }

  // If no data is available yet but we're not loading, show an empty card
  if (
    (locationType === 'CASH_VAULT' && !cashVaultData) ||
    (locationType === 'BANK_ACCOUNT' && !bankData)
  ) {
    return (
      <Card
        title={`${locationType === 'CASH_VAULT' ? 'Cash Vault' : 'Bank Account'} Details - ${subTitle}`}
        className="mb-4 shadow-lg"
      >
        <div className="flex h-40 items-center justify-center text-gray-500">
          {currencyCode ? `Select a currency to view details` : `No data available`}
        </div>
      </Card>
    )
  }

  return null
}
