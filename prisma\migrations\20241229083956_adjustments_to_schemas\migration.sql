/*
  Warnings:

  - The values [PARTNER] on the enum `AccountType` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `isWalkIn` on the `Account` table. All the data in the column will be lost.
  - Added the required column `address` to the `Account` table without a default value. This is not possible if the table is not empty.
  - Added the required column `phoneNumber` to the `Account` table without a default value. This is not possible if the table is not empty.
  - Added the required column `name` to the `User` table without a default value. This is not possible if the table is not empty.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "AccountType_new" AS ENUM ('CUSTOMER', 'TENANT', 'BOTH');
ALTER TABLE "Account" ALTER COLUMN "type" TYPE "AccountType_new" USING ("type"::text::"AccountType_new");
ALTER TYPE "AccountType" RENAME TO "AccountType_old";
ALTER TYPE "AccountType_new" RENAME TO "AccountType";
DROP TYPE "AccountType_old";
COMMIT;

-- AlterEnum
ALTER TYPE "TransactionLocation" ADD VALUE 'ACCOUNT';

-- AlterTable
ALTER TABLE "Account" DROP COLUMN "isWalkIn",
ADD COLUMN     "address" TEXT NOT NULL,
ADD COLUMN     "phoneNumber" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "isActive" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "name" TEXT NOT NULL;
